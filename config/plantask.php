<?php
// +----------------------------------------------------------------------
// | 计划任务设置
// +----------------------------------------------------------------------
return [
    //启动进程数量
    'processes' =>  8,
    //监听新任务间隔时间（s）
    'listen_task_interval'    =>  1,
    //执行任务间隔时间（s）
    'interval'  => 1,
    //任务分配模式，round（轮询：根据任务ID依次分配），random（随机：随机分配空闲进程）
    'load_balance'  =>  'round',
    // 开启后会输出详细错误日志，修改配置后重启服务生效
    'debug' => true,
    //redis配置
    'redis' => [
        'host'        => '127.0.0.1',
        'port'        => 6379,
        'password'    => '',
        'select'      => 0,
        'timeout'     => 0
    ]
];
