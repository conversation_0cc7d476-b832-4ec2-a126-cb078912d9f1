<?php

// +----------------------------------------------------------------------
// | 缓存设置
// +----------------------------------------------------------------------

return [
    // 默认缓存驱动
    'default' => env('cache.driver', 'redis'),

    // 缓存连接方式配置
    'stores'  => [
        'file' => [
            // 驱动方式
            'type'       => 'File',
            // 缓存保存目录
            'path'       => '',
            // 缓存前缀
            'prefix'     => '',
            // 缓存有效期 0表示永久缓存
            'expire'     => 0,
            // 缓存标签前缀
            'tag_prefix' => 'tag:',
            // 序列化机制 例如 ['serialize', 'unserialize']
            'serialize'  => [],
        ],
        'redis' => [
            // 驱动方式
            'type'       => 'redis',
            // Redis 服务器地址
            'host'       => env('cache.redis.host', '127.0.0.1'),
            // Redis 服务器端口
            'port'       => env('cache.redis.port', 6379),
            // Redis 密码（如果有的话）
            'password'   => env('cache.redis.password', ''),
            // Redis 数据库索引
            'select'     => env('cache.redis.select', 3),
            // 缓存前缀
            'prefix'     => env('cache.redis.prefix', 'cache:'),
            // 缓存有效期 0表示永久缓存
            'expire'     => 3600,
            // 缓存标签前缀
            'tag_prefix' => 'tag:',
            // 序列化机制 例如 ['serialize', 'unserialize']
            'serialize'  => [],
        ],
    ],
];
