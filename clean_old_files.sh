#!/bin/bash

# 设置日志文件路径
LOG_FILE="/delete_old_files.log"

# 获取今天的日期
TODAY=$(date +"%Y%m%d")
echo "========== 删除旧文件任务开始：$(date) ==========" | tee -a "$LOG_FILE"

# 删除 /www/server/data 下 mysql-bin.0 开头且不是今天的文件
echo "处理目录：/www/server/data" | tee -a "$LOG_FILE"
find /www/server/data -type f -name "mysql-bin.0*" ! -newermt "$TODAY" -print -delete | tee -a "$LOG_FILE"

# 删除 /www/wwwroot/Datagather.cm/public/sam_data 下不是今天的文件
echo "处理目录：/www/wwwroot/Datagather.cm/public/sam_data" | tee -a "$LOG_FILE"
find /www/wwwroot/Datagather.cm/public/sam_data -type f ! -newermt "$TODAY" -print -delete | tee -a "$LOG_FILE"

echo "========== 删除旧文件任务结束：$(date) ==========" | tee -a "$LOG_FILE"
