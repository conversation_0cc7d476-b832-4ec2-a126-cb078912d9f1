/**
 * backend common language package
 */
export default {
    Connection: 'connection',
    'Database connection': 'Database connection',
    'Database connection help': 'You can configure multiple database connections in config/database.php and select it here',
    layouts: {
        'Layout configuration': 'Layout configuration',
        'Layout mode': 'Layout mode',
        default: 'Default',
        classic: 'Classic',
        'Single column': 'Single Column',
        'overall situation': 'Global',
        'Background page switching animation': 'Background page switching animation',
        'Please select an animation name': 'Please select an animation name',
        sidebar: 'Sidebar',
        'Side menu bar background color': 'Background color of the side menu bar',
        'Side menu text color': 'Side menu text color',
        'Side menu active item background color': 'Background color of the side menu activation item',
        'Side menu active item text color': 'Side menu activation item text color',
        'Show side menu top bar (logo bar)': 'Display the top bar of the side menu (Logo Bar)',
        'Side menu top bar background color': 'Background color of the top bar of the side menu ',
        'Side menu width (when expanded)': 'Width of the side menu (Unfolding)',
        'Side menu default icon': 'Side menu default icon',
        'Side menu horizontal collapse': 'Side menu collapsed horizontally',
        'Side menu accordion': 'Side menu accordion',
        'Top bar': 'Top bar',
        'Top bar background color': 'Top bar background color',
        'Top bar text color': 'Top bar text color',
        'Background color when hovering over the top bar': 'Top bar hover background color',
        'Top bar menu active item background color': 'Background color of the top bar activation item',
        'Top bar menu active item text color': 'Top bar menu activation item text color',
        'Are you sure you want to restore all configurations to the default values?':
            'Are you sure to restore all configurations to the default values?',
        'Restore default': 'Restore default',
        'personal data': 'Personal data',
        cancellation: 'Cancellation',
        'Dark mode': 'Dark mode',
        'Exit full screen': 'Exit Full Screen',
        'Full screen is not supported': 'Your browser does not support full screen, please change another browser and try again~',
    },
    terminal: {
        Source: 'source',
        Terminal: 'Terminal',
        'Command run log': 'Command Run Log',
        'No mission yet': 'There is no task yet',
        'Test command': 'Test command',
        'Install dependent packages': 'Install dependent packages',
        Republish: 'Republish',
        'Clean up task list': 'Clean up the task list',
        unknown: 'Unknown',
        'Waiting for execution': 'Waiting for execution',
        Connecting: 'Connecting',
        Executing: 'Executing',
        'Successful execution': 'Executed successfully',
        'Execution failed': 'Failed to execute',
        'Unknown execution result': 'Execution result is unknown',
        'Are you sure you want to republish?': 'Are you sure to republish?',
        'Failure to execute this command will block the execution of the queue': 'Failed to execute this command will block queue execution.',
        'NPM package manager': 'NPM package manager',
        'NPM package manager tip': 'Select an available package manager for the execution of commands such as npm install in the WEB terminal',
        'Clear successful task': 'Clear successful task',
        'Clear successful task tip': 'When you start a new task, automatically clear the list of already successful tasks',
        'Manual execution': 'Manual execution',
        'Do not refresh the browser': 'Do not refresh your browser.',
        'Terminal settings': 'Terminal setup',
        'Back to terminal': 'Back to terminal',
        or: 'or',
        'Site domain name': 'Site domain name',
        'The current terminal is not running under the installation service, and some commands may not be executed':
            'The current terminal is not running under the installation service, and some commands may not be executed.',
        'Newly added tasks will never start because they are blocked by failed tasks':
            'Newly added tasks will never start because they are blocked by failed tasks!(Web terminal)',
        'Failed to modify the source command, Please try again manually': 'Failed to modify the source command. Please try again manually.',
    },
    vite: {
        Later: '稍后',
        'Restart hot update': '重启热更新',
        'Close type terminal': 'WEB Terminal server',
        'Close type crud': 'CRUD server',
        'Close type modules': 'module install server',
        'Close type config': 'system configuration server',
        'Reload hot server title': 'Need to restart Vite hot update service',
        'Reload hot server tips 1': 'To ensure that ',
        'Reload hot server tips 2':
            " is not interrupted, the system has temporarily suspended Vite's hot update function. During this period, changes to front-end files will not be updated in real-time and web pages will not be automatically reloaded. It has been detected that there are file updates during the service suspension period, and the hot update service needs to be restarted.",
        'Reload hot server tips 3':
            'The pause of hot updates does not affect the already loaded functions. You can continue to operate and click to restart the hot update service after everything is ready.',
    },
}
