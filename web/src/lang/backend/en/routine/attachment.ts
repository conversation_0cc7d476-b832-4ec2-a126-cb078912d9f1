export default {
    'Upload administrator': 'Upload administrator',
    'Upload user': 'Upload member',
    'Storage mode': 'Storage mode',
    'Physical path': 'Physical path',
    'image width': 'Picture width',
    'Picture height': 'Picture height',
    'file size': 'file size',
    'mime type': 'mime type ',
    'SHA1 code': 'SHA1',
    'The file is saved in the directory, and the file will not be automatically transferred if the record is modified':
        'The file had saved in the directory, and the modification record will not automatically tansfer the file.',
    'File saving path Modifying records will not automatically transfer files':
        'The file had saved in the path, and the modification record will not automatically tansfer the file.',
    'Width of picture file': 'The width of the image file.',
    'Height of picture file': 'The height of the image file.',
    'Original file name': 'Original name of the file',
    'File size (bytes)': 'File size (Bytes)',
    'File MIME type': 'File MIME type',
    'Upload (Reference) times of this file': 'Upload (Reference) times of this file',
    'When the same file is uploaded multiple times, only one attachment record will be saved and added':
        'When the same file is uploaded many times, only one attachment record will be saved and added.',
    'SHA1 encoding of file': 'The SHA1 encoding of file',
    'Files and records will be deleted at the same time Are you sure?': 'Files and records will be deleted at the same time Are you sure?',
}
