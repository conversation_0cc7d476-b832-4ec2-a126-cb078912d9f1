export default {
    task_id: 'task_id',
    type: 'type',
    task_name: 'task_name',
    'type method': 'type method',
    'type url': 'type url',
    class_method: 'class_method',
    url: 'url',
    params: 'params',
    repeat_times: 'repeat_times',
    interval: 'interval',
    run_time: 'run_time',
    status: 'status',
    'status 0': 'start',
    'status 1': 'queued',
    'status 2': 'In execution',
    'status 3': 'End of execution',
    'status 4': 'Execution error',
    'status 10': 'Task recovery',
    remark: 'remark',
    add_time: 'add_time',
    error_description: 'error_description',
    times: 'times',
    last_time: 'last_time',
    timer_id: 'timer_id',
    'quick Search Fields': 'task_id',
    weigh: 'weigh',
}
