export default {
    title: '标题',
    Icon: '图标',
    name: '名称',
    type: '类型',
    cache: '缓存',
    'Superior menu rule': '上级菜单规则',
    'Rule type': '规则类型',
    'type menu_dir': '菜单目录',
    'type menu': '菜单项',
    'type button': '页面按钮',
    'Rule title': '规则标题',
    'Rule name': '规则名称',
    'Routing path': '路由路径',
    'Rule Icon': '规则图标',
    'Menu type': '菜单类型',
    'Menu type tab': '选项卡',
    'Menu type link (offsite)': '链接(站外)',
    'Link address': '链接地址',
    'Component path': '组件路径',
    'Extended properties': '扩展属性',
    'Add as route only': '只添加为路由',
    'Add as menu only': '只添加为菜单',
    'Rule comments': '规则备注',
    'Rule weight': '规则权重',
    'Please enter the weight of menu rule (sort by)': '请输入菜单规则权重(排序依据)',
    'Please enter the correct URL': '请输入正确的Url',
    'The superior menu rule cannot be the rule itself': '上级菜单规则不能是规则本身',
    'It will be registered as the web side routing name and used as the server side API authentication':
        '将注册为web端路由名称，同时作为server端API验权使用',
    'Please enter the URL address of the link or iframe': '请输入链接或Iframe的URL地址',
    'English name, which does not need to start with `/admin`, such as auth/menu': '英文名称，无需以`/admin`开头，如:auth/menu',
    'Web side component path, please start with /src, such as: /src/views/backend/dashboard':
        'web端组件路径，请以/src开头，如:/src/views/backend/dashboard.vue',
    'The web side routing path (path) does not need to start with `/admin`, such as auth/menu':
        'web端路由路径(path)，无需以`/admin`开头，如:auth/menu',
    'Use in controller `get_ route_ Remark()` function, which can obtain the value of this field for your own use, such as the banner file of the console':
        '在控制器中使用`get_route_remark()`函数，可以获得此字段值自用，比如控制台的banner文案',
    'extend Title': '比如将`auth/menu`只添加为路由，那么可以另外将`auth/menu`、`auth/menu/:a`、`auth/menu/:b/:c`只添加为菜单',
    none: '无',
}
