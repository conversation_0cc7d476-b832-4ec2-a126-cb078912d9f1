export default {
    'stateTitle init': '模块安装器初始化...',
    'stateTitle download': '正在下载模块...',
    'stateTitle install': '正在安装模块...',
    'env require': '后端依赖（composer）',
    'env require-dev': '后端开发环境依赖（composer）',
    'env dependencies': '前端依赖（NPM）',
    'env devDependencies': '前端开发环境依赖（NPM）',
    'env nuxtDependencies': '前端依赖（Nuxt-NPM）',
    'env nuxtDevDependencies': '前端开发环境依赖（Nuxt-NPM）',
    // user
    'Member information': '会员信息',
    'My module': '我的模块',
    'Log in to the buildadmin module marketplace': '登录到 BuildAdmin 模块市场',
    'Please enter buildadmin account name or email': '请输入 BuildAdmin 账户名/邮箱/手机号',
    'Please enter the buildadmin account password': '请输入 BuildAdmin 账户密码',
    'Sign in': '登录',
    'Please enter the login verification code': '请输入登录验证码',
    Register: '没有账户？去注册',
    // buy
    'Module installation warning': '购买后一年内可免费下载和更新，虚拟产品不支持7天无理由退款',
    'Order title': '订单标题',
    'Order No': '订单编号',
    'Purchase user': '购买用户',
    'Order price': '订单价格',
    'Purchased, can be installed directly': '已购买，可直接安装',
    'Understand and agree': '理解并同意',
    'Module purchase and use agreement': '模块购买和使用协议',
    'Point payment': '积分支付',
    'Balance payment': '余额支付',
    'Wechat payment': '微信支付',
    'Alipay payment': '支付宝支付',
    'Install now': '立即安装',
    payment: '支付',
    'Confirm order info': '确认订单信息',
    // commonDone
    'Congratulations, module installation is complete': '恭喜，模块安装已完成。',
    'Module is disabled': '模块已禁用。',
    'Congratulations, the code of the module is ready': '恭喜，模块的代码已经准备好了。',
    'Unknown state': '未知状态。',
    'Do not refresh the page!': '请勿刷新页面！',
    'New adjustment of dependency detected': '检测到依赖项有新的调整',
    'This module adds new dependencies': '本模块添加了新的依赖项',
    'The built-in terminal of the system is automatically installing these dependencies, please wait~': '系统内置终端正在自动安装这些依赖，请稍等~',
    'View progress': '查看进度',
    'Dependency installation completed~': '依赖已安装完成~',
    'This module does not add new dependencies': '本模块没有添加新的依赖项。',
    'There is no adjustment for system dependency': '系统依赖无调整。',
    please: '请',
    'After installation 1': '在安装结束后',
    'Manually clean up the system and browser cache': '手动的清理系统和浏览器缓存。',
    'After installation 2': '安装结束后',
    'Automatically execute reissue command?': '自动执行重新发布命令？',
    'End of installation': '安装结束',
    'Dependency installation fail 1': '依赖安装失败，请点击',
    'Dependency installation fail 2': '终端',
    'Dependency installation fail 3': '中的重试按钮，您也可以查看',
    'Dependency installation fail 4': '手动完成未尽事宜',
    'Dependency installation fail 5': '在您',
    'Dependency installation fail 6': '确定依赖已准备好',
    'Dependency installation fail 7': '之前，模块还不能正常使用！',
    'Is the command that failed on the WEB terminal executed manually or in other ways successfully?':
        'WEB终端失败的命令已经手动或以其他方式执行成功？',
    yes: '是',
    no: '否',
    // confirmFileConflict
    'Update warning': '检测到以下的模块文件有更新，禁用时将自动覆盖，请注意备份。',
    'File conflict': '文件冲突',
    'Conflict file': '冲突文件',
    'Dependency conflict': '依赖冲突',
    'Confirm to disable the module': '确认禁用模块',
    'The module declares the added dependencies': '模块声明添加的依赖',
    Dependencies: '依赖项',
    retain: '保留',
    // goodsInfo
    'detailed information': '详细信息',
    Price: '价格',
    'Last updated': '最后更新',
    'Published on': '发布时间',
    'amount of downloads': '下载次数',
    'Module classification': '模块分类',
    'Developer Homepage': '开发者主页',
    'Click to access': '点击访问',
    'Module status': '模块状态',
    'View demo': '查看演示',
    'Code scanning Preview': '扫码预览',
    'Buy now': '立即购买',
    'continue installation': '继续安装',
    installed: '已安装',
    'to update': '更新',
    uninstall: '卸载',
    'Contact developer': '联系开发者',
    'Other works of developers': 'TA的其他作品',
    'There are no more works': '没有更多作品了',
    'You need to disable this module before updating Do you want to disable it now?': '更新前需要先禁用该模块，立即禁用？',
    'Disable and update': '禁用并更新',
    'No module purchase order was found within the expiration date': '没有找到在有效期以内的模块购买订单，是否立即购买当前模块？',
    // installConflict
    'new file': '新文件',
    'Existing files': '已有文件',
    'Treatment scheme': '处理方案',
    'Backup and overwrite existing files': '备份并覆盖已有文件',
    'Discard new file': '丢弃新文件',
    environment: '环境',
    'New dependency': '新依赖',
    'Existing dependencies': '已有依赖',
    'Overwrite existing dependencies': '覆盖已有依赖',
    'Do not use new dependencies': '不使用新依赖',
    // tableHeader
    'Upload zip package for installation': '上传ZIP包安装',
    'Upload installation': '上传安装',
    'Uploaded / installed modules': '已上传/安装的模块',
    'Local module': '本地模块',
    'Publishing module': '发布模块',
    'Get points': '获得积分',
    'Search is actually very simple': '搜索其实很简单',
    // tabs
    Loading: '加载中...',
    'No more': '没有更多了...',
    // uploadInstall
    'Local upload warning': '请您务必确认模块包文件来自官方渠道或经由官方认证的模块作者，否则系统可能被破坏，因为：',
    'The module can modify and add system files': '模块可以修改和新增系统文件',
    'The module can execute sql commands and codes': '模块可以执行sql命令和代码',
    'The module can install new front and rear dependencies': '模块可以安装新的前后端依赖',
    'Drag the module package file here': '拖拽模块包文件到此处或',
    'Click me to upload': '点击我上传',
    'Uploaded, installation is about to start, please wait': '已上传，即将开始安装，请稍等',
    'Update Log': '更新日志',
    'No detailed update log': '无详细更新日志',
    'Use WeChat to scan QR code for payment': '使用微信扫描二维码支付',
    'Use Alipay to scan QR code for payment': '使用支付宝扫描二维码支付',
    'Logout login': '注销登录',
    Integral: '积分',
    Balance: '余额',
    Password: '密码',
    'User name': '用户名',
    'Verification Code': '验证码',
    'dependency-installation-fail-tips': '若手动执行命令成功，可点击以上的 `确定依赖已准备好` 将模块修改为已安装状态。',
    'New version': '有新版本',
    Install: '安装',
    'Installation cancelled because module already exists!': '安装取消，因为模块已经存在！',
    'Installation cancelled because the directory required by the module is occupied!': '安装取消，因为模块所需目录被占用！',
    'Installation complete': '安装完成',
    'A conflict is found Please handle it manually': '发现冲突，请手动处理',
    'Wait for dependent installation': '等待依赖安装',
    'The operation succeeds Please clear the system cache and refresh the browser ~': '操作成功，请清理系统缓存并刷新浏览器~',
    'Deal with conflict': '处理冲突',
    Points: '积分',
    'Wait for installation': '等待安装',
    'Conflict pending': '冲突待处理',
    'Dependency to be installed': '依赖待安装',
    'Restart Vite hot server': '重启热更新服务',
    'Restart Vite hot server tips': '在完成服务重启之前，您还可以随时从顶栏右侧的按钮组中找到手动重启服务的按钮。',
    'Manual restart': '手动重启',
    'Restart Now': '立即重启',
}
