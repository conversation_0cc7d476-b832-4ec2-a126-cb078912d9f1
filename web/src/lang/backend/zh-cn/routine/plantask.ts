export default {
    task_id: 'ID',
    type: '任务类型',
    task_name: '任务名称',
    'type method': '执行类方法',
    'type url': '访问URL',
    'type sql': '执行SQL',
    class_method: '类方法',
    url: 'URL',
    sql: 'SQL',
    params: '参数',
    repeat_times: '执行次数',
    cycle: '执行周期',
    run_time: '下次执行时间',
    is_amend: '是否修正执行时间',
    'is_amend 0': '否',
    'is_amend 1': '是',
    status: '状态',
    'status 0': '执行中',
    'status 1': '执行中',
    'status 2': '执行中',
    'status 3': '执行结束',
    'status 4': '执行错误',
    'status 10': '执行中',
    remark: '任务备注',
    add_time: '创建时间',
    error_description: '错误描述',
    times: '已执行',
    last_time: '上次执行时间',
    timer_id: '定时器ID',
    'quick Search Fields': '任务名称',
    start: '启动',
    stop: '停止',
    weigh: '排序'
}
