<template>
    <div class="modern-login-container">
        <!-- 语言切换 -->
        <div class="language-switcher">
            <el-dropdown size="large" :hide-timeout="50" placement="bottom-end" :hide-on-click="true">
                <div class="language-btn">
                    <Icon name="fa fa-globe" size="20" />
                </div>
                <template #dropdown>
                    <el-dropdown-menu class="lang-dropdown">
                        <el-dropdown-item v-for="item in config.lang.langArray" :key="item.name" @click="editDefaultLang(item.name)">
                            {{ item.value }}
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>

        <!-- 背景装饰 - 极致美学 -->
        <div class="background-decoration">
            <!-- 动态网格 -->
            <div class="dynamic-grid"></div>

            <!-- 浮动几何体 -->
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
            </div>

            <!-- 光晕系统 -->
            <div class="gradient-orbs">
                <div class="orb orb-1"></div>
                <div class="orb orb-2"></div>
                <div class="orb orb-3"></div>
            </div>

            <!-- 粒子系统 -->
            <div class="particle-system">
                <div class="particle" v-for="n in 10" :key="n"></div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧品牌区域 -->
            <div class="brand-section">
                <div class="brand-content">
                    <div class="logo-area">
                        <div class="logo-icon">
                            <svg viewBox="0 0 100 100" width="60" height="60">
                                <defs>
                                    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#667eea"/>
                                        <stop offset="100%" style="stop-color:#764ba2"/>
                                    </linearGradient>
                                </defs>
                                <circle cx="50" cy="50" r="45" fill="url(#logoGradient)" opacity="0.1"/>
                                <circle cx="50" cy="50" r="35" fill="none" stroke="url(#logoGradient)" stroke-width="2"/>
                                <circle cx="50" cy="50" r="25" fill="none" stroke="url(#logoGradient)" stroke-width="2"/>
                                <circle cx="50" cy="50" r="15" fill="url(#logoGradient)"/>
                                <circle cx="50" cy="50" r="8" fill="white"/>
                            </svg>
                        </div>
                        <h1 class="brand-title">DataGather</h1>
                        <p class="brand-subtitle">数据采集中心</p>
                    </div>
                    <div class="brand-description">
                        <h2>专业的数据管理平台</h2>
                        <p>高效、安全、智能的数据采集与分析解决方案，为您的业务提供强大的数据支撑。</p>
                        <div class="feature-list">
                            <div class="feature-item">
                                <Icon name="fa fa-shield-alt" />
                                <span>企业级安全</span>
                            </div>
                            <div class="feature-item">
                                <Icon name="fa fa-chart-line" />
                                <span>实时分析</span>
                            </div>
                            <div class="feature-item">
                                <Icon name="fa fa-cloud" />
                                <span>云端部署</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧登录区域 -->
            <div class="login-section">
                <div class="login-card">
                    <div class="card-header">
                        <h3>管理员登录</h3>
                        <p>请输入您的凭据以访问管理后台</p>
                    </div>
                    <div class="card-body">
                        <el-form @keyup.enter="onSubmitPre()" ref="formRef" :rules="rules" :model="form" class="login-form">
                            <el-form-item prop="username" class="form-group">
                                <label class="form-label">账号</label>
                                <div class="input-wrapper">
                                    <Icon name="fa fa-user" class="input-icon" />
                                    <el-input
                                        ref="usernameRef"
                                        type="text"
                                        clearable
                                        v-model="form.username"
                                        :placeholder="t('login.Please enter an account')"
                                        class="modern-input"
                                        size="large"
                                    />
                                </div>
                            </el-form-item>
                            <el-form-item prop="password" class="form-group">
                                <label class="form-label">密码</label>
                                <div class="input-wrapper">
                                    <Icon name="fa fa-lock" class="input-icon" />
                                    <el-input
                                        ref="passwordRef"
                                        v-model="form.password"
                                        type="password"
                                        :placeholder="t('login.Please input a password')"
                                        show-password
                                        class="modern-input"
                                        size="large"
                                    />
                                </div>
                            </el-form-item>
                            <div class="form-options">
                                <el-checkbox v-model="form.keep" class="remember-checkbox">
                                    {{ t('login.Hold session') }}
                                </el-checkbox>
                            </div>
                            <el-form-item class="submit-group">
                                <el-button
                                    :loading="state.submitLoading"
                                    class="login-submit-btn"
                                    type="primary"
                                    size="large"
                                    @click="onSubmitPre()"
                                >
                                    <span v-if="!state.submitLoading">{{ t('login.Sign in') }}</span>
                                    <span v-else>登录中...</span>
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div class="card-footer">
                        <p class="footer-text">
                            © 2025 DataGather.cm - 专业数据采集平台
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 保留原有气泡效果但隐藏 -->
        <div @contextmenu.stop="" id="bubble" class="bubble" style="display: none;">
            <canvas id="bubble-canvas" class="bubble-canvas"></canvas>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, reactive, ref, nextTick } from 'vue'
import * as pageBubble from '/@/utils/pageBubble'
import type { FormInstance, InputInstance } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { editDefaultLang } from '/@/lang/index'
import { useConfig } from '/@/stores/config'
import { useAdminInfo } from '/@/stores/adminInfo'
import { login } from '/@/api/backend'
import { uuid } from '/@/utils/random'
import { buildValidatorData } from '/@/utils/validate'
import router from '/@/router'
import clickCaptcha from '/@/components/clickCaptcha'
import toggleDark from '/@/utils/useDark'
import { fullUrl } from '/@/utils/common'
import { adminBaseRoutePath } from '/@/router/static/adminBase'

// 导入美化样式
import '/@/assets/DataCenterHome/login-style.css'

let timer: number

const config = useConfig()
const adminInfo = useAdminInfo()
toggleDark(config.layout.isDark)

const formRef = ref<FormInstance>()
const usernameRef = ref<InputInstance>()
const passwordRef = ref<InputInstance>()
const state = reactive({
    showCaptcha: false,
    submitLoading: false,
})
const form = reactive({
    username: '',
    password: '',
    keep: false,
    captchaId: uuid(),
    captchaInfo: '',
})

const { t } = useI18n()

// 表单验证规则
const rules = reactive({
    username: [buildValidatorData({ name: 'required', message: t('login.Please enter an account') }), buildValidatorData({ name: 'account' })],
    password: [buildValidatorData({ name: 'required', message: t('login.Please input a password') }), buildValidatorData({ name: 'password' })],
})

const focusInput = () => {
    if (form.username === '') {
        usernameRef.value!.focus()
    } else if (form.password === '') {
        passwordRef.value!.focus()
    }
}

onMounted(() => {
    timer = window.setTimeout(() => {
        pageBubble.init()
    }, 1000)

    login('get')
        .then((res) => {
            state.showCaptcha = res.data.captcha
            nextTick(() => focusInput())
        })
        .catch((err) => {
            console.log(err)
        })
})

onBeforeUnmount(() => {
    clearTimeout(timer)
    pageBubble.removeListeners()
})

const onSubmitPre = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            if (state.showCaptcha) {
                clickCaptcha(form.captchaId, (captchaInfo: string) => onSubmit(captchaInfo))
            } else {
                onSubmit()
            }
        }
    })
}

const onSubmit = (captchaInfo = '') => {
    state.submitLoading = true
    form.captchaInfo = captchaInfo
    login('post', form)
        .then((res) => {
            adminInfo.dataFill(res.data.userInfo)
            router.push({ path: adminBaseRoutePath })
        })
        .finally(() => {
            state.submitLoading = false
        })
}
</script>

<style scoped lang="scss">
/* 现代化登录页面样式 - 仅保留必要的Vue特定样式 */

/* 隐藏原有气泡效果 */
.bubble {
    display: none;
}

/* 确保表单元素正确显示 */
.login-form :deep(.el-form-item__label) {
    display: none; /* 隐藏Element Plus默认标签，使用自定义标签 */
}

.login-form :deep(.el-form-item__content) {
    line-height: normal;
    width: 100%;
    display: flex;
    justify-content: center;
}

.login-form :deep(.el-form-item) {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 28px;
}

.login-form :deep(.el-form-item__error) {
    color: #e74c3c;
    font-size: 0.85rem;
    margin-top: 5px;
    width: 90%;
    text-align: left;
    align-self: center;
    padding-left: 0;
}

/* 输入框图标定位 */
.input-wrapper {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    pointer-events: none;
}

/* 确保输入框内边距正确 */
.modern-input :deep(.el-input__wrapper) {
    padding-left: 56px !important;
}

/* 密码显示按钮样式调整 */
.modern-input :deep(.el-input__suffix) {
    right: 15px;
}

.modern-input :deep(.el-input__suffix-inner) {
    color: #6c757d;
}

/* 加载状态按钮样式 */
.login-submit-btn.is-loading {
    pointer-events: none;
}

.login-submit-btn :deep(.el-loading-spinner) {
    margin-top: -10px;
}

/* 复选框样式重置 */
.remember-checkbox :deep(.el-checkbox__input) {
    line-height: 1;
}

/* 表单选项容器 */
.form-options {
    width: 100%;
    display: flex;
    justify-content: center;
}

.form-options > * {
    width: 90%;
}

/* 确保下拉菜单样式正确 */
.lang-dropdown :deep(.el-dropdown-menu) {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: none;
    padding: 8px 0;
}

.lang-dropdown :deep(.el-dropdown-menu__item) {
    padding: 10px 20px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .login-form :deep(.el-form-item__error) {
        width: 85%;
    }
}

@media screen and (max-width: 480px) {
    .login-form :deep(.el-form-item__error) {
        width: 95%;
        font-size: 0.8rem;
    }
}

/* 暗黑模式支持 */
@at-root .dark {
    .modern-login-container {
        /* 暗黑模式下的调整 */
    }
}
</style>
