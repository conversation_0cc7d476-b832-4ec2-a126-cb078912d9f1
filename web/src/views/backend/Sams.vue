<template>
    <div class="sams-dashboard">
        <!-- 美化后的头部横幅 -->
        <div class="sams-header-banner">
            <div class="banner-background">
                <div class="banner-pattern"></div>
                <div class="banner-glow"></div>
            </div>
            <el-row :gutter="20" class="banner-content">
                <el-col :span="24" :lg="16">
                    <div class="welcome-section">
                        <div class="sams-brand-area">
                            <div class="brand-logo-container">
                                <div class="logo-circle">
                                    <Icon name="fa fa-shopping-cart" size="32" color="#ffffff" />
                                </div>
                                <div class="logo-glow"></div>
                            </div>
                            <div class="brand-info">
                                <h1 class="brand-title">山姆会员商店</h1>
                                <p class="brand-subtitle">Sam's Club Data Center</p>
                            </div>
                        </div>
                        <div class="welcome-content">
                            <div class="welcome-greeting">{{ adminInfo.nickname }}，{{ getGreet() }}</div>
                            <div class="welcome-message">{{ state.remark }}</div>
                            <div class="quick-actions">
                                <el-button
                                    type="primary"
                                    size="default"
                                    class="action-btn export-btn"
                                    @click="navigateToExport"
                                >
                                    <Icon name="fa fa-download" size="14" />
                                    <span>商品导出</span>
                                </el-button>
                                <el-button
                                    type="success"
                                    size="default"
                                    class="action-btn query-btn"
                                    @click="navigateToQuery"
                                >
                                    <Icon name="fa fa-search" size="14" />
                                    <span>商品查询</span>
                                </el-button>
                            </div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="24" :lg="8" class="sync-status-col">
                    <div class="sync-status-card">
                        <div class="status-header">
                            <div class="status-icon">
                                <Icon name="fa fa-refresh" size="20" color="#0066cc" />
                            </div>
                            <span class="status-title">数据同步状态</span>
                        </div>
                        <div class="status-content">
                            <div class="sync-time">{{ state.lastSyncTime }}</div>
                            <div class="sync-badge" :class="state.syncStatus">
                                <span class="badge-dot"></span>
                                {{ state.syncStatusText }}
                            </div>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>
        <!-- 美化后的统计面板 -->
        <div class="stats-panel-container">
            <el-row :gutter="24">
                <el-col :span="24" :sm="12" :lg="6" >
                    <div class="stats-card stores-card" >
                        <div class="card-background">
                            <div class="card-pattern"></div>
                        </div>
                        <div class="card-header">
                            <div class="icon-container stores-icon">
                                <Icon name="fa fa-building" size="24" color="#ffffff" />
                            </div>
                            <div class="card-info">
                                <div class="card-title">门店总数</div>
                                <div class="card-subtitle">Total Stores</div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="stats-number">
                                <el-statistic :value="storeCountOutput" :value-style="storeStatisticStyle" />
                                <span class="stats-unit">家</span>
                            </div>
                            <div class="stats-trend" :class="state.storeChange.type">
                                <Icon :name="getTrendIcon(state.storeChange.type)" size="12" />
                                <span>{{ state.storeChange.text }}</span>
                            </div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="24" :sm="12" :lg="6">
                    <div class="stats-card goods-card">
                        <div class="card-background">
                            <div class="card-pattern"></div>
                        </div>
                        <div class="card-header">
                            <div class="icon-container goods-icon">
                                <Icon name="fa fa-cube" size="24" color="#ffffff" />
                            </div>
                            <div class="card-info">
                                <div class="card-title">商品总数</div>
                                <div class="card-subtitle">Total Products</div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="stats-number">
                                <el-statistic :value="goodsCountOutput" :value-style="goodsStatisticStyle" />
                                <span class="stats-unit">个</span>
                            </div>
                            <div class="stats-trend" :class="state.goodsChange.type">
                                <Icon :name="getTrendIcon(state.goodsChange.type)" size="12" />
                                <span>{{ state.goodsChange.text }}</span>
                            </div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="24" :sm="12" :lg="6">
                    <div class="stats-card categories-card">
                        <div class="card-background">
                            <div class="card-pattern"></div>
                        </div>
                        <div class="card-header">
                            <div class="icon-container categories-icon">
                                <Icon name="fa fa-tags" size="24" color="#ffffff" />
                            </div>
                            <div class="card-info">
                                <div class="card-title">商品分类</div>
                                <div class="card-subtitle">Categories</div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="stats-number">
                                <el-statistic :value="categoriesCountOutput" :value-style="categoriesStatisticStyle" />
                                <span class="stats-unit">类</span>
                            </div>
                            <div class="stats-trend" :class="state.categoriesChange.type">
                                <Icon :name="getTrendIcon(state.categoriesChange.type)" size="12" />
                                <span>{{ state.categoriesChange.text }}</span>
                            </div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="24" :sm="12" :lg="6">
                    <div class="stats-card sync-card">
                        <div class="card-background">
                            <div class="card-pattern"></div>
                        </div>
                        <div class="card-header">
                            <div class="icon-container sync-icon">
                                <Icon name="fa fa-refresh" size="24" color="#ffffff" />
                            </div>
                            <div class="card-info">
                                <div class="card-title">今日同步</div>
                                <div class="card-subtitle">Today Sync</div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="stats-number">
                                <el-statistic :value="syncCountOutput" :value-style="syncStatisticStyle" />
                                <span class="stats-unit">次</span>
                            </div>
                            <div class="stats-trend" :class="state.syncChange.type">
                                <Icon :name="getTrendIcon(state.syncChange.type)" size="12" />
                                <span>{{ state.syncChange.text }}</span>
                            </div>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>
        <!-- 美化后的图表区域 -->
        <div class="charts-container">
            <el-row :gutter="24">
                <el-col :span="24" :md="12" :lg="8" class="chart-col">
                    <div class="chart-card trend-card">
                        <div class="chart-header">
                            <div class="header-icon">
                                <Icon name="fa fa-line-chart" size="18" color="#0066cc" />
                            </div>
                            <div class="header-content">
                                <h3 class="chart-title">商品数量趋势</h3>
                                <p class="chart-subtitle">Product Trend</p>
                            </div>
                        </div>
                        <div class="chart-body">
                            <div class="goods-trend-chart" :ref="chartRefs.set"></div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="24" :md="12" :lg="8" class="chart-col">
                    <div class="chart-card distribution-card">
                        <div class="chart-header">
                            <div class="header-icon">
                                <Icon name="fa fa-bar-chart" size="18" color="#00a854" />
                            </div>
                            <div class="header-content">
                                <h3 class="chart-title">门店商品分布</h3>
                                <p class="chart-subtitle">Store Distribution</p>
                            </div>
                        </div>
                        <div class="chart-body">
                            <div class="store-distribution-chart" :ref="chartRefs.set"></div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="24" :lg="8" class="activity-col">
                    <div class="activity-card">
                        <div class="activity-header">
                            <div class="header-icon">
                                <Icon name="fa fa-clock-o" size="18" color="#f5a623" />
                            </div>
                            <div class="header-content">
                                <h3 class="activity-title">最近活动</h3>
                                <p class="activity-subtitle">Recent Activities</p>
                            </div>
                        </div>
                        <div class="activity-body">
                            <el-scrollbar class="activity-scrollbar">
                                <div class="activity-list">
                                    <div v-for="(activity, index) in state.activities" :key="index" class="activity-item">
                                        <div class="activity-icon-wrapper" :class="activity.type">
                                            <Icon :name="getActivityIcon(activity.type)" size="14" />
                                        </div>
                                        <div class="activity-details">
                                            <div class="activity-title">{{ activity.title }}</div>
                                            <div class="activity-desc">{{ activity.description }}</div>
                                            <div class="activity-time">{{ activity.time }}</div>
                                        </div>
                                    </div>
                                    <div v-if="state.activities.length === 0" class="activity-empty">
                                        <div class="empty-icon">
                                            <Icon name="fa fa-clock-o" size="24" color="#ccc" />
                                        </div>
                                        <div class="empty-text">暂无最近活动</div>
                                    </div>
                                </div>
                            </el-scrollbar>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>

        <!-- 美化后的分析图表区域 -->
        <div class="analysis-container">
            <el-row :gutter="24">
                <el-col :span="24" :lg="12">
                    <div class="chart-card category-card">
                        <div class="chart-header">
                            <div class="header-icon">
                                <Icon name="fa fa-pie-chart" size="18" color="#722ed1" />
                            </div>
                            <div class="header-content">
                                <h3 class="chart-title">商品分类占比</h3>
                                <p class="chart-subtitle">Category Distribution</p>
                            </div>
                            <div class="chart-actions">
                                <el-tooltip content="查看详情" placement="top">
                                    <Icon name="fa fa-info-circle" size="16" color="#999" />
                                </el-tooltip>
                            </div>
                        </div>
                        <div class="chart-body">
                            <div class="category-distribution-chart" :ref="chartRefs.set"></div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="24" :lg="12">
                    <div class="chart-card region-card">
                        <div class="chart-header">
                            <div class="header-icon">
                                <Icon name="fa fa-map-marker" size="18" color="#eb2f96" />
                            </div>
                            <div class="header-content">
                                <h3 class="chart-title">门店地区分布</h3>
                                <p class="chart-subtitle">Regional Distribution</p>
                            </div>
                            <div class="chart-actions">
                                <el-tooltip content="查看详情" placement="top">
                                    <Icon name="fa fa-info-circle" size="16" color="#999" />
                                </el-tooltip>
                            </div>
                        </div>
                        <div class="chart-body">
                            <div class="store-region-chart" :ref="chartRefs.set"></div>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useEventListener, useTemplateRefsList, useTransition } from '@vueuse/core'
import * as echarts from 'echarts'
import { CSSProperties, nextTick, onActivated, onBeforeMount, onMounted, onUnmounted, reactive, toRefs, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { index, goodsTrend, storeDistribution, categoryDistribution, storeRegion, recentActivities } from '/@/api/backend/sam/dashboard'
import { useAdminInfo } from '/@/stores/adminInfo'
import { useNavTabs } from '/@/stores/navTabs'
import { getGreet } from '/@/utils/common'
let workTimer: number

defineOptions({
    name: 'dashboard',
})

const d = new Date()
const { t } = useI18n()
const navTabs = useNavTabs()
const adminInfo = useAdminInfo()
const chartRefs = useTemplateRefsList<HTMLDivElement>()

const state: {
    charts: any[]
    remark: string
    lastSyncTime: string
    syncStatus: string
    syncStatusText: string
    storeChange: any
    goodsChange: any
    categoriesChange: any
    syncChange: any
    activities: any[]
} = reactive({
    charts: [],
    remark: '欢迎使用山姆会员商店数据统计系统',
    lastSyncTime: '2025-01-27 14:30:25',
    syncStatus: 'success',
    syncStatusText: '同步正常',
    storeChange: { value: 0, type: 'stable', text: '无变化' },
    goodsChange: { value: 0, type: 'stable', text: '无变化' },
    categoriesChange: { value: 0, type: 'stable', text: '无变化' },
    syncChange: { value: 0, type: 'stable', text: '无变化' },
    activities: [],
})

/**
 * 山姆会员商店统计数据
 */
const countUp = reactive({
    storeCount: 0,
    goodsCount: 0,
    categoriesCount: 0,
    syncCount: 0,
})

const countUpRefs = toRefs(countUp)
const storeCountOutput = useTransition(countUpRefs.storeCount, { duration: 1500 })
const goodsCountOutput = useTransition(countUpRefs.goodsCount, { duration: 1500 })
const categoriesCountOutput = useTransition(countUpRefs.categoriesCount, { duration: 1500 })
const syncCountOutput = useTransition(countUpRefs.syncCount, { duration: 1500 })
const statisticValueStyle: CSSProperties = {
    fontSize: '28px',
}

// 美化后的统计样式
const storeStatisticStyle: CSSProperties = {
    fontSize: '32px',
    fontWeight: 'bold',
    color: '#0066cc'
}

const goodsStatisticStyle: CSSProperties = {
    fontSize: '32px',
    fontWeight: 'bold',
    color: '#00a854'
}

const categoriesStatisticStyle: CSSProperties = {
    fontSize: '32px',
    fontWeight: 'bold',
    color: '#f5a623'
}

const syncStatisticStyle: CSSProperties = {
    fontSize: '32px',
    fontWeight: 'bold',
    color: '#722ed1'
}

// 初始化山姆数据
const initSamsData = async () => {
    try {
        const res = await index()
        state.remark = res.data.remark
        state.lastSyncTime = res.data.lastSyncTime
        state.syncStatus = res.data.syncStatus
        state.syncStatusText = res.data.syncStatusText

        // 更新统计数据
        if (res.data.stats) {
            countUpRefs.storeCount.value = res.data.stats.storeCount
            countUpRefs.goodsCount.value = res.data.stats.goodsCount
            countUpRefs.categoriesCount.value = res.data.stats.categoriesCount
            countUpRefs.syncCount.value = res.data.stats.syncCount

            // 更新变化数据
            state.storeChange = res.data.stats.storeChange || { value: 0, type: 'stable', text: '无变化' }
            state.goodsChange = res.data.stats.goodsChange || { value: 0, type: 'stable', text: '无变化' }
            state.categoriesChange = res.data.stats.categoriesChange || { value: 0, type: 'stable', text: '无变化' }
            state.syncChange = res.data.stats.syncChange || { value: 0, type: 'stable', text: '无变化' }
        }
    } catch (error) {
        console.error('获取统计数据失败:', error)
        // 使用默认数据
        state.remark = '欢迎使用山姆会员商店数据统计系统'
        state.lastSyncTime = new Date().toLocaleString('zh-CN')
        state.syncStatus = 'success'
        state.syncStatusText = '同步正常'
        initCountUp()
    }
}

const initCountUp = () => {
    // 默认统计数据
    countUpRefs.storeCount.value = 42
    countUpRefs.goodsCount.value = 15678
    countUpRefs.categoriesCount.value = 128
    countUpRefs.syncCount.value = 24
}

// 根据变化类型获取对应的图标
const getTrendIcon = (type: string) => {
    switch (type) {
        case 'increase':
            return 'fa fa-arrow-up'
        case 'decrease':
            return 'fa fa-arrow-down'
        case 'stable':
        default:
            return 'fa fa-minus'
    }
}

// 根据活动类型获取对应的图标
const getActivityIcon = (type: string) => {
    switch (type) {
        case 'success':
            return 'fa fa-plus'
        case 'info':
            return 'fa fa-refresh'
        case 'warning':
            return 'fa fa-edit'
        case 'error':
            return 'fa fa-exclamation-triangle'
        default:
            return 'fa fa-info-circle'
    }
}

// 初始化活动数据
const initActivitiesData = async () => {
    try {
        const result = await recentActivities()
        if (result.code === 1 && result.data) {
            state.activities = result.data
        }
    } catch (error) {
        console.error('获取最近活动数据失败:', error)
        // 使用默认数据
        state.activities = [
            {
                type: 'success',
                title: '新增商品',
                description: '系统新增商品数据',
                time: '刚刚'
            },
            {
                type: 'info',
                title: '数据同步',
                description: '门店数据同步完成',
                time: '5分钟前'
            }
        ]
    }
}

// 跳转到商品导出页面
const navigateToExport = () => {
    window.open('/sams_goods_export.html', '_blank')
}

// 跳转到商品查询页面
const navigateToQuery = () => {
    window.open('/sams_goods_query.html', '_blank')
}

const initGoodsTrendChart = async () => {
    const goodsTrendChart = echarts.init(chartRefs.value[0] as HTMLElement)

    try {
        // 从API获取趋势数据
        const result = await goodsTrend()

        let months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
        let data = [12000, 13200, 14500, 15100, 15400, 15600, 15678]

        if (result.code === 1 && result.data) {
            months = result.data.months || months
            data = result.data.data || data
        }

        const option = {
            grid: {
                top: 20,
                right: 20,
                bottom: 40,
                left: 50,
            },
            xAxis: {
                type: 'category',
                data: months,
                axisLabel: {
                    fontSize: 12,
                    color: '#666',
                },
            },
            yAxis: {
                type: 'value',
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        opacity: 0.3,
                    },
                },
                axisLabel: {
                    fontSize: 12,
                    color: '#666',
                },
            },
            tooltip: {
                trigger: 'axis',
                formatter: '{b}: {c} 个商品'
            },
            series: [
                {
                    name: '商品数量',
                    data: data,
                    type: 'line',
                    symbol: 'circle',
                    symbolSize: 8,
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: 'rgba(0, 102, 204, 0.8)',
                            },
                            {
                                offset: 1,
                                color: 'rgba(0, 102, 204, 0.1)',
                            },
                        ]),
                    },
                    itemStyle: {
                        color: '#0066cc',
                    },
                    lineStyle: {
                        width: 3,
                        color: '#0066cc',
                    },
                },
            ],
        }
        goodsTrendChart.setOption(option)
    } catch (error) {
        console.error('获取商品趋势数据失败:', error)
        // 使用默认数据
        const option = {
            grid: { top: 20, right: 20, bottom: 40, left: 50 },
            xAxis: { type: 'category', data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'] },
            yAxis: { type: 'value' },
            series: [{ name: '商品数量', data: [12000, 13200, 14500, 15100, 15400, 15600, 15678], type: 'line' }]
        }
        goodsTrendChart.setOption(option)
    }

    state.charts.push(goodsTrendChart)
}

const initStoreDistributionChart = async () => {
    const storeDistributionChart = echarts.init(chartRefs.value[1] as HTMLElement)

    try {
        // 从API获取门店分布数据
        const result = await storeDistribution()

        let stores = ['深圳福田', '上海浦东', '北京朝阳', '广州天河', '杭州西湖', '成都锦江']
        let data = [3200, 2800, 3500, 2900, 2600, 2400]

        if (result.code === 1 && result.data) {
            stores = result.data.stores || stores
            data = result.data.data || data
        }

        const option = {
            grid: {
                top: 20,
                right: 20,
                bottom: 60,
                left: 50,
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: '{b}: {c} 个商品'
            },
            xAxis: {
                type: 'category',
                data: stores,
                axisLabel: {
                    fontSize: 12,
                    color: '#666',
                    rotate: 45
                },
            },
            yAxis: {
                type: 'value',
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        opacity: 0.3,
                    },
                },
                axisLabel: {
                    fontSize: 12,
                    color: '#666',
                },
            },
            series: [
                {
                    name: '商品数量',
                    data: data,
                    type: 'bar',
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: 'rgba(0, 168, 84, 0.8)',
                            },
                            {
                                offset: 1,
                                color: 'rgba(0, 168, 84, 0.3)',
                            },
                        ]),
                    },
                    barWidth: '60%',
                },
            ],
        }
        storeDistributionChart.setOption(option)
    } catch (error) {
        console.error('获取门店分布数据失败:', error)
        // 使用默认数据
        const option = {
            grid: { top: 20, right: 20, bottom: 60, left: 50 },
            xAxis: { type: 'category', data: ['深圳福田', '上海浦东', '北京朝阳', '广州天河', '杭州西湖', '成都锦江'] },
            yAxis: { type: 'value' },
            series: [{ name: '商品数量', data: [3200, 2800, 3500, 2900, 2600, 2400], type: 'bar' }]
        }
        storeDistributionChart.setOption(option)
    }

    state.charts.push(storeDistributionChart)
}

const initCategoryDistributionChart = async () => {
    const categoryDistributionChart = echarts.init(chartRefs.value[2] as HTMLElement)

    try {
        // 从API获取分类分布数据
        const result = await categoryDistribution()

        let pieData = [
            { value: 3200, name: '食品饮料', itemStyle: { color: '#722ed1' } },
            { value: 2800, name: '生鲜食品', itemStyle: { color: '#13c2c2' } },
            { value: 2400, name: '家居用品', itemStyle: { color: '#52c41a' } },
            { value: 1800, name: '个护美妆', itemStyle: { color: '#faad14' } },
            { value: 1600, name: '母婴用品', itemStyle: { color: '#f5222d' } },
            { value: 1200, name: '服装鞋帽', itemStyle: { color: '#1890ff' } },
            { value: 800, name: '数码家电', itemStyle: { color: '#eb2f96' } },
            { value: 600, name: '其他', itemStyle: { color: '#666666' } }
        ]

        if (result.code === 1 && result.data) {
            const colors = ['#722ed1', '#13c2c2', '#52c41a', '#faad14', '#f5222d', '#1890ff', '#eb2f96', '#666666']
            pieData = result.data.categories.map((name: string, index: number) => ({
                value: result.data.data[index],
                name: name,
                itemStyle: { color: colors[index % colors.length] }
            }))
        }

        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 'left',
                textStyle: {
                    color: '#666'
                }
            },
            series: [
                {
                    name: '商品分类',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['60%', '50%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: pieData
                }
            ]
        }
        categoryDistributionChart.setOption(option)
    } catch (error) {
        console.error('获取分类分布数据失败:', error)
        // 使用默认数据
        const option = {
            tooltip: { trigger: 'item' },
            legend: { orient: 'vertical', left: 'left' },
            series: [{
                name: '商品分类',
                type: 'pie',
                radius: ['40%', '70%'],
                data: [
                    { value: 3200, name: '食品饮料' },
                    { value: 2800, name: '生鲜食品' },
                    { value: 2400, name: '家居用品' }
                ]
            }]
        }
        categoryDistributionChart.setOption(option)
    }

    state.charts.push(categoryDistributionChart)
}

const initStoreRegionChart = async () => {
    const storeRegionChart = echarts.init(chartRefs.value[3] as HTMLElement)

    try {
        // 从API获取地区分布数据
        const result = await storeRegion()

        let pieData = [
            { value: 12, name: '华南地区', itemStyle: { color: '#eb2f96' } },
            { value: 10, name: '华东地区', itemStyle: { color: '#722ed1' } },
            { value: 8, name: '华北地区', itemStyle: { color: '#1890ff' } },
            { value: 6, name: '西南地区', itemStyle: { color: '#52c41a' } },
            { value: 4, name: '华中地区', itemStyle: { color: '#faad14' } },
            { value: 2, name: '西北地区', itemStyle: { color: '#f5222d' } }
        ]

        if (result.code === 1 && result.data) {
            const colors = ['#eb2f96', '#722ed1', '#1890ff', '#52c41a', '#faad14', '#f5222d']
            pieData = result.data.regions.map((name: string, index: number) => ({
                value: result.data.data[index],
                name: name,
                itemStyle: { color: colors[index % colors.length] }
            }))
        }

        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} 家门店 ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 'left',
                textStyle: {
                    color: '#666'
                }
            },
            series: [
                {
                    name: '门店分布',
                    type: 'pie',
                    radius: '50%',
                    center: ['60%', '50%'],
                    data: pieData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)',
                        },
                    },
                    label: {
                        show: true,
                        formatter: '{b}\n{c} 家'
                    }
                }
            ]
        }
        storeRegionChart.setOption(option)
    } catch (error) {
        console.error('获取地区分布数据失败:', error)
        // 使用默认数据
        const option = {
            tooltip: { trigger: 'item' },
            legend: { orient: 'vertical', left: 'left' },
            series: [{
                name: '门店分布',
                type: 'pie',
                radius: '50%',
                data: [
                    { value: 12, name: '华南地区' },
                    { value: 10, name: '华东地区' },
                    { value: 8, name: '华北地区' }
                ]
            }]
        }
        storeRegionChart.setOption(option)
    }

    state.charts.push(storeRegionChart)
}

const echartsResize = () => {
    nextTick(() => {
        for (const key in state.charts) {
            state.charts[key].resize()
        }
    })
}

// 山姆数据相关函数已在前面定义

// 删除工作状态相关函数

// 删除时间格式化函数

onActivated(() => {
    echartsResize()
})

onMounted(async () => {
    await initSamsData()
    await initActivitiesData()
    await initGoodsTrendChart()
    await initStoreDistributionChart()
    await initCategoryDistributionChart()
    await initStoreRegionChart()
    useEventListener(window, 'resize', echartsResize)
})

onBeforeMount(() => {
    for (const key in state.charts) {
        state.charts[key].dispose()
    }
})

onUnmounted(() => {
    // 清理图表资源
    for (const chart of state.charts) {
        chart.dispose()
    }
})

watch(
    () => navTabs.state.tabFullScreen,
    () => {
        echartsResize()
    }
)
</script>

<style scoped lang="scss">
.welcome {
    background: #e1eaf9;
    border-radius: 6px;
    display: flex;
    align-items: center;
    padding: 15px 20px !important;
    box-shadow: 0 0 30px 0 rgba(82, 63, 105, 0.05);
    .welcome-img {
        height: 100px;
        margin-right: 10px;
        user-select: none;
    }
    .welcome-title {
        font-size: 1.5rem;
        line-height: 30px;
        color: var(--ba-color-primary-light);
    }
    .welcome-note {
        padding-top: 6px;
        font-size: 15px;
        color: var(--el-text-color-primary);
    }
}
.working {
    height: 130px;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    height: 100%;
    position: relative;
    &:hover {
        .working-coffee {
            -webkit-transform: translateY(-4px) scale(1.02);
            -moz-transform: translateY(-4px) scale(1.02);
            -ms-transform: translateY(-4px) scale(1.02);
            -o-transform: translateY(-4px) scale(1.02);
            transform: translateY(-4px) scale(1.02);
            z-index: 999;
        }
    }
    .working-coffee {
        transition: all 0.3s ease;
        width: 80px;
    }
    .working-text {
        display: block;
        width: 100%;
        font-size: 15px;
        text-align: center;
        color: var(--el-text-color-primary);
    }
    .working-opt {
        position: absolute;
        top: -40px;
        right: 10px;
        background-color: rgba($color: #000000, $alpha: 0.3);
        padding: 10px 20px;
        border-radius: 20px;
        color: var(--ba-bg-color-overlay);
        transition: all 0.3s ease;
        cursor: pointer;
        opacity: 0;
        z-index: 999;
        &:active {
            background-color: rgba($color: #000000, $alpha: 0.6);
        }
    }
    &:hover {
        .working-opt {
            opacity: 1;
            top: 0;
        }
        .working-done {
            opacity: 1;
            top: 50px;
        }
    }
}
.small-panel-box {
    margin-top: 20px;
}
.small-panel {
    background-color: #e9edf2;
    border-radius: var(--el-border-radius-base);
    padding: 25px;
    margin-bottom: 20px;
    .small-panel-title {
        color: #92969a;
        font-size: 15px;
    }
    .small-panel-content {
        display: flex;
        align-items: flex-end;
        margin-top: 20px;
        color: #2c3f5d;
        .content-left {
            display: flex;
            align-items: center;
            font-size: 24px;
            .icon {
                margin-right: 10px;
            }
        }
        .content-right {
            font-size: 18px;
            margin-left: auto;
        }
        .color-success {
            color: var(--el-color-success);
        }
        .color-warning {
            color: var(--el-color-warning);
        }
        .color-danger {
            color: var(--el-color-danger);
        }
        .color-info {
            color: var(--el-text-color-secondary);
        }
    }
}
.growth-chart {
    margin-bottom: 20px;
}
.user-growth-chart,
.file-growth-chart {
    height: 260px;
}
.new-user-growth {
    height: 300px;
}

.user-source-chart,
.user-surname-chart {
    height: 400px;
}
.new-user-item {
    display: flex;
    align-items: center;
    padding: 20px;
    margin: 10px 15px;
    box-shadow: 0 0 30px 0 rgba(82, 63, 105, 0.05);
    background-color: var(--ba-bg-color-overlay);
    .new-user-avatar {
        height: 48px;
        width: 48px;
        border-radius: 50%;
    }
    .new-user-base {
        margin-left: 10px;
        color: #2c3f5d;
        .new-user-name {
            font-size: 15px;
        }
        .new-user-time {
            font-size: 13px;
        }
    }
    .new-user-arrow {
        margin-left: auto;
    }
}
.new-user-card :deep(.el-card__body) {
    padding: 0;
}

@media screen and (max-width: 425px) {
    .welcome-img {
        display: none;
    }
}
@media screen and (max-width: 1200px) {
    .lg-mb-20 {
        margin-bottom: 20px;
    }
}
html.dark {
    .welcome {
        background-color: var(--ba-bg-color-overlay);
    }
    .small-panel {
        background-color: var(--ba-bg-color-overlay);
        .small-panel-content {
            color: var(--el-text-color-regular);
        }
    }
    .new-user-item {
        .new-user-base {
            color: var(--el-text-color-regular);
        }
    }
}

/* 山姆会员商店美化样式 */
.sams-dashboard {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: calc(100vh - 48px);
    padding: 24px;
    margin: 24px;
    border-radius: 16px;
    overflow-x: hidden;
}

/* 确保所有容器不会超出屏幕宽度 */
.sams-dashboard * {
    box-sizing: border-box;
    //max-width: 100%;
}

/* 美化后的头部横幅 */
.sams-header-banner {
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24px;
    overflow: hidden;
    margin-bottom: 40px;
}

.banner-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.banner-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 60px 60px, 40px 40px;
    animation: patternMove 20s linear infinite;
}

.banner-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: glowRotate 30s linear infinite;
}

.banner-content {
    position: relative;
    z-index: 2;
    padding: 40px 32px;
}

.welcome-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.sams-brand-area {
    display: flex;
    align-items: center;
    gap: 20px;
}

.brand-logo-container {
    position: relative;
}

.logo-circle {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #0066cc 0%, #004499 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 32px rgba(0, 102, 204, 0.3);
    position: relative;
    z-index: 2;
}

.logo-glow {
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    background: linear-gradient(135deg, rgba(0, 102, 204, 0.3), rgba(0, 68, 153, 0.1));
    border-radius: 50%;
    filter: blur(8px);
    z-index: 1;
}

.brand-info {
    color: white;
}

.brand-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    letter-spacing: -1px;
}

.brand-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 4px 0 0 0;
    font-weight: 300;
    letter-spacing: 2px;
}

.welcome-content {
    color: white;
}

.welcome-greeting {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.welcome-message {
    font-size: 1rem;
    opacity: 0.9;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* 快捷操作按钮 */
.quick-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.export-btn {
    background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
    color: white;
}

.export-btn:hover {
    background: linear-gradient(135deg, #3a8ee6 0%, #337ecc 100%);
}

.query-btn {
    background: linear-gradient(135deg, #67c23a 0%, #5daf34 100%);
    color: white;
}

.query-btn:hover {
    background: linear-gradient(135deg, #5daf34 0%, #529b2e 100%);
}

/* 同步状态卡片 */
.sync-status-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 24px;
    color: white;
    height: 100%;
}

.status-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.status-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-title {
    font-size: 1.125rem;
    font-weight: 600;
}

.status-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.sync-time {
    font-size: 0.875rem;
    opacity: 0.9;
}

.sync-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    width: fit-content;
}

.badge-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #52c41a;
    animation: pulse 2s infinite;
}

.sync-badge.success .badge-dot {
    background: #52c41a;
}

.sync-badge.warning .badge-dot {
    background: #faad14;
}

.sync-badge.error .badge-dot {
    background: #ff4d4f;
}

/* 移动端同步状态卡片显示 */
.sync-status-col {
    margin-top: 0;
}

@media screen and (max-width: 992px) {
    .sync-status-col {
        margin-top: 20px;
    }
}

/* 统计面板列间距 */
.stats-panel-container .el-col {
    padding-right: 12px;
    padding-left: 12px;
    padding-bottom: 12px;
}

/* 图表和活动列的移动端间距 */
.chart-col,
.activity-col {
    margin-bottom: 24px;
}

@media screen and (min-width: 992px) {
    .chart-col:last-child,
    .activity-col:last-child {
        margin-bottom: 0;
    }
}

/* 美化后的统计面板 */
.stats-panel-container {
    margin-bottom: 32px;
}

.stats-card {
    background: white;
    border-radius: 20px;
    padding: 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.8);
    overflow: hidden;
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    height: 160px;
}

.stats-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.card-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.05;
    background-image:
        radial-gradient(circle at 20% 80%, currentColor 2px, transparent 2px),
        radial-gradient(circle at 80% 20%, currentColor 1px, transparent 1px);
    background-size: 30px 30px, 20px 20px;
}

.stores-card .card-pattern {
    color: #0066cc;
}

.goods-card .card-pattern {
    color: #00a854;
}

.categories-card .card-pattern {
    color: #f5a623;
}

.sync-card .card-pattern {
    color: #722ed1;
}

.card-header {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 24px 24px 16px;
}

.icon-container {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.icon-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
    border-radius: 12px;
}

.stores-icon {
    background: linear-gradient(135deg, #0066cc 0%, #004499 100%);
}

.goods-icon {
    background: linear-gradient(135deg, #00a854 0%, #007a3d 100%);
}

.categories-icon {
    background: linear-gradient(135deg, #f5a623 0%, #d48806 100%);
}

.sync-icon {
    background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
}

.card-info {
    flex: 1;
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 4px 0;
}

.card-subtitle {
    font-size: 0.75rem;
    color: #999;
    margin: 0;
    font-weight: 400;
}

.card-content {
    position: relative;
    z-index: 2;
    padding: 0 24px 24px;
}

.stats-number {
    display: flex;
    align-items: baseline;
    gap: 8px;
    margin-bottom: 12px;
}

.stats-unit {
    font-size: 1.125rem;
    font-weight: 500;
    color: #666;
}

.stats-trend {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 4px 12px;
    border-radius: 12px;
    width: fit-content;
}

.stats-trend.increase {
    color: #52c41a;
    background: rgba(82, 196, 26, 0.1);
}

.stats-trend.stable {
    color: #999;
    background: rgba(153, 153, 153, 0.1);
}

.stats-trend.decrease {
    color: #ff4d4f;
    background: rgba(255, 77, 79, 0.1);
}

/* 兼容旧的样式名 */
.stats-trend.positive {
    color: #52c41a;
    background: rgba(82, 196, 26, 0.1);
}

.stats-trend.negative {
    color: #ff4d4f;
    background: rgba(255, 77, 79, 0.1);
}

/* 美化后的图表容器 */
.charts-container,
.analysis-container {
    margin-bottom: 32px;
}

.chart-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.8);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    height: 400px;
}

.chart-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.header-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

.trend-card .header-icon {
    background: linear-gradient(135deg, rgba(0, 102, 204, 0.1), rgba(0, 102, 204, 0.05));
}

.distribution-card .header-icon {
    background: linear-gradient(135deg, rgba(0, 168, 84, 0.1), rgba(0, 168, 84, 0.05));
}

.category-card .header-icon {
    background: linear-gradient(135deg, rgba(114, 46, 209, 0.1), rgba(114, 46, 209, 0.05));
}

.region-card .header-icon {
    background: linear-gradient(135deg, rgba(235, 47, 150, 0.1), rgba(235, 47, 150, 0.05));
}

.header-content {
    flex: 1;
    display: flex;
    align-items: center;
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #333;
    margin: 0 16px 0 0;
}

.chart-subtitle {
    font-size: 0.875rem;
    color: #999;
    margin: 0;
}

.chart-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-body {
    padding: 16px 24px 24px;
    height: calc(100% - 80px);
}

.chart-body > div {
    width: 100%;
    height: 100%;
}

/* 美化后的活动卡片 */
.activity-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.8);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    height: 400px;
}

.activity-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.activity-header {
    display: flex;
    align-items: center;
    padding: 24px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.activity-header .header-icon {
    background: linear-gradient(135deg, rgba(245, 166, 35, 0.1), rgba(245, 166, 35, 0.05));
}

.activity-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #333;
    margin: 0 16px 0 0;
}

.activity-subtitle {
    font-size: 0.875rem;
    color: #999;
    margin: 0;
}

.activity-body {
    padding: 16px 0;
    height: calc(100% - 80px);
}

.activity-scrollbar {
    height: 100%;
}

.activity-list {
    padding: 0 24px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 0;
    border-bottom: 1px solid #f5f5f5;
    transition: all 0.3s ease;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background: rgba(0, 0, 0, 0.02);
    margin: 0 -16px;
    padding: 16px;
    border-radius: 12px;
}

.activity-icon-wrapper {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.activity-icon-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
    border-radius: 50%;
}

.activity-icon-wrapper.success {
    background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    color: white;
}

.activity-icon-wrapper.info {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    color: white;
}

.activity-icon-wrapper.warning {
    background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
    color: white;
}

.activity-icon-wrapper.error {
    background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
    color: white;
}

.activity-details {
    flex: 1;
    min-width: 0;
}

.activity-details .activity-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 6px 0;
}

.activity-desc {
    font-size: 0.8rem;
    color: #666;
    line-height: 1.5;
    margin: 0 0 6px 0;
}

.activity-time {
    font-size: 0.75rem;
    color: #999;
    margin: 0;
}

/* 活动列表空状态 */
.activity-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #999;
}

.empty-icon {
    margin-bottom: 12px;
    opacity: 0.5;
}

.empty-text {
    font-size: 0.875rem;
    color: #ccc;
}

/* 动画效果 */
@keyframes patternMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(60px, 60px); }
}

@keyframes glowRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

/* 响应式设计 - 简化版本 */
@media screen and (max-width: 1200px) {
    .sams-dashboard {
        padding: 20px;
        margin: 20px;
        min-height: calc(100vh - 40px);
    }

    .brand-title {
        font-size: 2rem;
    }
}

@media screen and (max-width: 992px) {
    .sync-status-col {
        margin-top: 20px;
    }

    .sams-brand-area {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .chart-col,
    .activity-col {
        margin-bottom: 20px;
    }
}

@media screen and (max-width: 768px) {
    .sams-dashboard {
        padding: 16px;
        margin: 16px;
        min-height: calc(100vh - 32px);
    }

    .sams-header-banner {
        border-radius: 16px;
        margin-bottom: 24px;
    }

    .banner-content {
        padding: 20px 16px;
    }

    .brand-title {
        font-size: 1.75rem;
    }

    .sync-status-card {
        padding: 16px;
    }

    .stats-card {
        margin-bottom: 16px;
    }

    .chart-card,
    .activity-card {
        margin-bottom: 16px;
    }

    .welcome-message {
        margin-bottom: 16px;
    }

    .quick-actions {
        flex-direction: column;
        gap: 8px;
        margin-top: 16px;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
        padding: 10px 16px;
    }
}

@media screen and (max-width: 480px) {
    .sams-dashboard {
        padding: 12px;
        margin: 12px;
        min-height: calc(100vh - 24px);
    }

    .sams-header-banner {
        border-radius: 12px;
        margin-bottom: 20px;
    }

    .banner-content {
        padding: 16px 12px;
    }

    .brand-title {
        font-size: 1.5rem;
    }

    .logo-circle {
        width: 48px;
        height: 48px;
    }

    .sync-status-card {
        padding: 12px;
    }

    .stats-card {
        margin-bottom: 12px;
    }

    .card-header {
        padding: 12px 12px 8px;
    }

    .card-content {
        padding: 0 12px 12px;
    }

    .icon-container {
        width: 32px;
        height: 32px;
    }

    .chart-card,
    .activity-card {
        margin-bottom: 12px;
    }

    .chart-header,
    .activity-header {
        padding: 12px 12px 8px;
    }

    .chart-body {
        padding: 8px 12px 12px;
        height: 200px;
    }

    .activity-body {
        height: 180px;
    }

    .activity-list {
        padding: 0 12px;
    }

    .activity-item {
        padding: 10px 0;
    }

    .activity-icon-wrapper {
        width: 24px;
        height: 24px;
    }
}


</style>
