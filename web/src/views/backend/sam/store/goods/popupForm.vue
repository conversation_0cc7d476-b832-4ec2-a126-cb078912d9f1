<template>
    <!-- 对话框表单 -->
    <!-- 建议使用 Prettier 格式化代码 -->
    <!-- el-form 内可以混用 el-form-item、FormItem、ba-input 等输入组件 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
        width="50%"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '':'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    v-if="!baTable.form.loading"
                    ref="formRef"
                    @submit.prevent=""
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    :label-position="config.layout.shrink ? 'top' : 'right'"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <FormItem :label="t('sam.store.goods.sam_store_id')" type="remoteSelect" v-model="baTable.form.items!.sam_store_id" prop="sam_store_id" :input-attr="{ pk: 'ba_sam_store.storeId', field: 'name', remoteUrl: '/admin/sam.Store/index' }" :placeholder="t('Please select field', { field: t('sam.store.goods.sam_store_id') })" />
                    <FormItem :label="t('sam.store.goods.sam_goods_id')" type="remoteSelect" v-model="baTable.form.items!.sam_goods_id" prop="sam_goods_id" :input-attr="{ pk: 'ba_sam_goods.spuId', field: 'name', remoteUrl: '/admin/sam.Goods/index' }" :placeholder="t('Please select field', { field: t('sam.store.goods.sam_goods_id') })" />
                    <FormItem :label="t('sam.store.goods.price')" type="number" prop="price" :input-attr="{ step: 1 }" v-model.number="baTable.form.items!.price" :placeholder="t('Please input field', { field: t('sam.store.goods.price') })" />
                    <FormItem :label="t('sam.store.goods.stock')" type="number" prop="stock" :input-attr="{ step: 1 }" v-model.number="baTable.form.items!.stock" :placeholder="t('Please input field', { field: t('sam.store.goods.stock') })" />
                    <FormItem :label="t('sam.store.goods.remark')" type="textarea" v-model="baTable.form.items!.remark" prop="remark" :input-attr="{ rows: 3 }" @keyup.enter.stop="" @keyup.ctrl.enter="baTable.onSubmit(formRef)" :placeholder="t('Please input field', { field: t('sam.store.goods.remark') })" />
                    <FormItem :label="t('sam.store.goods.status')" type="switch" v-model="baTable.form.items!.status" prop="status" :input-attr="{ content: { '0': t('sam.store.goods.status 0'), '1': t('sam.store.goods.status 1') } }" />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm()">{{ t('Cancel') }}</el-button>
                <el-button v-blur :loading="baTable.form.submitLoading" @click="baTable.onSubmit(formRef)" type="primary">
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormItemRule } from 'element-plus'
import { inject, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import type baTableClass from '/@/utils/baTable'
import { buildValidatorData } from '/@/utils/validate'

const config = useConfig()
const formRef = ref<FormInstance>()
const baTable = inject('baTable') as baTableClass

const { t } = useI18n()

const rules: Partial<Record<string, FormItemRule[]>> = reactive({
    sam_store_id: [buildValidatorData({ name: 'required', title: t('sam.store.goods.sam_store_id') })],
    sam_goods_id: [buildValidatorData({ name: 'required', title: t('sam.store.goods.sam_goods_id') })],
    price: [buildValidatorData({ name: 'number', title: t('sam.store.goods.price') }), buildValidatorData({ name: 'required', title: t('sam.store.goods.price') })],
    stock: [buildValidatorData({ name: 'number', title: t('sam.store.goods.stock') }), buildValidatorData({ name: 'required', title: t('sam.store.goods.stock') })],
    status: [buildValidatorData({ name: 'required', title: t('sam.store.goods.status') })],
    create_time: [buildValidatorData({ name: 'date', title: t('sam.store.goods.create_time') })],
    update_time: [buildValidatorData({ name: 'date', title: t('sam.store.goods.update_time') })],
})
</script>

<style scoped lang="scss"></style>
