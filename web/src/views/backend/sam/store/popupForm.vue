<template>
    <!-- 对话框表单 -->
    <!-- 建议使用 Prettier 格式化代码 -->
    <!-- el-form 内可以混用 el-form-item、FormItem、ba-input 等输入组件 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
        width="50%"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '':'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    v-if="!baTable.form.loading"
                    ref="formRef"
                    @submit.prevent=""
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    :label-position="config.layout.shrink ? 'top' : 'right'"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <FormItem :label="t('sam.store.name')" type="string" v-model="baTable.form.items!.name" prop="name" :placeholder="t('Please input field', { field: t('sam.store.name') })" />
                    <FormItem :label="t('sam.store.storeId')" type="number" prop="storeId" :input-attr="{ step: 1 }" v-model.number="baTable.form.items!.storeId" :placeholder="t('Please input field', { field: t('sam.store.storeId') })" />
                    <FormItem :label="t('sam.store.type')" type="radio" v-model="baTable.form.items!.type" prop="type" :input-attr="{ content: { '2': t('sam.store.type 2'), '4': t('sam.store.type 4'), '8': t('sam.store.type 8'), '16': t('sam.store.type 16'), '32': t('sam.store.type 32'), '64': t('sam.store.type 64'), '128': t('sam.store.type 128'), '256': t('sam.store.type 256') } }" :placeholder="t('Please select field', { field: t('sam.store.type') })" />
                    <FormItem :label="t('sam.store.city')" type="string" v-model="baTable.form.items!.city" prop="city" :placeholder="t('Please input field', { field: t('sam.store.city') })" />
                    <FormItem :label="t('sam.store.address')" type="string" v-model="baTable.form.items!.address" prop="address" :placeholder="t('Please input field', { field: t('sam.store.address') })" />
                    <FormItem :label="t('sam.store.location')" type="string" v-model="baTable.form.items!.location" prop="location" :placeholder="t('Please input field', { field: t('sam.store.location') })" />
                    <FormItem :label="t('sam.store.location2')" type="string" v-model="baTable.form.items!.location2" prop="location2" :placeholder="t('Please input field', { field: t('sam.store.location2') })" />
                    <FormItem :label="t('sam.store.postcode')" type="number" prop="postcode" :input-attr="{ step: 1 }" v-model.number="baTable.form.items!.postcode" :placeholder="t('Please input field', { field: t('sam.store.postcode') })" />
                    <FormItem label="更新时间间隔（秒）" type="number" prop="update_interval" :input-attr="{ step: 1 }" v-model.number="baTable.form.items!.update_interval" placeholder="请输入更新间隔,单位秒" />
                    <FormItem :label="t('sam.store.remark')" type="textarea" v-model="baTable.form.items!.remark" prop="remark" :input-attr="{ rows: 3 }" @keyup.enter.stop="" @keyup.ctrl.enter="baTable.onSubmit(formRef)" :placeholder="t('Please input field', { field: t('sam.store.remark') })" />
                    <FormItem :label="t('sam.store.status')" type="switch" v-model="baTable.form.items!.status" prop="status" :input-attr="{ content: { '0': t('sam.store.status 0'), '1': t('sam.store.status 1') } }" />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm()">{{ t('Cancel') }}</el-button>
                <el-button v-blur :loading="baTable.form.submitLoading" @click="baTable.onSubmit(formRef)" type="primary">
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormItemRule } from 'element-plus'
import { inject, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import type baTableClass from '/@/utils/baTable'
import { buildValidatorData } from '/@/utils/validate'

const config = useConfig()
const formRef = ref<FormInstance>()
const baTable = inject('baTable') as baTableClass

const { t } = useI18n()

const rules: Partial<Record<string, FormItemRule[]>> = reactive({
    name: [buildValidatorData({ name: 'required', title: t('sam.store.name') })],
    storeId: [buildValidatorData({ name: 'number', title: t('sam.store.storeId') }), buildValidatorData({ name: 'required', title: t('sam.store.storeId') })],
    type: [buildValidatorData({ name: 'required', title: t('sam.store.type') })],
    city: [buildValidatorData({ name: 'required', title: t('sam.store.city') })],
    address: [buildValidatorData({ name: 'required', title: t('sam.store.address') })],
    postcode: [buildValidatorData({ name: 'number', title: t('sam.store.postcode') })],
    update_interval: [buildValidatorData({ name: 'number', title: '请输入更新间隔,单位秒' })],
    create_time: [buildValidatorData({ name: 'date', title: t('sam.store.create_time') })],
    update_time: [buildValidatorData({ name: 'date', title: t('sam.store.update_time') })],
})
</script>

<style scoped lang="scss"></style>
