<template>
    <div class="default-main ba-table-box">
        <el-alert class="ba-table-alert" v-if="baTable.table.remark" :title="baTable.table.remark" type="info" show-icon />

        <!-- 表格顶部菜单 -->
        <!-- 自定义按钮请使用插槽，甚至公共搜索也可以使用具名插槽渲染，参见文档 -->
        <TableHeader
            :buttons="['refresh', 'add', 'comSearch', 'quickSearch', 'columnDisplay']"
            :quick-search-placeholder="
                t('Quick search placeholder', {
                    fields: t('routine.plantask.quick Search Fields'),
                })
            "
        >
            <template #default>
                <div class="header-customer">
                    <div class="task-status">
                        <el-radio-group v-model="status" @change="baTable.getIndex()">
                            <el-radio-button label="全部" value="" />
                            <el-radio-button label="执行中" :value="1" />
                            <el-radio-button label="停止执行" :value="3" />
                        </el-radio-group>
                    </div>
                </div>
                <div class="header-customer">
                    <div class="server-status">
                        <span class="start" v-if="serverStatus">服务已启动</span>
                        <span class="unstart" v-else>服务未启动</span>
                    </div>
                </div>
                <div class="header-customer" v-if="unreadErrorTotal > 0">
                    <div class="unread-error-total">
                        <template v-if="filtering">
                            <el-tag type="danger" closable @close="filteringError(false)">已筛选报错任务</el-tag>
                        </template>
                        <template v-else>
                            <el-tooltip content="点击筛选" placement="top">
                                <el-tag type="danger" @click="filteringError">未读报错数量：{{ unreadErrorTotal }}</el-tag>
                            </el-tooltip>
                        </template>
                    </div>
                </div>
            </template>
        </TableHeader>

        <!-- 表格 -->
        <!-- 表格列有多种自定义渲染方式，比如自定义组件、具名插槽等，参见文档 -->
        <!-- 要使用 el-table 组件原有的属性，直接加在 Table 标签上即可 -->
        <Table ref="tableRef">
            <template #buttons>
                <!-- 还可以继续使用 el-table-column 组件本身的插槽 -->
                <el-table-column label="操作" width="180" align="left">
                    <template #default="scope">
                        <div style="display: flex; gap: 3px">
                            <div>
                                <el-tooltip content="测试执行一次" placement="top">
                                    <el-button v-blur @click="testExecute(scope.row)" class="table-operate" type="warning">
                                        <Icon name="fa fa-hand-o-down" />
                                    </el-button>
                                </el-tooltip>
                            </div>
                            <div>
                                <el-tooltip
                                    :content="scope.row.unread_error_count > 0 ? `查看日志 [error:${scope.row.unread_error_count}]` : '查看日志'"
                                    placement="top"
                                >
                                    <el-badge :is-dot="scope.row.unread_error_count > 0" class="badge" :offset="[-2, 3]">
                                        <el-button v-blur @click="viewLog(scope.row)" class="table-operate" type="info">
                                            <Icon name="fa fa-file-text-o" />
                                        </el-button>
                                    </el-badge>
                                </el-tooltip>
                            </div>

                            <div v-if="[0, 1, 2].includes(scope.row?.status)">
                                <el-tooltip content="停止" placement="top">
                                    <el-button v-blur @click="stopExecute(scope.row)" class="table-operate" type="danger">
                                        <Icon name="fa fa-stop-circle" />
                                    </el-button>
                                </el-tooltip>
                            </div>
                            <div v-if="[3, 4].includes(scope.row?.status)">
                                <el-tooltip content="启动" placement="top">
                                    <el-button v-blur @click="startExecute(scope.row)" class="table-operate" type="success">
                                        <Icon name="fa fa-play-circle" />
                                    </el-button>
                                </el-tooltip>
                            </div>

                            <div>
                                <el-tooltip content="编辑" placement="top">
                                    <el-button v-blur @click="baTable.onTableAction('edit', scope)" class="table-operate" type="primary">
                                        <Icon name="fa fa-pencil" />
                                    </el-button>
                                </el-tooltip>
                            </div>
                            <div v-if="[3, 4].includes(scope.row?.status)">
                                <el-popconfirm title="确定要删除所选记录吗？" @confirm="baTable.onTableAction('delete', scope)">
                                    <template #reference>
                                        <el-button v-blur class="table-operate" type="danger">
                                            <Icon name="fa fa-trash" />
                                        </el-button>
                                    </template>
                                </el-popconfirm>
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </template>
        </Table>

        <!-- 表单 -->
        <PopupForm />

        <Tasklog v-model:show="showTaskLog" :taskId="taskId" :title="taskName" @close="taskLogClost" />
    </div>
</template>

<script setup lang="ts">
import { onMounted, provide, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import PopupForm from './popupForm.vue'
import { baTableApi } from '/@/api/common'
import TableHeader from '/@/components/table/header/index.vue'
import Table from '/@/components/table/index.vue'
import baTableClass from '/@/utils/baTable'
import Tasklog from './tasklog.vue'
import { executeOnce } from '/@/api/backend/routine/plantask'

defineOptions({
    name: 'plantask',
})

const status = ref('')
const showTaskLog = ref(false)
const taskId = ref(0)
const taskName = ref('')

const { t } = useI18n()
const tableRef = ref()

//测试执行
const testExecute = (row: TableRow) => {
    executeOnce(row.task_id)
}

//查看日志
const viewLog = (row: TableRow) => {
    taskId.value = row.task_id
    taskName.value = row.task_name
    showTaskLog.value = true
}

//停止执行
const stopExecute = (row: TableRow) => {
    baTable.api.postData('edit', { task_id: row.task_id, status: 3 }).then(() => {
        baTable.onTableHeaderAction('refresh', {})
    })
}

//启动
const startExecute = (row: TableRow) => {
    baTable.api.postData('edit', { task_id: row.task_id, status: 0 }).then(() => {
        baTable.onTableHeaderAction('refresh', {})
    })
}
const unreadErrorTotal = ref(0)

// 关闭日志
const taskLogClost = () => {
    showTaskLog.value = false
    // baTable.getIndex()
}

const filtering = ref(false)
const filteringError = (filter: any = true) => {
    if (filter) {
        baTable.table.filter!.unread_error = 1
        filtering.value = true
    } else {
        delete baTable.table.filter!.unread_error
        filtering.value = false
    }
    baTable.getIndex()
}

/**
 * baTable 内包含了表格的所有数据且数据具备响应性，然后通过 provide 注入给了后代组件
 */
const baTable = new baTableClass(new baTableApi('/admin/routine.Plantask/'), {
    pk: 'task_id',
    column: [
        {
            label: t('routine.plantask.task_id'),
            prop: 'task_id',
            align: 'center',
            width: 70,
            operator: 'RANGE',
            sortable: 'custom',
        },
        {
            label: t('routine.plantask.task_name'),
            prop: 'task_name',
            align: 'center',
            operator: 'LIKE',
            sortable: false,
        },
        {
            label: t('routine.plantask.type'),
            prop: 'type',
            align: 'center',
            width: 100,
            render: 'tag',
            operator: 'LIKE',
            sortable: false,
            replaceValue: {
                url: t('routine.plantask.url'),
                sql: t('routine.plantask.sql'),
                method: t('routine.plantask.class_method'),
            },
        },
        {
            label: t('routine.plantask.repeat_times'),
            prop: 'repeat_times',
            align: 'center',
            width: 100,
            sortable: false,
            formatter(row) {
                if (row.repeat_times == 0) {
                    return '不限次数'
                }
                return row.repeat_times + ' 次'
            },
            operator: false,
        },
        {
            label: t('routine.plantask.cycle'),
            prop: 'rule',
            align: 'center',
            operator: false,
            sortable: false,
            render: 'tags',
        },
        {
            label: t('routine.plantask.times'),
            prop: 'times',
            align: 'center',
            width: 150,
            operator: 'RANGE',
            sortable: false,
            formatter(row) {
                if (row.times == 0) return '-'
                return row.times + ' 次'
            },
        },
        {
            label: t('routine.plantask.run_time'),
            prop: 'run_time',
            align: 'center',
            render: 'datetime',
            operator: 'RANGE',
            sortable: 'custom',
            width: 160,
            timeFormat: 'yyyy-mm-dd hh:MM:ss',
        },
        {
            label: t('routine.plantask.status'),
            prop: 'status',
            align: 'center',
            width: 100,
            operator: false,
            sortable: false,
            render: 'tag',
            custom: {
                '0': 'waring',
                '1': 'waring',
                '2': 'success',
                '3': 'info',
                '4': 'danger',
            },
            replaceValue: {
                '0': t('routine.plantask.status 0'),
                '1': t('routine.plantask.status 1'),
                '2': t('routine.plantask.status 2'),
                '3': t('routine.plantask.status 3'),
                '4': t('routine.plantask.status 4'),
                '10': t('routine.plantask.status 10'),
            },
        },

        {
            label: t('routine.plantask.add_time'),
            prop: 'add_time',
            align: 'center',
            render: 'datetime',
            operator: 'RANGE',
            sortable: 'custom',
            width: 160,
            timeFormat: 'yyyy-mm-dd hh:MM:ss',
        },
        {
            label: t('routine.plantask.weigh'),
            prop: 'weigh',
            align: 'center',
            operator: false,
            sortable: 'custom',
            width: 80,
        },
        { render: 'slot', slotName: 'buttons', width: 170, align: 'left' },
    ],
    dblClickNotEditColumn: ['all'],
    defaultOrder: { prop: 'weigh', order: 'desc' },
})

const serverStatus = ref(false)
baTable.before.getIndex = () => {
    baTable.table!.filter!.status = status.value
}
baTable.after.getIndex = ({ res }) => {
    serverStatus.value = res.data.server_status
    unreadErrorTotal.value = res.data.unread_error_total
}

provide('baTable', baTable)

onMounted(() => {
    baTable.table.ref = tableRef.value
    baTable.mount()
    baTable.getIndex()?.then(() => {
        baTable.initSort()
        baTable.dragSort()
    })
})
</script>

<style scoped lang="scss">
.header-customer {
    display: flex;
    .server-status {
        line-height: 32px;
    }
    .server-status > .start {
        color: green;
    }
    .server-status > .unstart {
        color: red;
    }
    .task-status {
        // width: 380px;
        margin: 0 20px 0 10px;
    }
    .unread-error-total {
        cursor: pointer;
        margin-left: 20px;
    }
}
.table-operate-text {
    padding-left: 5px;
}
.table-operate {
    padding: 4px 5px;
    height: auto;
}
.table-operate .icon {
    font-size: 14px !important;
    color: var(--ba-bg-color-overlay) !important;
}
.badge {
    z-index: 999999999999999999;
}
</style>
