<template>
    <el-dialog @open="onOpen" :title="title" :model-value="show" @close="emits('close')" width="1000px">
        <div class="default-main ba-table-box">
            <div class="header">
                <div>
                    <el-date-picker
                        v-model="logTable.queryParams.start_time"
                        type="datetimerange"
                        :clearable="true"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 370px"
                        @change="getTaskLogHandle"
                    />
                </div>
                <div>
                    <el-select v-model="logTable.queryParams.status" clearable placeholder="筛选状态" @change="getTaskLogHandle" style="width: 120px">
                        <el-option label="成功" :value="1" />
                        <el-option label="失败" :value="0" />
                    </el-select>
                </div>
                <div>
                    <el-button type="primary" @click="getTaskLogHandle"
                        >搜索 <el-icon><Search /></el-icon
                    ></el-button>
                </div>
                <div>
                    <el-button type="info" @click="downloadTaskLog" :loading="logTable.downloadLoading">
                        {{ logTable.queryParams.start_time ? '下载筛选日志' : '下载日志' }} <el-icon style="font-size: 14px"> <Download /></el-icon>
                    </el-button>
                </div>
                <div>
                    <el-popconfirm title="此操作将不能恢复，确认删除日志吗？" @confirm="deleteTaskLog">
                        <template #reference>
                            <el-button type="danger"
                                >{{ logTable.queryParams.start_time ? '删除筛选日志' : '清空全部日志' }}
                                <el-icon style="font-size: 14px"> <Delete /></el-icon
                            ></el-button>
                        </template>
                    </el-popconfirm>
                </div>
            </div>
            <div>
                <el-table
                    :data="logTable.logList"
                    stripe
                    class="ba-data-table"
                    header-cell-class-name="table-header-cell"
                    v-loading="logTable.getIndexLoading"
                >
                    <el-table-column prop="log_id" label="ID" width="100" align="center" />
                    <el-table-column prop="start_time_text" label="执行时间" width="180" align="center" />
                    <el-table-column prop="status" label="状态" width="100" align="center">
                        <template #default="scope">
                            <el-tag v-if="scope.row.status === 1" type="success">成功</el-tag>
                            <el-tag v-else type="danger">失败</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="expend_time_text" label="耗时（毫秒）" align="center" width="180">
                        <template #default="scope">
                            <span>{{ scope.row.expend_time_text }} </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="response_short" label="响应">
                        <template #default="scope">
                            <span>{{ scope.row.response_short }}</span>
                            <el-button v-if="scope.row.response_short !== scope.row.response" type="text" @click="showLoginfo(scope.row.response)"
                                >查看</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    v-if="logTable.total > 1"
                    background
                    layout="prev, pager, next, jumper, total"
                    :total="logTable.total"
                    :currentPage="logTable.queryParams.page"
                    @current-change="getTasklogList"
                    style="margin-top: 10px"
                />
            </div>
        </div>
        <el-dialog title="完整响应" :model-value="loginfoShow" @close="loginfoShow = false">
            <el-scrollbar width="1000px" style="height: 618px; padding: 0 20px">
                <pre class="response-full-text">{{ responseText }}</pre>
            </el-scrollbar>
        </el-dialog>
    </el-dialog>
</template>

<script setup lang="ts">
import { Delete, Download, Search } from '@element-plus/icons-vue'
import { defineProps, defineEmits, reactive, ref } from 'vue'
import { deleteTaskApi, downloadLogApi, getTasklogApi } from '/@/api/backend/routine/plantask'

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    taskId: {
        type: Number,
        default: 0,
    },
    title: {
        type: String,
        default: '',
    },
})

const logTable = reactive({
    queryParams: {
        task_id: 0,
        page: 1,
        start_time: '',
        status: '',
    },
    logList: [],
    total: 0,
    getIndexLoading: false,
    downloadLoading: false,
})

const getTasklogList = (page: number = 1) => {
    if (logTable.getIndexLoading) return
    logTable.queryParams.task_id = props.taskId
    logTable.queryParams.page = page

    logTable.getIndexLoading = true
    getTasklogApi(logTable.queryParams)
        .then((res) => {
            logTable.logList = res.data.data
            logTable.queryParams.page = res.data.current_page
            logTable.total = res.data.total
        })
        .finally(() => {
            logTable.getIndexLoading = false
        })
        .catch(() => {
            logTable.getIndexLoading = false
        })
}

const loginfoShow = ref(false)
const responseText = ref('')
const showLoginfo = (text: string) => {
    responseText.value = text
    loginfoShow.value = true
}

const onOpen = () => {
    logTable.queryParams.start_time = ''
    logTable.queryParams.status = ''
    logTable.logList = []
    logTable.total = 0
    getTaskLogHandle()
}

const getTaskLogHandle = () => {
    logTable.queryParams.page = 1
    getTasklogList()
}

const deleteTaskLog = () => {
    deleteTaskApi(logTable.queryParams).then(() => {
        getTaskLogHandle()
    })
}
const downloadTaskLog = () => {
    logTable.downloadLoading = true
    downloadLogApi(logTable.queryParams)
        .then((res: any) => {
            const filename = res.headers['content-disposition'].split('filename=')[1]
            const blob = new Blob([res.data], { type: 'application/octet-stream' })
            const downloadUrl = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = downloadUrl
            link.setAttribute('download', filename ? filename : `task_log_${props.taskId}.log`)
            document.body.appendChild(link)
            link.click()
            window.URL.revokeObjectURL(downloadUrl)
            document.body.removeChild(link)
            // downloading.close()
        })
        .finally(() => {
            logTable.downloadLoading = false
        })
        .catch(() => {
            logTable.downloadLoading = false
        })
}

const emits = defineEmits(['close'])
</script>
<style scoped lang="scss">
.header {
    width: 100%;
    height: 50px;
    display: flex;
    gap: 10px;
}
.ba-data-table :deep(.el-button + .el-button) {
    margin-left: 6px;
}
.ba-data-table :deep(.table-header-cell) .cell {
    color: var(--el-text-color-primary);
    overflow: hidden;
}
.table-pagination {
    box-sizing: border-box;
    width: 100%;
    max-width: 100%;
    background-color: var(--ba-bg-color-overlay);
    padding: 13px 15px;
}
.response-full-text {
    background-color: #f9f9f9;
    padding: 16px;
    border-radius: 4px;
    border-left: 4px solid #409eff;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    line-height: 1.5;
    color: #333;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>
