<template>
    <!-- 对话框表单 -->
    <!-- 建议使用 Prettier 格式化代码 -->
    <!-- el-form 内可以混用 el-form-item、FormItem、ba-input 等输入组件 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
        width="1000px"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }} {{ readOnly ? '(只读)' : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '' : 'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    v-if="!baTable.form.loading"
                    ref="formRef"
                    @submit.prevent=""
                    :model="baTable.form.items"
                    :label-position="config.layout.shrink ? 'top' : 'right'"
                    :label-width="baTable.form.labelWidth + 'px'"
                >
                    <FormItem
                        :label="t('routine.plantask.task_name')"
                        type="string"
                        v-model="baTable.form.items!.task_name"
                        prop="task_name"
                        :input-attr="{
                            disabled: readOnly,
                        }"
                        :placeholder="
                            t('Please input field', {
                                field: t('routine.plantask.task_name'),
                            })
                        "
                    />

                    <el-form-item :label="t('routine.plantask.type')">
                        <el-radio-group v-model="baTable.form.items!.type" :disabled="readOnly">
                            <el-radio-button :label="t('routine.plantask.type method')" value="method" />
                            <el-radio-button :label="t('routine.plantask.type url')" value="url" />
                            <el-radio-button :label="t('routine.plantask.type sql')" value="sql" />
                        </el-radio-group>
                    </el-form-item>

                    <FormItem
                        v-if="baTable.form.items!.type == 'method'"
                        :label="t('routine.plantask.class_method')"
                        type="string"
                        v-model="baTable.form.items!.goal"
                        prop="class_method"
                        :input-attr="{
                            disabled: readOnly,
                        }"
                        :placeholder="
                            t('Please input field', {
                                field: t('routine.plantask.class_method'),
                            })
                        "
                        tip="格式“\命名空间\类名@方法名”，如：\app\api\service\TaskService@run"
                    />

                    <FormItem
                        v-if="baTable.form.items!.type == 'url'"
                        :label="t('routine.plantask.url')"
                        type="string"
                        v-model="baTable.form.items!.goal"
                        prop="url"
                        :input-attr="{
                            disabled: readOnly,
                        }"
                        :placeholder="
                            t('Please input field', {
                                field: t('routine.plantask.url'),
                            })
                        "
                        tip="请输入URL，若有URL参数请填写在“参数”字段，若需要指定请求类型则：POST https://xxx.com，默认GET请求"
                    />

                    <FormItem
                        v-if="baTable.form.items!.type == 'sql'"
                        :label="t('routine.plantask.sql')"
                        type="textarea"
                        v-model="baTable.form.items!.goal"
                        prop="class_method"
                        :input-attr="{ rows: 6, disabled: readOnly, inputStyle: { height: '123px' } }"
                        placeholder="请输入SQL语句"
                        tip="请输入SQL语句。支持多语句，以分号“;”分隔。"
                    />

                    <FormItem
                        v-if="baTable.form.items!.type == 'method'"
                        :label="t('routine.plantask.params')"
                        type="textarea"
                        v-model="baTable.form.items!.params"
                        prop="params"
                        :input-attr="{ rows: 3, disabled: readOnly }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="
                            t('Please input field', {
                                field: t('routine.plantask.params'),
                            })
                        "
                        tip='请填写数组，数组每个元素依次对应方法中的参数。如：["参数1", "param2", 3, {"name":"张三"}]，没有参数则不填。'
                    />

                    <FormItem
                        v-if="baTable.form.items!.type == 'url'"
                        :label="t('routine.plantask.params')"
                        type="textarea"
                        v-model="baTable.form.items!.params"
                        prop="params"
                        :input-attr="{ rows: 3, disabled: readOnly }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="
                            t('Please input field', {
                                field: t('routine.plantask.params'),
                            })
                        "
                        tip='请填写json格式，URL参数格式：{"name":"张三","gender":1}，没有参数则不填。'
                    />

                    <el-form-item label="重复次数">
                        <el-col :span="6">
                            <el-select v-model="options.repeat_times_type" :disabled="readOnly">
                                <el-option :value="0" label="不限次数" />
                                <el-option :value="1" label="单次执行" />
                                <el-option :value="2" label="指定次数" />
                            </el-select>
                        </el-col>
                        <el-col :span="8" v-if="options.repeat_times_type == 2">
                            <el-input
                                v-model.number="baTable.form.items!.repeat_times"
                                type="number"
                                step="1"
                                min="1"
                                :disabled="readOnly"
                                :placeholder="
                                    t('Please input field', {
                                        field: t('routine.plantask.repeat_times'),
                                    })
                                "
                            >
                                <template #append>次</template>
                            </el-input>
                        </el-col>
                    </el-form-item>

                    <el-form-item label="重复周期" v-if="options.repeat_times_type != 1">
                        <el-col :span="6">
                            <el-select v-model="options.rule_type" :disabled="readOnly">
                                <el-option value="seconds" label="固定间隔时长" />
                                <el-option value="crontab" label="Crontab规则" />
                            </el-select>
                        </el-col>
                        <el-col :span="8" v-if="options.rule_type == 'seconds'">
                            <el-input
                                v-model.number="baTable.form.items!.rule"
                                type="number"
                                step="1"
                                min="1"
                                :disabled="readOnly"
                                placeholder="请输入间隔时间（秒）"
                            >
                                <template #append>秒</template>
                            </el-input>
                        </el-col>
                        <template v-if="options.rule_type == 'crontab'">
                            <el-col :span="2">
                                <el-input v-model="options.cron_rule[0]" :disabled="readOnly" type="text" input-style="text-align: center;" />
                            </el-col>
                            <el-col :span="2">
                                <el-input v-model="options.cron_rule[1]" :disabled="readOnly" type="text" input-style="text-align: center;" />
                            </el-col>
                            <el-col :span="2">
                                <el-input v-model="options.cron_rule[2]" :disabled="readOnly" type="text" input-style="text-align: center;" />
                            </el-col>
                            <el-col :span="2">
                                <el-input v-model="options.cron_rule[3]" :disabled="readOnly" type="text" input-style="text-align: center;" />
                            </el-col>
                            <el-col :span="2">
                                <el-input v-model="options.cron_rule[4]" :disabled="readOnly" type="text" input-style="text-align: center;" />
                            </el-col>
                            <el-col :span="3">
                                <el-button v-bind:loading="checkCronRuleloading" type="primary" @click="checkCronRule" style="margin-left: 5px"
                                    >验证</el-button
                                >
                            </el-col>
                            <el-col :span="3">
                                <el-button type="info" :icon="QuestionFilled" @click="showCrontabRules = true">Crontab规则</el-button>
                            </el-col>
                        </template>
                    </el-form-item>
                    <el-form-item v-if="showCrontabRules">
                        <el-alert show-icon type="info" @close="showCrontabRules = false">
                            <template #default>
                                <p>Crontab由5个字段组成，5个字段从左往右依次对应：<b>分钟</b> - <b>小时</b> - <b>日</b> - <b>月</b> - <b>周</b></p>
                                <h4>基础用法：</h4>
                                <p>分钟：取值范围：0-59，说明：每小时的第几分钟执行</p>
                                <p>小时：取值范围：0-23，说明：每天的第几小时执行</p>
                                <p>日：取值范围：1-12，说明：每年的第几个月执行</p>
                                <p>周：取值范围：0-6，0表示周日，说明：每周的周几执行</p>
                                <h4>拓展用法：</h4>
                                <p>
                                    使用特殊符号能实现更多效果：<b class="symbol">*</b> <b class="symbol">,</b> <b class="symbol">-</b
                                    ><b class="symbol">/</b>
                                </p>
                                <p><b class="symbol">*</b>匹配任意值（例如 在“分钟”字段填*：表示每分钟执行）</p>
                                <p><b class="symbol">,</b>指定多个值（例如 在“小时”字段填2,4,6：表示2点、4点、6点执行）</p>
                                <p><b class="symbol">-</b>指定范围（例如 在“日”字段填10-15：表示10号到15号执行）</p>
                                <p><b class="symbol">/</b>间隔频率（例如 在“分钟”字段填*/5 表示每5分钟执行）</p>
                                <p><b>注意：“日”字段和“周”字段不能同时使用，只能使用一个</b></p>
                            </template>
                        </el-alert>
                    </el-form-item>
                    <el-form-item label="" v-if="options.repeat_times_type != 1 && options.rule_type == 'crontab' && !showCrontabRules">
                        <el-col :offset="6" :span="2">
                            <div class="cron-instructions cron-instructions-string">*</div>
                            <div class="cron-instructions cron-instructions-string">-</div>
                            <div class="cron-instructions">|</div>
                        </el-col>
                        <el-col :span="2">
                            <div class="cron-instructions cron-instructions-string">*</div>
                            <div class="cron-instructions cron-instructions-string">-</div>
                            <div class="cron-instructions">|</div>
                        </el-col>
                        <el-col :span="2">
                            <div class="cron-instructions cron-instructions-string">*</div>
                            <div class="cron-instructions cron-instructions-string">-</div>
                            <div class="cron-instructions">|</div>
                        </el-col>
                        <el-col :span="2">
                            <div class="cron-instructions cron-instructions-string">*</div>
                            <div class="cron-instructions cron-instructions-string">-</div>
                            <div class="cron-instructions">|</div>
                        </el-col>
                        <el-col :span="2">
                            <div class="cron-instructions cron-instructions-string">*</div>
                            <div class="cron-instructions cron-instructions-string">-</div>
                            <div class="cron-instructions">+</div>
                        </el-col>
                        <el-col :span="8">
                            <div class="cron-instructions cron-instructions-string">&nbsp;</div>
                            <div class="cron-instructions cron-instructions-string">&nbsp;</div>
                            <div class="cron-instructions cron-instructions-text">--- 周 [0-6] (周日 = 0)</div>
                        </el-col>

                        <el-col :offset="6" :span="2">
                            <div class="cron-instructions">|</div>
                        </el-col>
                        <el-col :span="2">
                            <div class="cron-instructions">|</div>
                        </el-col>
                        <el-col :span="2">
                            <div class="cron-instructions">|</div>
                        </el-col>
                        <el-col :span="2">
                            <div class="cron-instructions">+</div>
                        </el-col>
                        <el-col :span="10">
                            <div class="cron-instructions cron-instructions-text">---------------- 月 [1-12]</div>
                        </el-col>

                        <el-col :offset="6" :span="2">
                            <div class="cron-instructions">|</div>
                        </el-col>
                        <el-col :span="2">
                            <div class="cron-instructions">|</div>
                        </el-col>
                        <el-col :span="2">
                            <div class="cron-instructions">+</div>
                        </el-col>
                        <el-col :span="12">
                            <div class="cron-instructions cron-instructions-text">----------------------------- 日 [1-31]</div>
                        </el-col>

                        <el-col :offset="6" :span="2">
                            <div class="cron-instructions">|</div>
                        </el-col>
                        <el-col :span="2">
                            <div class="cron-instructions">+</div>
                        </el-col>
                        <el-col :span="14">
                            <div class="cron-instructions cron-instructions-text">------------------------------------------ 小时 [0-23]</div>
                        </el-col>

                        <el-col :offset="6" :span="2">
                            <div class="cron-instructions">+</div>
                        </el-col>
                        <el-col :span="16">
                            <div class="cron-instructions cron-instructions-text">
                                ------------------------------------------------------- 分钟 [0-59]
                            </div>
                        </el-col>
                    </el-form-item>

                    <el-form-item :label="t('routine.plantask.run_time')">
                        <el-radio-group v-model="options.run_time_type" :disabled="readOnly">
                            <el-radio-button label="立即执行" value="now" />
                            <el-radio-button label="自定义时间" value="customerTime" />
                        </el-radio-group>
                    </el-form-item>

                    <FormItem
                        v-if="options.run_time_type == 'customerTime'"
                        :label="t('routine.plantask.run_time')"
                        type="datetime"
                        v-model="baTable.form.items!.run_time"
                        :input-attr="{
                            disabledDate: (time: Date) => {
                                //小于当前时间不能选择
                                return time.getTime() < Date.now() - 8.64e7
                            },
                            disabled: readOnly,
                        }"
                        prop="run_time"
                        :placeholder="
                            t('Please select field', {
                                field: t('routine.plantask.run_time'),
                            })
                        "
                        tip="到执行时间，任务才会首次执行"
                    />
                    <FormItem
                        :label="t('routine.plantask.remark')"
                        type="textarea"
                        v-model="baTable.form.items!.remark"
                        prop="remark"
                        :input-attr="{
                            disabled: readOnly,
                        }"
                        :placeholder="
                            t('Please input field', {
                                field: t('routine.plantask.remark'),
                            })
                        "
                    />
                    <FormItem
                        :label="t('routine.plantask.weigh')"
                        type="number"
                        v-model="baTable.form.items!.weigh"
                        prop="weigh"
                        :input-attr="{
                            disabled: readOnly,
                        }"
                        :placeholder="
                            t('Please input field', {
                                field: t('routine.plantask.weigh'),
                            })
                        "
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template v-if="!readOnly" #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm()">{{ t('Cancel') }}</el-button>
                <el-button v-blur :loading="baTable.form.submitLoading" @click="baTable.onSubmit(formRef)" type="primary">
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
        <el-dialog title="未来10次执行时间" :model-value="showCronRuleRes" width="500px" style="height: 400px;" @close="showCronRuleRes = false">
            <div style="font-size: 15px;padding: 10px;line-height: 30px;">
                <template v-for="(item, index) in runtimeList">
                    <p>{{ item }}</p>
                </template>
            </div>
        </el-dialog>
    </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage, type FormInstance } from 'element-plus'
import { inject, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import type baTableClass from '/@/utils/baTable'
import { QuestionFilled } from '@element-plus/icons-vue'
import { checkCronRuleApi } from '/@/api/backend/routine/plantask'

const config = useConfig()
const formRef = ref<FormInstance>()
const baTable = inject('baTable') as baTableClass

const options = ref({
    repeat_times_type: 0,
    rule_type: 'seconds',
    cron_rule: ['*', '*', '*', '*', '*'],
    run_time_type: 'now',
})

const showCrontabRules = ref(false)

const zeroFill = (n: number) => {
    return n < 10 ? '0' + n : n
}

const handleDateFormat = (date: Date) => {
    if (date) {
        let time = new Date(date)
        let Year = time.getFullYear() // 年
        let Month = zeroFill(time.getMonth() + 1) //月
        let Day = zeroFill(time.getDate()) // 日
        // let Week = time.getDay() // 周
        let hour = zeroFill(time.getHours()) // 时
        let minute = zeroFill(time.getMinutes()) // 分
        let second = zeroFill(time.getSeconds()) // 秒
        return Year + '-' + Month + '-' + Day + ' ' + hour + ':' + minute + ':' + second
    } else {
        return ''
    }
}

const checkCronRuleloading = ref(false)
const showCronRuleRes = ref(false)
const runtimeList = ref([])
const checkCronRule = () => {
    const rule = options.value.cron_rule.join(' ')
    checkCronRuleloading.value = true
    checkCronRuleApi(rule)
        .then((res) => {
            runtimeList.value = res.data.runtime_list
            showCronRuleRes.value = true
        })
        .finally(() => {
            checkCronRuleloading.value = false
        })
        .catch(() => {
            checkCronRuleloading.value = false
        })
}

//只读
const readOnly = ref(false)
baTable.before.toggleForm = ({ operate }) => {
    if (operate === 'Add') {
        baTable.form.defaultItems!.type = 'method'
        baTable.form.defaultItems!.run_time = handleDateFormat(new Date())
        baTable.form.defaultItems!.rule = 60
        options.value = {
            repeat_times_type: 0,
            rule_type: 'seconds',
            cron_rule: ['*', '*', '*', '*', '*'],
            run_time_type: 'now',
        }
        readOnly.value = false
    }
}

baTable.after.requestEdit = ({ res }) => {
    options.value.repeat_times_type = res?.data?.row?.repeat_times_type
    options.value.rule_type = res?.data?.row?.rule_type
    options.value.cron_rule = res?.data?.row?.cron_rule ? res?.data?.row?.cron_rule : ['*', '*', '*', '*', '*']
    options.value.run_time_type = 'customerTime'
    if ([0, 1, 2, 10].includes(res.data.row.status)) {
        readOnly.value = true
    } else {
        readOnly.value = false
    }
}

baTable.before.onSubmit = ({ items }) => {
    let params = <any>{}

    //编辑
    if (items.task_id) {
        params.task_id = items.task_id
    }

    //任务名称
    params.task_name = items.task_name

    //任务类型
    if (!items.type || !['method', 'url', 'sql'].includes(items.type)) {
        ElMessage.error('任务类型有误，只能为method、url、sql')
        return false
    }
    params.type = items.type

    //执行目标
    if (!items.goal || items.goal == '') {
        ElMessage.error(items.type + '不能为空')
        return false
    }
    params.goal = items.goal

    //参数
    params.params = items.params

    //重复次数
    switch (options.value.repeat_times_type) {
        case 0: //不限次数
            params.repeat_times = 0
            break
        case 1: //单次执行
            params.repeat_times = 1
            break
        case 2: //指定次数
            if (!items.repeat_times || items.repeat_times == '' || items.repeat_times <= 0) {
                ElMessage.error('执行次数有误')
                return false
            }
            params.repeat_times = items.repeat_times
    }

    //重复周期
    switch (options.value.rule_type) {
        case 'seconds': //固定间隔时长
            if (!items.rule || items.rule == '' || items.rule <= 0) {
                ElMessage.error('间隔时间有误')
                return false
            }
            params.rule = items.rule
            break
        case 'crontab': //Crontab规则
            if (options.value.cron_rule.length < 5) {
                ElMessage.error('Crontab规则有误')
                return false
            }
            params.rule = options.value.cron_rule.join(' ')
            break
    }

    //执行时间
    switch (options.value.run_time_type) {
        case 'now': //立即执行
            params.run_time = handleDateFormat(new Date())
            break
        case 'customerTime': //自定义时间
            if (!items.run_time || items.run_time == '') {
                ElMessage.error('执行时间有误')
                return false
            }
            params.run_time = handleDateFormat(items.run_time)
    }
    params.remark = items.remark
    params.weigh = items.weigh

    baTable.form.items = params
}

const { t } = useI18n()
</script>

<style scoped lang="scss">
.cron-instructions {
    color: #888;
    text-align: center;
    line-height: 20px;
}
.cron-instructions-string {
    line-height: 8px;
}
.cron-instructions-text {
    text-align: left;
    margin-left: -25px;
}
.symbol {
    font-size: 16px;
    font-weight: 700;
    margin-right: 15px;
}
</style>
