<template>
    <div class="data-center-home">
        <!-- 导航栏 -->
        <nav class="navbar" id="navbar">
            <div class="nav-container">
                <div class="logo">{{ siteConfig.siteName || 'DataGather.cm' }}</div>
                <ul class="nav-menu" id="navMenu">
                    <li><a @click="scrollToSection('home')" class="nav-link active">首页</a></li>
                    <li><a @click="scrollToSection('services')" class="nav-link">服务</a></li>
                    <li><a @click="scrollToSection('features')" class="nav-link">特色</a></li>
                    <li><a @click="scrollToSection('contact')" class="nav-link">联系</a></li>
                    <li v-if="memberCenter.state.open">
                        <a @click="$router.push(memberCenterBaseRoutePath)" class="nav-link">会员中心</a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- 主要横幅区域 -->
        <section class="hero-section" id="home">
            <div class="hero-background">
                <div class="data-particles"></div>
                <div class="grid-overlay"></div>
            </div>
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        <span class="title-main">{{ siteConfig.siteName || 'DataGather.cm' }}</span>
                        <span class="title-sub">专业数据采集中心</span>
                    </h1>
                    <p class="hero-description">
                        高效、准确、实时的数据采集与处理平台<br>
                        为企业提供全方位的数据服务解决方案
                    </p>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number">99.9%</div>
                            <div class="stat-label">数据准确率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">1000+</div>
                            <div class="stat-label">服务客户</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">24/7</div>
                            <div class="stat-label">实时监控</div>
                        </div>
                    </div>
                    <div class="hero-actions">
                        <a @click="scrollToSection('services')" class="btn-primary">
                            <span>开始使用</span>
                            <span>→</span>
                        </a>
                        <a @click="scrollToSection('features')" class="btn-secondary">
                            <span>了解更多</span>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 服务展示区域 -->
        <section class="services-section" id="services">
            <div class="container">
                <h2 class="section-title">核心服务</h2>
                <p class="section-subtitle">专业的数据采集与处理服务，满足您的各种数据需求</p>
                <div class="services-grid">
                    <div class="service-card">
                        <div class="service-icon">📊</div>
                        <h3 class="service-title">数据采集</h3>
                        <p class="service-description">
                            高效的数据采集系统，支持多种数据源，实时采集各类业务数据
                        </p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">⚡</div>
                        <h3 class="service-title">实时处理</h3>
                        <p class="service-description">
                            强大的数据处理引擎，实时清洗、转换和分析数据，确保数据质量
                        </p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">📈</div>
                        <h3 class="service-title">数据分析</h3>
                        <p class="service-description">
                            智能数据分析平台，提供深度洞察和预测分析，助力业务决策
                        </p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">🔒</div>
                        <h3 class="service-title">安全保障</h3>
                        <p class="service-description">
                            企业级安全防护，确保数据传输和存储的安全性，符合合规要求
                        </p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">🌐</div>
                        <h3 class="service-title">API接口</h3>
                        <p class="service-description">
                            丰富的API接口，支持多种编程语言，轻松集成到您的业务系统
                        </p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">📱</div>
                        <h3 class="service-title">可视化展示</h3>
                        <p class="service-description">
                            直观的数据可视化界面，多维度展示数据，让数据价值一目了然
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 特色功能区域 -->
        <section class="features-section" id="features">
            <div class="container">
                <h2 class="section-title">技术特色</h2>
                <p class="section-subtitle">先进的技术架构，为您提供稳定可靠的数据服务</p>
                <div class="features-grid">
                    <div class="feature-item">
                        <div class="feature-header">
                            <div class="feature-icon">🚀</div>
                            <h3 class="feature-title">高性能架构</h3>
                        </div>
                        <p class="feature-description">
                            采用分布式微服务架构，支持高并发数据处理，确保系统稳定运行
                        </p>
                        <ul class="feature-list">
                            <li>分布式集群部署</li>
                            <li>负载均衡优化</li>
                            <li>自动扩缩容</li>
                            <li>故障自动恢复</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <div class="feature-header">
                            <div class="feature-icon">🛡️</div>
                            <h3 class="feature-title">安全防护</h3>
                        </div>
                        <p class="feature-description">
                            多层安全防护体系，保障数据传输和存储的绝对安全
                        </p>
                        <ul class="feature-list">
                            <li>端到端加密传输</li>
                            <li>访问权限控制</li>
                            <li>数据脱敏处理</li>
                            <li>安全审计日志</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <div class="feature-header">
                            <div class="feature-icon">⚡</div>
                            <h3 class="feature-title">实时处理</h3>
                        </div>
                        <p class="feature-description">
                            毫秒级数据处理响应，支持实时流数据分析和处理
                        </p>
                        <ul class="feature-list">
                            <li>流式数据处理</li>
                            <li>实时数据清洗</li>
                            <li>智能异常检测</li>
                            <li>实时告警通知</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <div class="feature-header">
                            <div class="feature-icon">🔧</div>
                            <h3 class="feature-title">灵活集成</h3>
                        </div>
                        <p class="feature-description">
                            丰富的API接口和SDK，支持多种开发语言和框架集成
                        </p>
                        <ul class="feature-list">
                            <li>RESTful API接口</li>
                            <li>多语言SDK支持</li>
                            <li>Webhook回调</li>
                            <li>自定义数据格式</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <div class="feature-header">
                            <div class="feature-icon">📊</div>
                            <h3 class="feature-title">智能监控</h3>
                        </div>
                        <p class="feature-description">
                            全方位的系统监控和数据质量管理，确保服务稳定运行
                        </p>
                        <ul class="feature-list">
                            <li>实时性能监控</li>
                            <li>数据质量检测</li>
                            <li>异常自动告警</li>
                            <li>可视化监控面板</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <div class="feature-header">
                            <div class="feature-icon">🌟</div>
                            <h3 class="feature-title">智能优化</h3>
                        </div>
                        <p class="feature-description">
                            基于AI的智能优化引擎，自动调优采集策略和处理流程
                        </p>
                        <ul class="feature-list">
                            <li>智能采集调度</li>
                            <li>自适应负载均衡</li>
                            <li>机器学习优化</li>
                            <li>预测性维护</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系区域 -->
        <section class="contact-section" id="contact">
            <div class="container">
                <div class="contact-content">
                    <h2 class="contact-title">联系我们</h2>
                    <p class="contact-description">
                        专业的技术团队为您提供7×24小时技术支持服务
                    </p>
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-item-icon">📧</div>
                            <div class="contact-item-text"><EMAIL></div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-item-icon">📞</div>
                            <div class="contact-item-text">400-888-8888</div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-item-icon">💬</div>
                            <div class="contact-item-text">在线客服</div>
                        </div>
                    </div>
                    <div class="hero-actions">
                        <a v-if="memberCenter.state.open" @click="$router.push(memberCenterBaseRoutePath)" class="btn-primary">
                            <span>进入控制台</span>
                            <span>→</span>
                        </a>
                        <a @click="scrollToSection('services')" class="btn-secondary">
                            <span>查看文档</span>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="container">
                <div class="footer-links">
                    <a href="#" class="footer-link">服务条款</a>
                    <a href="#" class="footer-link">隐私政策</a>
                    <a href="#" class="footer-link">帮助中心</a>
                    <a href="#" class="footer-link">开发者文档</a>
                </div>
                <div class="footer-content">
                    <p>&copy; 2025 {{ siteConfig.siteName || 'DataGather.cm' }}. 专业数据采集中心 - 让数据创造价值</p>
                    <p v-if="siteConfig.recordNumber">{{ siteConfig.recordNumber }}</p>
                </div>
            </div>
        </footer>
    </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useSiteConfig } from '/@/stores/siteConfig'
import { useMemberCenter } from '/@/stores/memberCenter'
import { memberCenterBaseRoutePath } from '/@/router/static/memberCenterBase'
import { initialize } from '/@/api/frontend/index'

const siteConfig = useSiteConfig()
const memberCenter = useMemberCenter()

// 平滑滚动到指定区域
const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        })

        // 更新导航栏活动状态
        updateActiveNavLink(sectionId)
    }
}

// 更新导航栏活动状态
const updateActiveNavLink = (activeId: string) => {
    // 移除所有活动状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active')
    })

    // 根据section ID找到对应的导航链接并添加活动状态
    const navLinks = document.querySelectorAll('.nav-link')
    const sectionMap: { [key: string]: number } = {
        'home': 0,
        'services': 1,
        'features': 2,
        'contact': 3
    }

    const linkIndex = sectionMap[activeId]
    if (linkIndex !== undefined && navLinks[linkIndex]) {
        navLinks[linkIndex].classList.add('active')
    }
}

// 导入样式和脚本
import '/@/assets/DataCenterHome/style.css'

// 加载JavaScript和初始化配置
onMounted(() => {
    // 初始化站点配置
    initialize()

    // 动态导入JavaScript模块
    import('/@/assets/DataCenterHome/script.js')
        .then(() => {
            console.log('数据采集中心首页脚本加载完成')
        })
        .catch(error => {
            console.error('脚本加载失败:', error)
        })

    // 监听滚动事件，自动更新导航栏状态
    window.addEventListener('scroll', handleScroll)
})

// 处理滚动事件
const handleScroll = () => {
    const sections = ['home', 'services', 'features', 'contact']
    const scrollPosition = window.scrollY + 100 // 偏移量

    for (const sectionId of sections) {
        const element = document.getElementById(sectionId)
        if (element) {
            const { offsetTop, offsetHeight } = element
            if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
                updateActiveNavLink(sectionId)
                break
            }
        }
    }
}

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped lang="scss">
/* 数据采集中心首页 - Vue组件样式 */
.data-center-home {
    /* 基础样式由外部CSS文件提供 */
    min-height: 100vh;

    /* 确保导航链接的点击事件正常工作 */
    .nav-link {
        cursor: pointer;
        user-select: none;
    }

    /* Vue特定的响应式调整 */
    @media (max-width: 768px) {
        .hero-stats {
            flex-direction: column;
            gap: 20px;
        }

        .services-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
    }
}

/* 确保组件在Vue环境中的正确显示 */
:deep(.navbar) {
    z-index: 9999;
}

:deep(.hero-section) {
    position: relative;
    z-index: 1;
}
</style>
