import createAxios from '/@/utils/axios'

export const url = '/admin/sam.dashboard/'

/**
 * 获取山姆会员商店统计数据
 */
export function index() {
    return createAxios({
        url: url + 'index',
        method: 'get',
    })
}

/**
 * 获取商品数量趋势数据
 */
export function goodsTrend() {
    return createAxios({
        url: url + 'goodsTrend',
        method: 'get',
    })
}

/**
 * 获取门店商品分布数据
 */
export function storeDistribution() {
    return createAxios({
        url: url + 'storeDistribution',
        method: 'get',
    })
}

/**
 * 获取商品分类占比数据
 */
export function categoryDistribution() {
    return createAxios({
        url: url + 'categoryDistribution',
        method: 'get',
    })
}

/**
 * 获取门店地区分布数据
 */
export function storeRegion() {
    return createAxios({
        url: url + 'storeRegion',
        method: 'get',
    })
}

/**
 * 获取最近活动数据
 */
export function recentActivities() {
    return createAxios({
        url: url + 'recentActivities',
        method: 'get',
    })
}
