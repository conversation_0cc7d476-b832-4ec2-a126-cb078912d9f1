import createAxios from '/@/utils/axios'

export const url = '/admin/routine.Plantask/'
export function getTasklogApi(queryParams: any) {
    return createAxios({
        url: url + 'getTasklog',
        method: 'post',
        data: queryParams,
    })
}

export function deleteTaskApi(queryParams: any) {
    return createAxios(
        {
            url: url + 'deleteTask',
            method: 'post',
            data: queryParams,
        },
        {
            showSuccessMessage: true,
        }
    )
}

export function downloadLogApi(queryParams: any) {
    return createAxios(
        {
            url: url + 'downloadLog',
            method: 'post',
            responseType: 'blob',
            data: queryParams,
        },
        {
            showSuccessMessage: true,
            reductDataFormat: false,
        }
    )
}

export function checkCronRuleApi(rule: string) {
    return createAxios(
        {
            url: url + 'checkCronRule',
            method: 'post',
            data: { rule },
        },
        {
            showErrorMessage: true,
        }
    )
}

export function executeOnce(task_id: number) {
    return createAxios(
        {
            url: url + 'executeOnce',
            method: 'post',
            data: { task_id },
        },
        {
            showSuccessMessage: true,
            showErrorMessage: true,
        }
    )
}
