/* 基本样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    outline: none !important;
}

html,
body,
#app {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    font-family:
        Helvetica Neue,
        Helvetica,
        PingFang SC,
        Hiragino Sans GB,
        Microsoft YaHei,
        SimSun,
        sans-serif;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -webkit-tap-highlight-color: transparent;
    background-color: var(--ba-bg-color);
    color: var(--el-text-color-primary);
    font-size: var(--el-font-size-base);
}

// 阿里 iconfont Symbol引用css
.iconfont-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

.w100 {
    width: 100% !important;
}
.h100 {
    height: 100% !important;
}
.ba-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.default-main {
    margin: var(--ba-main-space) var(--ba-main-space) 60px var(--ba-main-space);
}
.zoom-handle {
    position: absolute;
    width: 20px;
    height: 20px;
    bottom: -10px;
    right: -10px;
    cursor: se-resize;
}
.block-help {
    display: block;
    width: 100%;
    color: #909399;
    font-size: 13px;
    line-height: 16px;
    padding-top: 5px;
}

/* 表格顶部菜单-s */
.table-header {
    .table-header-operate .icon {
        font-size: 14px !important;
        color: var(--el-color-white) !important;
    }
    .el-button.is-disabled .icon {
        color: var(--el-button-disabled-text-color) !important;
    }
}
/* 表格顶部菜单-e */

/* 鼠标置入浮动效果-s */
.suspension {
    transition: all 0.3s ease;
}
.suspension:hover {
    -webkit-transform: translateY(-4px) scale(1.02);
    -moz-transform: translateY(-4px) scale(1.02);
    -ms-transform: translateY(-4px) scale(1.02);
    -o-transform: translateY(-4px) scale(1.02);
    transform: translateY(-4px) scale(1.02);
    -webkit-box-shadow: 0 14px 24px rgba(0, 0, 0, 0.2);
    box-shadow: 0 14px 24px rgba(0, 0, 0, 0.2);
    z-index: 999;
    border-radius: 6px;
}
/* 鼠标置入浮动效果-e */

/* 表格-s */
.ba-table-box {
    border-radius: var(--el-border-radius-round);
}
.ba-table-alert {
    background-color: var(--el-fill-color-darker) !important;
    border: 1px solid var(--ba-boder-color);
    border-bottom: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
/* 表格-e */

/* 新增/编辑表单-s */
.ba-operate-dialog {
    overflow: hidden;
    border-radius: var(--el-border-radius-base);
    padding-bottom: 52px;
}
.ba-operate-dialog .el-dialog__header {
    border-bottom: 1px solid var(--ba-bg-color);
    .el-dialog__headerbtn {
        top: 4px;
    }
}
.ba-operate-dialog .el-dialog__body {
    height: 58vh;
}
.ba-operate-dialog .el-dialog__footer {
    padding: 10px var(--el-dialog-padding-primary);
    box-shadow: var(--el-box-shadow);
    position: absolute;
    width: 100%;
    bottom: 0;
    left: 0;
}
.ba-operate-form {
    padding-top: 20px;
}
/* 新增/编辑表单-e */

/* 全局遮罩-s */
.ba-layout-shade {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100vw;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999990;
}
/* 全局遮罩-e */

/* 图片上传预览-s */
.img-preview-dialog .el-dialog__body {
    display: flex;
    align-items: center;
    justify-content: center;
    img {
        max-width: 100%;
    }
}
/* 图片上传预览-e */

/* 页面切换动画-s */
.slide-right-enter-active,
.slide-right-leave-active,
.slide-left-enter-active,
.slide-left-leave-active {
    will-change: transform;
    transition: all 0.3s ease;
}
// slide-right
.slide-right-enter-from {
    opacity: 0;
    transform: translateX(-20px);
}
.slide-right-leave-to {
    opacity: 0;
    transform: translateX(20px);
}
// slide-left
.slide-left-enter-from {
    @extend .slide-right-leave-to;
}
.slide-left-leave-to {
    @extend .slide-right-enter-from;
}
/* 页面切换动画-e */

/* 布局相关-s */
.frontend-footer-brother {
    min-height: calc(100vh - 120px);
}
.user-views {
    padding-left: 15px;
    .user-views-card {
        margin-bottom: 15px;
    }
}
.ba-aside-drawer {
    .el-drawer__body {
        padding: 0;
    }
}
/* 布局相关-e */

/* 暗黑模式公共样式-s */
.ba-icon-dark {
    color: var(--el-text-color-primary) !important;
}
/* 暗黑模式公共样式-e */

/* NProgress-s */
#nprogress {
    .bar,
    .spinner {
        z-index: 999999;
    }
}
/* NProgress-e */

/* 自适应-s */
@media screen and (max-width: 768px) {
    .xs-hidden {
        display: none;
    }
}
@media screen and (max-width: 1024px) {
    .ba-operate-dialog {
        width: 96%;
    }
}
@media screen and (max-width: 991px) {
    .user-views {
        padding: 0;
    }
}
/* 自适应-e */
