/* 修复 Chrome 浏览器输入框内选中字符行高异常的bug-s */
.el-input .el-input__inner {
    line-height: calc(var(--el-input-height, 40px) - 4px);
}
/* 修复 Chrome 浏览器输入框内选中字符行高异常的bug-e */

.datetime-picker {
    height: 32px;
    padding-top: 0;
    padding-bottom: 0;
}
.el-divider__text.is-center {
    transform: translateX(-50%) translateY(-62%);
}

.el-menu {
    user-select: none;
    .el-sub-menu__title:hover {
        background-color: var(--el-color-primary-light-9) !important;
    }
}

.el-table {
    --el-table-border-color: var(--ba-border-color);
}

.el-card {
    border: none;
}
.el-card__header {
    border-bottom: 1px solid var(--el-border-color-extra-light);
}
.el-textarea__inner {
    padding: 5px 11px;
}

/* dialog滚动条-s */
.el-overlay-dialog,
.ba-scroll-style {
    &::-webkit-scrollbar {
        width: 5px;
        height: 5px;
    }
    &::-webkit-scrollbar-thumb {
        background: #eaeaea;
        border-radius: var(--el-border-radius-base);
        box-shadow: none;
        -webkit-box-shadow: none;
    }
    &:hover {
        &::-webkit-scrollbar-thumb:hover {
            background: #c8c9cc;
        }
    }
}
@supports not (selector(::-webkit-scrollbar)) {
    .el-overlay-dialog,
    .ba-scroll-style {
        scrollbar-width: thin;
        scrollbar-color: #c8c9cc #eaeaea;
    }
}
/* dialog滚动条-e */

/* 小屏设备 el-radio-group 样式优化-s */
.ba-input-item-radio {
    margin-bottom: 10px;
    .el-radio-group {
        .el-radio {
            margin-bottom: 8px;
        }
    }
}
/* 小屏设备 el-radio-group 样式调整-e */
