// 数据采集中心首页交互脚本

class DataCenterHome {
    constructor() {
        this.init();
    }

    init() {
        this.setupNavigation();
        this.setupAnimations();
        this.setupCounters();
        this.setupParticles();
        this.setupScrollEffects();
        this.setupDataStream();
        this.setupInteractiveEffects();
        this.setupPerformanceOptimization();
    }

    // 导航栏交互
    setupNavigation() {
        const navbar = document.querySelector('.navbar');
        const navLinks = document.querySelectorAll('.nav-link');

        // 滚动时改变导航栏样式
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(10, 14, 39, 0.98)';
                navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.3)';
            } else {
                navbar.style.background = 'rgba(10, 14, 39, 0.95)';
                navbar.style.boxShadow = 'none';
            }
        });

        // 注意：导航链接的点击事件由Vue组件处理
        // 这里只处理非Vue管理的链接（如果有的话）
        const nonVueLinks = document.querySelectorAll('a[href^="#"]:not([onclick]):not([v-on\\:click]):not([@click])');
        nonVueLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // 设置动画效果
    setupAnimations() {
        // 页面加载动画
        const heroContent = document.querySelector('.hero-content');
        if (heroContent) {
            heroContent.style.opacity = '0';
            heroContent.style.transform = 'translateY(50px)';

            setTimeout(() => {
                heroContent.style.transition = 'all 1s ease-out';
                heroContent.style.opacity = '1';
                heroContent.style.transform = 'translateY(0)';
            }, 300);
        }

        // 服务卡片悬停效果
        const serviceCards = document.querySelectorAll('.service-card');
        serviceCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    // 数字计数动画
    setupCounters() {
        const counters = document.querySelectorAll('.stat-number');
        const animateCounter = (counter) => {
            const target = parseFloat(counter.textContent);
            const increment = target / 100;
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                if (counter.textContent.includes('%')) {
                    counter.textContent = current.toFixed(1) + '%';
                } else if (counter.textContent.includes('+')) {
                    counter.textContent = Math.floor(current) + '+';
                } else if (counter.textContent.includes('/')) {
                    counter.textContent = counter.textContent; // 保持原样，如 24/7
                } else {
                    counter.textContent = Math.floor(current);
                }
            }, 20);
        };

        // 使用 Intersection Observer 来触发动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        });

        counters.forEach(counter => {
            observer.observe(counter);
        });
    }

    // 粒子效果
    setupParticles() {
        const particlesContainer = document.querySelector('.data-particles');
        if (!particlesContainer) return;

        // 创建动态粒子
        for (let i = 0; i < 50; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: 2px;
                height: 2px;
                background: #64ffda;
                border-radius: 50%;
                opacity: ${Math.random() * 0.5 + 0.2};
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: particleFloat ${Math.random() * 10 + 5}s linear infinite;
            `;
            particlesContainer.appendChild(particle);
        }

        // 添加粒子动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes particleFloat {
                0% {
                    transform: translateY(100vh) translateX(0);
                    opacity: 0;
                }
                10% {
                    opacity: 1;
                }
                90% {
                    opacity: 1;
                }
                100% {
                    transform: translateY(-100px) translateX(${Math.random() * 200 - 100}px);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 滚动效果
    setupScrollEffects() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                }
            });
        }, observerOptions);

        // 观察所有需要动画的元素
        const animatedElements = document.querySelectorAll('.service-card, .section-title, .section-subtitle, .feature-item');
        animatedElements.forEach(el => {
            observer.observe(el);
        });
    }

    // 设置数据流效果
    setupDataStream() {
        this.createDataStream();
    }

    // 添加数据流效果
    createDataStream() {
        const streamContainer = document.createElement('div');
        streamContainer.className = 'data-stream';
        streamContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
            overflow: hidden;
        `;

        // 创建数据流线条
        for (let i = 0; i < 15; i++) {
            const line = document.createElement('div');
            line.style.cssText = `
                position: absolute;
                width: 1px;
                height: 100px;
                background: linear-gradient(to bottom, transparent, #64ffda, transparent);
                left: ${Math.random() * 100}%;
                animation: dataFlow ${Math.random() * 3 + 2}s linear infinite;
                animation-delay: ${Math.random() * 2}s;
            `;
            streamContainer.appendChild(line);
        }

        document.body.appendChild(streamContainer);

        // 添加数据流动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes dataFlow {
                0% {
                    transform: translateY(-100px);
                    opacity: 0;
                }
                50% {
                    opacity: 1;
                }
                100% {
                    transform: translateY(100vh);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 设置交互效果
    setupInteractiveEffects() {
        // 鼠标跟随效果
        this.setupMouseFollower();

        // 特色卡片交互
        this.setupFeatureCards();

        // 按钮悬停效果
        this.setupButtonEffects();
    }

    // 鼠标跟随效果
    setupMouseFollower() {
        const follower = document.createElement('div');
        follower.className = 'mouse-follower';
        follower.style.cssText = `
            position: fixed;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #64ffda, transparent);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        document.body.appendChild(follower);

        document.addEventListener('mousemove', (e) => {
            follower.style.left = e.clientX - 10 + 'px';
            follower.style.top = e.clientY - 10 + 'px';
            follower.style.opacity = '0.6';
        });

        document.addEventListener('mouseleave', () => {
            follower.style.opacity = '0';
        });
    }

    // 特色卡片交互
    setupFeatureCards() {
        const featureCards = document.querySelectorAll('.feature-item');
        featureCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
                card.style.boxShadow = '0 25px 50px rgba(100, 255, 218, 0.2)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
                card.style.boxShadow = '0 25px 50px rgba(100, 255, 218, 0.15)';
            });
        });
    }

    // 按钮效果
    setupButtonEffects() {
        const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', () => {
                button.style.transform = 'translateY(-3px) scale(1.05)';
            });

            button.addEventListener('mouseleave', () => {
                button.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    // 性能优化
    setupPerformanceOptimization() {
        // 使用 requestAnimationFrame 优化动画
        this.optimizeAnimations();

        // 懒加载优化
        this.setupLazyLoading();
    }

    // 优化动画性能
    optimizeAnimations() {
        // 减少不必要的重绘
        const animatedElements = document.querySelectorAll('.data-particles, .grid-overlay');
        animatedElements.forEach(element => {
            element.style.willChange = 'transform';
        });
    }

    // 懒加载设置
    setupLazyLoading() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '50px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('loaded');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // 观察需要懒加载的元素
        const lazyElements = document.querySelectorAll('.service-card, .feature-item');
        lazyElements.forEach(el => {
            observer.observe(el);
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new DataCenterHome();
});

// 添加一些实用工具函数
const utils = {
    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    },

    // 防抖函数
    debounce(func, delay) {
        let timeoutId;
        return function() {
            const args = arguments;
            const context = this;
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(context, args), delay);
        }
    },

    // 随机数生成
    random(min, max) {
        return Math.random() * (max - min) + min;
    }
};

// 导出工具函数供其他脚本使用
window.DataCenterUtils = utils;
