/* 数据采集中心首页样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

    color: #ffffff;
    overflow-x: hidden;
    background: #0a0e27;
}

/* 主容器 */
.data-center-home {
    min-height: 100vh;
    background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #2d3561 100%);
    position: relative;
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(10, 14, 39, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.logo {
    font-size: 24px;
    font-weight: 700;
    color: #64ffda;
    text-decoration: none;
    letter-spacing: -0.5px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #64ffda;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #64ffda;
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* 主横幅区域 */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.data-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="%2364ffda" opacity="0.3"><animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/></circle><circle cx="80" cy="40" r="1" fill="%2364ffda" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite"/></circle><circle cx="40" cy="80" r="1" fill="%2364ffda" opacity="0.4"><animate attributeName="opacity" values="0.4;1;0.4" dur="2.5s" repeatCount="indefinite"/></circle></svg>') repeat;
    animation: float 20s linear infinite;
}

.grid-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(100, 255, 218, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(100, 255, 218, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 1000px;
    padding: 0 20px;
}

.hero-title {
    margin-bottom: 30px;
}

.title-main {
    display: block;
    font-size: 4rem;
    font-weight: 800;
    color: #64ffda;
    margin-bottom: 10px;
    letter-spacing: -2px;
    text-shadow: 0 0 30px rgba(100, 255, 218, 0.5);
}

.title-sub {
    display: block;
    font-size: 2rem;
    font-weight: 300;
    color: #ffffff;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 50px;
    line-height: 1.8;
}

/* 统计数据 */
.hero-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-bottom: 50px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #64ffda;
    margin-bottom: 10px;
    text-shadow: 0 0 20px rgba(100, 255, 218, 0.5);
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

/* 服务按钮 */
.hero-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.btn-primary,
.btn-secondary {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.btn-primary {
    background: linear-gradient(135deg, #64ffda 0%, #4fd1c7 100%);
    color: #0a0e27;
    box-shadow: 0 10px 30px rgba(100, 255, 218, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(100, 255, 218, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #64ffda;
    border: 2px solid #64ffda;
}

.btn-secondary:hover {
    background: #64ffda;
    color: #0a0e27;
    transform: translateY(-2px);
}

/* 服务展示区域 */
.services-section {
    padding: 100px 0;
    background: rgba(26, 31, 58, 0.5);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 20px;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 60px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-card {
    background: rgba(45, 53, 97, 0.3);
    border: 1px solid rgba(100, 255, 218, 0.2);
    border-radius: 12px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: rgba(100, 255, 218, 0.5);
    box-shadow: 0 20px 40px rgba(100, 255, 218, 0.1);
}

.service-icon {
    font-size: 3rem;
    color: #64ffda;
    margin-bottom: 20px;
}

.service-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 15px;
}

.service-description {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
}

/* 动画效果 */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
    100% { transform: translateY(0px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

/* 特色功能区域 */
.features-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #1a1f3a 0%, #2d3561 100%);
    position: relative;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%2364ffda" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
    position: relative;
    z-index: 2;
}

.feature-item {
    background: rgba(10, 14, 39, 0.8);
    border: 1px solid rgba(100, 255, 218, 0.3);
    border-radius: 16px;
    padding: 30px;
    transition: all 0.4s ease;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.1), transparent);
    transition: left 0.6s ease;
}

.feature-item:hover::before {
    left: 100%;
}

.feature-item:hover {
    transform: translateY(-5px);
    border-color: rgba(100, 255, 218, 0.6);
    box-shadow: 0 25px 50px rgba(100, 255, 218, 0.15);
}

.feature-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.feature-icon {
    font-size: 2.5rem;
    margin-right: 20px;
    color: #64ffda;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
}

.feature-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    margin-bottom: 20px;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #64ffda;
    font-weight: bold;
}

/* 联系区域 */
.contact-section {
    padding: 80px 0;
    background: rgba(10, 14, 39, 0.95);
    text-align: center;
}

.contact-content {
    max-width: 600px;
    margin: 0 auto;
}

.contact-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 20px;
}

.contact-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 40px;
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
}

.contact-item {
    text-align: center;
}

.contact-item-icon {
    font-size: 2rem;
    color: #64ffda;
    margin-bottom: 10px;
}

.contact-item-text {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* 页脚 */
.footer {
    background: #0a0e27;
    padding: 40px 0;
    text-align: center;
    border-top: 1px solid rgba(100, 255, 218, 0.2);
}

.footer-content {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
}

.footer-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #64ffda;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 30px;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .nav-menu {
        display: none;
    }

    .contact-info {
        flex-direction: column;
        gap: 20px;
    }

    .footer-links {
        flex-direction: column;
        gap: 15px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .feature-item {
        padding: 25px;
    }

    .feature-header {
        flex-direction: column;
        text-align: center;
        margin-bottom: 15px;
    }

    .feature-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
}

/* 中等屏幕优化 */
@media (min-width: 769px) and (max-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 35px;
    }
}
