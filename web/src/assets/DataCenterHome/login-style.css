/* 顶级设计师作品 - 极致美学登录页面 */

/* 重置与基础 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-border: rgba(255, 255, 255, 0.18);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.12);
    --shadow-medium: 0 16px 64px rgba(0, 0, 0, 0.16);
    --shadow-strong: 0 24px 96px rgba(0, 0, 0, 0.24);
    --border-radius: 24px;
    --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 主容器 - 沉浸式体验 */
.modern-login-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--primary-gradient);
    overflow: hidden;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    perspective: 1000px;
}

/* 顶级语言切换器 - 悬浮设计 */
.language-switcher {
    position: fixed;
    top: 32px;
    right: 32px;
    z-index: 1000;
}

.language-btn {
    width: 56px;
    height: 56px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-soft);
    position: relative;
    overflow: hidden;
}

.language-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.language-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-medium);
}

.language-btn:hover::before {
    transform: translateX(100%);
}

/* 极致背景艺术 - 多层次视觉体验 */
.background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

/* 动态网格系统 */
.dynamic-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 60px 60px, 40px 40px;
    animation: gridMove 20s linear infinite;
    opacity: 0.6;
}

/* 浮动几何体 - 更精致的设计 */
.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
    animation: sophisticatedFloat 8s ease-in-out infinite;
}

.shape-1 {
    width: 120px;
    height: 120px;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    top: 15%;
    left: 8%;
    animation-delay: 0s;
}

.shape-2 {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    top: 65%;
    right: 12%;
    animation-delay: 2.5s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    border-radius: 20px;
    transform: rotate(45deg);
    top: 75%;
    left: 15%;
    animation-delay: 5s;
}

.shape-4 {
    width: 60px;
    height: 60px;
    border-radius: 50% 0;
    top: 25%;
    right: 25%;
    animation-delay: 1.5s;
}

/* 光晕系统 - 深度与层次 */
.gradient-orbs {
    position: absolute;
    width: 100%;
    height: 100%;
}

.orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.4;
    animation: orbPulse 6s ease-in-out infinite;
}

.orb-1 {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, #ff9a9e 0%, #fecfef 50%, transparent 100%);
    top: -200px;
    left: -200px;
    animation-delay: 0s;
}

.orb-2 {
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, #a8edea 0%, #fed6e3 50%, transparent 100%);
    bottom: -250px;
    right: -250px;
    animation-delay: 3s;
}

.orb-3 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, #d299c2 0%, #fef9d7 50%, transparent 100%);
    top: 40%;
    left: 60%;
    animation-delay: 1.5s;
}

/* 粒子系统 */
.particle-system {
    position: absolute;
    width: 100%;
    height: 100%;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: particleFloat 15s linear infinite;
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { left: 20%; animation-delay: 2s; }
.particle:nth-child(3) { left: 30%; animation-delay: 4s; }
.particle:nth-child(4) { left: 40%; animation-delay: 6s; }
.particle:nth-child(5) { left: 50%; animation-delay: 8s; }
.particle:nth-child(6) { left: 60%; animation-delay: 10s; }
.particle:nth-child(7) { left: 70%; animation-delay: 12s; }
.particle:nth-child(8) { left: 80%; animation-delay: 14s; }
.particle:nth-child(9) { left: 90%; animation-delay: 1s; }
.particle:nth-child(10) { left: 95%; animation-delay: 3s; }

/* 主要内容区域 - 极致布局设计 */
.main-content {
    position: relative;
    z-index: 2;
    display: flex;
    width: 100%;
    height: 100vh;
    align-items: center;
    justify-content: center;
    padding: 40px;
    gap: 80px;
}

/* 左侧品牌区域 - 沉浸式体验 */
.brand-section {
    flex: 1;
    max-width: 600px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 60px 40px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-medium);
    transform: perspective(1000px) rotateY(-5deg);
    transition: var(--transition);
}

.brand-section:hover {
    transform: perspective(1000px) rotateY(0deg) translateY(-10px);
    box-shadow: var(--shadow-strong);
}

.brand-content {
    color: var(--text-primary);
    text-align: left;
}

/* Logo区域 - 动态设计 */
.logo-area {
    margin-bottom: 48px;
    text-align: center;
}

.logo-icon {
    margin-bottom: 24px;
    display: flex;
    justify-content: center;
    position: relative;
}

.logo-icon svg {
    filter: drop-shadow(0 16px 32px rgba(0, 0, 0, 0.2));
    animation: logoFloat 6s ease-in-out infinite;
    transition: var(--transition);
}

.logo-icon:hover svg {
    transform: scale(1.1) rotate(10deg);
}

.brand-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    letter-spacing: -2px;
    line-height: 1.1;
}

.brand-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    font-weight: 300;
    letter-spacing: 4px;
    text-transform: uppercase;
    margin-bottom: 32px;
}

/* 品牌描述 - 层次化信息 */
.brand-description {
    text-align: left;
}

.brand-description h2 {
    font-size: 2rem;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.3;
}

.brand-description p {
    font-size: 1.125rem;
    line-height: 1.7;
    opacity: 0.85;
    margin-bottom: 32px;
}

/* 特色功能 - 卡片式设计 */
.feature-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 16px;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 24px 16px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 16px;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: var(--transition);
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-soft);
}

.feature-item:hover::before {
    opacity: 1;
}

.feature-item i {
    font-size: 1.75rem;
    margin-bottom: 4px;
    color: rgba(255, 255, 255, 0.9);
}

.feature-item span {
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    opacity: 0.9;
}

/* 右侧登录区域 - 极致精美设计 */
.login-section {
    flex: 0 0 480px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.login-card {
    width: 100%;
    max-width: 420px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 32px;
    backdrop-filter: blur(40px);
    box-shadow:
        0 32px 64px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    overflow: hidden;
    animation: cardEntrance 1.2s cubic-bezier(0.34, 1.56, 0.64, 1);
    transform: perspective(1000px) rotateY(5deg);
    transition: var(--transition);
    position: relative;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.login-card:hover {
    transform: perspective(1000px) rotateY(0deg) translateY(-8px);
    box-shadow:
        0 48px 96px rgba(0, 0, 0, 0.16),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* 卡片头部 - 优雅设计 */
.card-header {
    padding: 48px 40px 32px;
    text-align: center;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(248, 249, 250, 0.8) 100%);
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 1px;
}

.card-header h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
}

.card-header p {
    color: #64748b;
    font-size: 1rem;
    line-height: 1.6;
    font-weight: 400;
}

/* 卡片主体 */
.card-body {
    padding: 32px 40px 40px;
    background: rgba(255, 255, 255, 0.6);
}

/* 卡片底部 */
.card-footer {
    padding: 24px 40px 32px;
    background: linear-gradient(135deg,
        rgba(248, 249, 250, 0.8) 0%,
        rgba(255, 255, 255, 0.6) 100%);
    text-align: center;
    border-top: 1px solid rgba(226, 232, 240, 0.6);
}

.footer-text {
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
}

/* 表单样式 - 顶级交互设计 */
.login-form {
    width: 100%;
}

.form-group {
    margin-bottom: 28px;
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* 表单验证错误提示样式 */
.form-group .el-form-item__error,
.login-form .el-form-item__error {
    color: #e74c3c !important;
    font-size: 0.85rem !important;
    margin-top: 5px !important;
    width: 90% !important;
    text-align: left !important;
    align-self: center !important;
    padding-left: 0 !important;
    line-height: 1.4 !important;
    position: relative !important;
    left: 0 !important;
    transform: none !important;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    letter-spacing: 0.025em;
    transition: var(--transition);
    width: 90%;
    text-align: left;
}

.input-wrapper {
    position: relative;
    group: input;
    width: 90%;
}

.input-icon {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    z-index: 3;
    font-size: 20px;
    transition: var(--transition);
    pointer-events: none;
}

/* Element Plus 输入框重新设计 - 更大尺寸 */
.modern-input :deep(.el-input__wrapper) {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    padding: 20px 24px 20px 56px;
    transition: var(--transition);
    box-shadow:
        0 4px 6px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    min-height: 64px;
}

.modern-input :deep(.el-input__wrapper:hover) {
    border-color: rgba(102, 126, 234, 0.4);
    background: rgba(255, 255, 255, 0.95);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 1);
}

.modern-input :deep(.el-input__wrapper.is-focus) {
    border-color: #667eea;
    background: rgba(255, 255, 255, 1);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.1),
        0 12px 24px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

.modern-input :deep(.el-input__inner) {
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 500;
    padding-left: 0;
    line-height: 1.5;
}

.modern-input :deep(.el-input__inner::placeholder) {
    color: #9ca3af;
    font-weight: 400;
}

/* 输入框聚焦时图标变化 */
.modern-input:focus-within .input-icon {
    color: #667eea;
    transform: translateY(-50%) scale(1.1);
}

/* 密码显示按钮美化 */
.modern-input :deep(.el-input__suffix) {
    right: 18px;
}

.modern-input :deep(.el-input__suffix-inner) {
    color: #9ca3af;
    transition: var(--transition);
}

.modern-input :deep(.el-input__suffix-inner:hover) {
    color: #667eea;
}

/* 表单选项 - 精致设计 */
.form-options {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 32px;
    padding: 0 4px;
    width: 90%;
    align-self: center;
}

/* 复选框重新设计 */
.remember-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.remember-checkbox :deep(.el-checkbox__input) {
    line-height: 1;
}

.remember-checkbox :deep(.el-checkbox__inner) {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
}

.remember-checkbox :deep(.el-checkbox__inner:hover) {
    border-color: #667eea;
    background: rgba(255, 255, 255, 1);
}

.remember-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

.remember-checkbox :deep(.el-checkbox__inner::after) {
    width: 6px;
    height: 10px;
    border: 2px solid #ffffff;
    border-left: 0;
    border-top: 0;
    left: 6px;
    top: 2px;
}

.remember-checkbox :deep(.el-checkbox__label) {
    color: #4b5563;
    font-size: 0.875rem;
    font-weight: 500;
    padding-left: 0;
}

/* 忘记密码链接已移除 */

/* 提交按钮 - 极致设计 */
.submit-group {
    margin-bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
}

.login-submit-btn {
    width: 90%;
    height: 56px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 16px;
    color: #ffffff;
    font-size: 1.125rem;
    font-weight: 600;
    letter-spacing: 0.025em;
    transition: var(--transition);
    box-shadow:
        0 8px 16px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.login-submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.login-submit-btn:hover {
    transform: translateY(-3px);
    box-shadow:
        0 16px 32px rgba(102, 126, 234, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.login-submit-btn:hover::before {
    left: 100%;
}

.login-submit-btn:active {
    transform: translateY(-1px);
    transition: transform 0.1s ease;
}

.login-submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* 加载状态 */
.login-submit-btn.is-loading {
    pointer-events: none;
}

.login-submit-btn :deep(.el-loading-spinner) {
    margin-top: -8px;
}

.login-submit-btn :deep(.el-loading-spinner .circular) {
    width: 24px;
    height: 24px;
}

/* 头部区域美化 */
.head {
    background: linear-gradient(135deg, #64ffda 0%, #4fd1c7 100%);
    padding: 40px 20px 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.head::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: headShine 3s ease-in-out infinite;
}

.head img {
    display: block;
    width: 100%;
    max-width: 350px;
    margin: 0 auto;
    user-select: none;
    filter: brightness(1.1);
}

.login-title {
    color: #0a0e27;
    font-size: 1.8rem;
    font-weight: 700;
    margin-top: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.login-subtitle {
    color: rgba(10, 14, 39, 0.8);
    font-size: 1rem;
    margin-top: 5px;
    font-weight: 500;
}

/* 头像美化 */
.profile-avatar {
    display: block;
    position: absolute;
    height: 100px;
    width: 100px;
    border-radius: 50%;
    border: 4px solid rgba(100, 255, 218, 0.8);
    top: -50px;
    right: calc(50% - 50px);
    z-index: 3;
    user-select: none;
    box-shadow: 0 10px 30px rgba(100, 255, 218, 0.3);
    transition: all 0.3s ease;
}

.profile-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(100, 255, 218, 0.4);
}

/* 表单区域美化 */
.form {
    position: relative;
    padding: 80px 40px 40px;
}

.content {
    position: relative;
}

/* 输入框美化 */
.content :deep(.el-form-item) {
    margin-bottom: 25px;
}

.content :deep(.el-input) {
    background: transparent;
}

.content :deep(.el-input__wrapper) {
    background: rgba(45, 53, 97, 0.3);
    border: 1px solid rgba(100, 255, 218, 0.2);
    border-radius: 12px;
    box-shadow: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.content :deep(.el-input__wrapper:hover) {
    border-color: rgba(100, 255, 218, 0.4);
    box-shadow: 0 0 15px rgba(100, 255, 218, 0.1);
}

.content :deep(.el-input__wrapper.is-focus) {
    border-color: #64ffda;
    box-shadow: 0 0 20px rgba(100, 255, 218, 0.2);
}

.content :deep(.el-input__inner) {
    color: #ffffff;
    background: transparent;
}

.content :deep(.el-input__inner::placeholder) {
    color: rgba(255, 255, 255, 0.5);
}

/* 复选框美化 */
.content :deep(.el-checkbox) {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
}

.content :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: #64ffda;
    border-color: #64ffda;
}

.content :deep(.el-checkbox__inner:hover) {
    border-color: #64ffda;
}

/* 登录按钮美化 */
.submit-button {
    width: 100%;
    height: 50px;
    background: linear-gradient(135deg, #64ffda 0%, #4fd1c7 100%);
    border: none;
    border-radius: 12px;
    color: #0a0e27;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 1px;
    margin-top: 20px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(100, 255, 218, 0.3);
}

.submit-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.submit-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(100, 255, 218, 0.4);
}

.submit-button:hover::before {
    left: 100%;
}

.submit-button:active {
    transform: translateY(0);
}

/* 顶级动画系统 - 流畅自然 */

/* 网格移动动画 */
@keyframes gridMove {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(60px, 60px);
    }
}

/* 精致浮动动画 */
@keyframes sophisticatedFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-15px) rotate(2deg);
    }
    50% {
        transform: translateY(-25px) rotate(0deg);
    }
    75% {
        transform: translateY(-10px) rotate(-2deg);
    }
}

/* 光晕脉冲动画 */
@keyframes orbPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.4;
    }
    33% {
        transform: scale(1.1);
        opacity: 0.6;
    }
    66% {
        transform: scale(0.9);
        opacity: 0.3;
    }
}

/* 粒子浮动动画 */
@keyframes particleFloat {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(20px);
        opacity: 0;
    }
}

/* Logo浮动动画 */
@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-10px) rotate(5deg);
    }
}

/* 卡片入场动画 */
@keyframes cardEntrance {
    0% {
        opacity: 0;
        transform: perspective(1000px) rotateY(45deg) translateY(50px) scale(0.8);
    }
    50% {
        opacity: 0.8;
        transform: perspective(1000px) rotateY(20deg) translateY(20px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: perspective(1000px) rotateY(5deg) translateY(0px) scale(1);
    }
}

/* 表单元素入场动画 */
@keyframes formElementFadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 按钮脉冲动画 */
@keyframes buttonPulse {
    0% {
        box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);
    }
    100% {
        box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
    }
}

/* 微交互动画 */
@keyframes microBounce {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* 渐变移动动画 */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 语言下拉菜单样式 - 精致设计 */
.lang-dropdown :deep(.el-dropdown-menu) {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-medium);
    padding: 8px 0;
    margin-top: 8px;
}

.lang-dropdown :deep(.el-dropdown-menu__item) {
    justify-content: center;
    color: #374151;
    font-weight: 500;
    padding: 12px 24px;
    transition: var(--transition);
    border-radius: 8px;
    margin: 0 8px;
}

.lang-dropdown :deep(.el-dropdown-menu__item:hover) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-1px);
}

/* 表单动画延迟 */
.form-group:nth-child(1) {
    animation: formElementFadeIn 0.6s ease-out 0.2s both;
}

.form-group:nth-child(2) {
    animation: formElementFadeIn 0.6s ease-out 0.4s both;
}

.form-options {
    animation: formElementFadeIn 0.6s ease-out 0.6s both;
}

.submit-group {
    animation: formElementFadeIn 0.6s ease-out 0.8s both;
}

/* 特殊视觉效果 */
.login-card::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #667eea);
    background-size: 400% 400%;
    border-radius: 34px;
    z-index: -1;
    animation: gradientShift 8s ease infinite;
    opacity: 0;
    transition: var(--transition);
}

.login-card:hover::after {
    opacity: 0.3;
}

/* 输入框聚焦特效 */
.modern-input:focus-within::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 18px;
    z-index: -1;
    opacity: 0.1;
}

/* 按钮特殊效果 */
.login-submit-btn:not(:disabled):hover {
    animation: buttonPulse 2s ease-in-out infinite;
}

/* 品牌区域特效 */
.brand-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%,
        rgba(255, 255, 255, 0.05) 100%);
    border-radius: var(--border-radius);
    pointer-events: none;
}

/* 微交互效果 */
.feature-item:active {
    animation: microBounce 0.3s ease;
}

.login-submit-btn:active {
    animation: microBounce 0.2s ease;
}

/* 加载状态特效 */
.login-submit-btn.is-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    animation: loadingShimmer 1.5s ease-in-out infinite;
}

@keyframes loadingShimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* 顶级响应式设计 - 完美适配 */

/* 大屏幕优化 */
@media screen and (min-width: 1440px) {
    .main-content {
        gap: 120px;
        padding: 60px;
    }

    .brand-title {
        font-size: 4.5rem;
    }

    .login-card {
        max-width: 480px;
    }
}

/* 平板横屏 */
@media screen and (max-width: 1200px) {
    .main-content {
        gap: 60px;
        padding: 32px;
    }

    .brand-section {
        padding: 48px 32px;
    }

    .brand-title {
        font-size: 3.5rem;
    }
}

/* 平板竖屏 */
@media screen and (max-width: 1024px) {
    .main-content {
        flex-direction: column;
        gap: 40px;
        padding: 24px;
        justify-content: flex-start;
        padding-top: 60px;
    }

    .brand-section {
        flex: 0 0 auto;
        max-width: none;
        width: 100%;
        padding: 40px 24px;
        transform: none;
        text-align: center;
    }

    .brand-content {
        text-align: center;
    }

    .brand-description {
        text-align: center;
    }

    .login-section {
        flex: 0 0 auto;
        width: 100%;
        max-width: 480px;
        margin: 0 auto;
    }

    .login-card {
        transform: none;
        max-width: 100%;
    }

    .brand-title {
        font-size: 3rem;
    }

    .feature-list {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }

    .feature-item {
        padding: 16px 12px;
    }
}

/* 手机横屏 */
@media screen and (max-width: 768px) {
    .language-switcher {
        top: 20px;
        right: 20px;
    }

    .language-btn {
        width: 48px;
        height: 48px;
    }

    .main-content {
        padding: 20px;
        gap: 32px;
        padding-top: 80px;
    }

    .brand-section {
        padding: 32px 20px;
        border-radius: 20px;
    }

    .brand-title {
        font-size: 2.5rem;
        letter-spacing: -1px;
    }

    .brand-subtitle {
        font-size: 1rem;
        letter-spacing: 2px;
    }

    .brand-description h2 {
        font-size: 1.5rem;
    }

    .brand-description p {
        font-size: 1rem;
    }

    .feature-list {
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
    }

    .feature-item {
        padding: 12px 8px;
    }

    .feature-item span {
        font-size: 0.75rem;
    }

    .login-card {
        border-radius: 24px;
    }

    .card-header {
        padding: 32px 24px 24px;
    }

    .card-header h3 {
        font-size: 1.75rem;
    }

    .card-body {
        padding: 24px;
    }

    .card-footer {
        padding: 20px 24px 24px;
    }

    .modern-input :deep(.el-input__wrapper) {
        min-height: 60px;
        padding: 18px 20px 18px 52px;
    }

    .input-icon {
        left: 18px;
        font-size: 18px;
    }

    .login-submit-btn {
        height: 52px;
        font-size: 1rem;
        width: 92%;
    }

    .form-label,
    .input-wrapper {
        width: 92%;
    }

    .form-options {
        width: 92%;
    }

    /* 平板端错误提示对齐 */
    .form-group .el-form-item__error,
    .login-form .el-form-item__error {
        width: 85% !important;
    }
}

/* 手机竖屏 */
@media screen and (max-width: 480px) {
    .main-content {
        padding: 16px;
        gap: 24px;
        padding-top: 70px;
    }

    .brand-section {
        padding: 24px 16px;
        border-radius: 16px;
    }

    .brand-title {
        font-size: 2rem;
        letter-spacing: -1px;
    }

    .brand-subtitle {
        font-size: 0.875rem;
        letter-spacing: 1px;
    }

    .logo-icon svg {
        width: 48px;
        height: 48px;
    }

    .brand-description h2 {
        font-size: 1.25rem;
    }

    .brand-description p {
        font-size: 0.9rem;
    }

    .feature-list {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .feature-item {
        flex-direction: row;
        justify-content: center;
        padding: 12px 16px;
        gap: 8px;
    }

    .feature-item i {
        font-size: 1.25rem;
    }

    .feature-item span {
        font-size: 0.875rem;
    }

    .login-card {
        border-radius: 20px;
    }

    .card-header {
        padding: 28px 20px 20px;
    }

    .card-header h3 {
        font-size: 1.5rem;
    }

    .card-header p {
        font-size: 0.875rem;
    }

    .card-body {
        padding: 20px;
    }

    .card-footer {
        padding: 16px 20px 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .modern-input :deep(.el-input__wrapper) {
        min-height: 56px;
        padding: 16px 18px 16px 48px;
        border-radius: 12px;
    }

    .input-icon {
        left: 16px;
        font-size: 17px;
    }

    .form-label,
    .input-wrapper {
        width: 95%;
    }

    .form-options {
        justify-content: flex-start;
        width: 95%;
    }

    .login-submit-btn {
        height: 48px;
        font-size: 0.95rem;
        border-radius: 12px;
        width: 95%;
    }

    /* 手机端错误提示对齐 */
    .form-group .el-form-item__error,
    .login-form .el-form-item__error {
        width: 95% !important;
        font-size: 0.8rem !important;
    }

    .footer-text {
        font-size: 0.8rem;
    }
}

/* 暗黑模式 - 极致美学 */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-gradient: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        --glass-bg: rgba(255, 255, 255, 0.05);
        --glass-border: rgba(255, 255, 255, 0.1);
        --text-primary: #ffffff;
        --text-secondary: rgba(255, 255, 255, 0.7);
    }

    .modern-login-container {
        background: var(--primary-gradient);
    }

    .brand-section {
        background: rgba(255, 255, 255, 0.03);
        border-color: rgba(255, 255, 255, 0.08);
    }

    .login-card {
        background: rgba(26, 26, 46, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
    }

    .card-header {
        background: linear-gradient(135deg,
            rgba(26, 26, 46, 0.9) 0%,
            rgba(22, 33, 62, 0.8) 100%);
    }

    .card-header h3 {
        color: #ffffff;
    }

    .card-header p {
        color: rgba(255, 255, 255, 0.7);
    }

    .card-body {
        background: rgba(26, 26, 46, 0.6);
    }

    .card-footer {
        background: linear-gradient(135deg,
            rgba(22, 33, 62, 0.8) 0%,
            rgba(26, 26, 46, 0.6) 100%);
        border-color: rgba(255, 255, 255, 0.1);
    }

    .footer-text {
        color: rgba(255, 255, 255, 0.6);
    }

    .form-label {
        color: rgba(255, 255, 255, 0.9);
    }

    .modern-input :deep(.el-input__wrapper) {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.15);
    }

    .modern-input :deep(.el-input__wrapper:hover) {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(102, 126, 234, 0.4);
    }

    .modern-input :deep(.el-input__wrapper.is-focus) {
        background: rgba(255, 255, 255, 0.1);
        border-color: #667eea;
    }

    .modern-input :deep(.el-input__inner) {
        color: #ffffff;
    }

    .modern-input :deep(.el-input__inner::placeholder) {
        color: rgba(255, 255, 255, 0.4);
    }

    .input-icon {
        color: rgba(255, 255, 255, 0.5);
    }

    .modern-input:focus-within .input-icon {
        color: #667eea;
    }

    .remember-checkbox :deep(.el-checkbox__inner) {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .remember-checkbox :deep(.el-checkbox__label) {
        color: rgba(255, 255, 255, 0.8);
    }

    .forgot-link {
        color: #8b9aff;
    }

    .forgot-link:hover {
        color: #a5b4ff;
    }

    /* 暗黑模式下的光晕效果 */
    .orb-1 {
        background: radial-gradient(circle, #4c1d95 0%, #312e81 50%, transparent 100%);
    }

    .orb-2 {
        background: radial-gradient(circle, #1e3a8a 0%, #1e40af 50%, transparent 100%);
    }

    .orb-3 {
        background: radial-gradient(circle, #581c87 0%, #7c3aed 50%, transparent 100%);
    }
}

/* 高性能模式 - 减少动画 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .modern-input :deep(.el-input__wrapper) {
        border-width: 3px;
    }

    .login-submit-btn {
        border: 2px solid #ffffff;
    }

    .feature-item {
        border-width: 2px;
    }
}
