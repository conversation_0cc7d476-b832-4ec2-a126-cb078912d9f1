{"name": "build-admin", "version": "2.1.3", "license": "Apache-2.0", "type": "module", "scripts": {"dev": "esno ./src/utils/build.ts && vite --force", "build": "vite build && esno ./src/utils/build.ts", "lint": "eslint .", "lint-fix": "eslint --fix .", "format": "npx prettier --write .", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueuse/core": "10.10.0", "axios": "1.7.4", "echarts": "5.5.0", "element-plus": "2.7.8", "font-awesome": "4.7.0", "lodash-es": "4.17.21", "mitt": "3.0.1", "nprogress": "0.2.0", "pinia": "2.2.2", "pinia-plugin-persistedstate": "4.0.2", "qrcode.vue": "3.4.1", "screenfull": "6.0.2", "sortablejs": "1.15.2", "v-code-diff": "1.12.1", "vue": "3.4.38", "vue-i18n": "9.13.1", "vue-router": "4.4.3"}, "devDependencies": {"@eslint/js": "9.11.1", "@types/lodash-es": "4.17.12", "@types/node": "20.14.0", "@types/nprogress": "0.2.3", "@types/sortablejs": "1.15.8", "@vitejs/plugin-vue": "5.0.5", "async-validator": "4.2.5", "eslint": "9.11.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-vue": "9.28.0", "esno": "4.7.0", "globals": "15.9.0", "prettier": "3.3.0", "rollup": "4.26.0", "sass": "1.77.4", "typescript": "5.4.5", "typescript-eslint": "8.7.0", "vite": "5.2.12", "vue-tsc": "2.1.6"}}