{"compilerOptions": {"target": "ESNext", "module": "ESNext", "lib": ["ESNext", "DOM"], "useDefineForClassFields": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "jsx": "preserve", "sourceMap": false, "resolveJsonModule": true, "esModuleInterop": true, "isolatedModules": true, "baseUrl": "./", "allowJs": true, "skipLibCheck": true, "paths": {"/@/*": ["src/*"]}, "types": ["vite/client", "element-plus/global"]}, "include": ["src/**/*.ts", "src/**/*.vue", "types/**/*.d.ts", "vite.config.ts"]}