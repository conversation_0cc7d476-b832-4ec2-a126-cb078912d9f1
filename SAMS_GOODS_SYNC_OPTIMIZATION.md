# 山姆商品同步任务优化说明

## 问题描述

原来的山姆商品同步任务存在以下问题：
1. 没有限制门店数量，可能导致任务超时
2. 没有根据门店更新间隔来筛选需要更新的门店
3. 没有按更新时间排序，导致更新不均匀
4. 没有处理更新时间为空、null或0的情况

## 解决方案

### 1. 优化门店筛选逻辑

**创建任务时的门店筛选**：
```php
// 获取需要更新的门店（更新时间超过更新间隔的门店）
$currentTime = time();
$storeQuery = Store::where('type', 4)
    ->where('status', 1)
    ->where(function($query) use ($currentTime) {
        // 更新时间 + 更新间隔 <= 当前时间，表示需要更新
        $query->whereRaw('(IFNULL(update_time, 0) + IFNULL(update_interval, 7200)) <= ?', [$currentTime])
              ->whereOr('update_time', 0)  // 或者从未更新过的门店
              ->whereOr('update_time', null)  // 或者更新时间为null的门店
              ->whereOr('update_time', '');   // 或者更新时间为空字符串的门店
    })
    ->order('IFNULL(update_time, 0)', 'asc')  // 按更新时间升序，null和0排在前面
    ->limit(100); // 每次最多处理100个门店
```

**筛选条件**：
- 只处理 `type=4`（极速达）门店
- 只处理 `status=1`（正常）门店
- 只处理更新时间超过更新间隔的门店
- 处理更新时间为空、null或0的门店（优先处理）
- 按更新时间升序排序，确保最久未更新的门店优先处理
- 每次最多处理100个门店

### 2. 任务重复检查机制

**在创建任务前检查是否已存在相同门店的任务**：
```php
// 检查是否已经存在相同门店的任务
$existingTask = $this->checkExistingTask($batchStoreIds);
if ($existingTask) {
    $storeIdList = implode(',', $batchStoreIds);
    $this->debugLog('info', "门店 {$storeIdList} 已存在任务，跳过创建: {$existingTask['task_name']}");
    continue;
}
```

**检查逻辑**：
- 查询1小时内创建的正在执行或等待执行的任务（status为0、1、2）
- 通过 `goal` 字段匹配 `SamsGoods@syncGoodsData` 方法
- 检查任务参数中的门店ID是否有重叠
- 如果有重叠的门店ID，则跳过创建新任务
- 避免同一门店的重复任务

**状态说明**：
- 0=待执行
- 1=添加到任务
- 2=执行中

### 3. 统一任务类型

**所有任务都改为单次执行**：
```php
// 创建单次执行任务
$taskCreated = self::createTask(
    $taskName,
    'SamsGoods',
    'syncGoodsData',
    $cronExpression,
    $taskDescription,
    $taskParams,
    1  // 设置为执行一次
);
```

**优势**：
- 避免定时任务重复执行导致的冲突
- 可以根据实际需要手动创建任务
- 更好的资源控制

## 修改的文件

### 1. SamsGoods.php
- 修改了 `syncStoreGoodsData()` 方法的门店筛选逻辑
- 添加了按更新时间升序排序
- 处理更新时间为空、null或0的情况
- 统一所有任务为单次执行
- 添加了 `checkExistingTask()` 方法检查重复任务
- 添加了 Plantask 模型引用

## 使用方法

### 1. 创建商品同步任务

```php
// 创建任务，系统会自动筛选需要更新的门店
POST /index.php/api/Sams.SamsGoods/syncStoreGoodsData
{
    "create_task": 1,
    "store_batch_size": 5,  // 每个任务处理5个门店
    "batch_size": 10        // 每次处理10个分类
}
```

### 2. 配置调试模式

```bash
# 开启调试模式查看详细日志
export SAMS_DEBUG=true

# 或在 .env 文件中
SAMS_DEBUG=true
```

### 3. 监控任务执行

通过日志可以看到：
- 任务锁的获取和释放
- 门店筛选结果
- 任务执行状态
- 冲突检测信息

## 优势

### 1. 智能门店筛选
- 只处理真正需要更新的门店
- 避免无效的API请求
- 提高整体效率

### 2. 优先级处理
- 按更新时间升序排序，最久未更新的门店优先处理
- 处理从未更新过的门店（update_time为0、null或空）
- 确保更新的均匀性

### 3. 可控的任务规模
- 每次最多处理100个门店
- 可配置的批次大小
- 防止任务超时

### 4. 更好的数据处理
- 使用IFNULL处理空值情况
- 默认更新间隔为7200秒（2小时）
- 详细的调试日志

### 5. 避免重复任务
- 创建任务前检查是否已存在相同门店的任务
- 防止资源浪费和任务冲突
- 智能跳过重复的任务创建

## 注意事项

### 1. 门店更新间隔
- 确保门店表中的 `update_interval` 字段设置合理
- 建议设置为2-6小时（7200-21600秒）
- 可以根据门店重要性调整间隔
- 如果字段为空或null，系统会使用默认值7200秒

### 2. 更新时间字段
- 确保门店表中的 `update_time` 字段正确维护
- 新门店的update_time可能为0、null或空字符串
- 系统会优先处理这些从未更新过的门店

### 3. 调试模式
- 生产环境建议关闭调试模式
- 测试时开启调试模式查看详细执行过程
- 日志会记录门店筛选和处理过程

### 4. 任务监控
- 建议监控任务执行状态
- 关注门店更新的均匀性
- 及时处理异常情况

### 5. 重复任务检查
- 系统会自动检查1小时内创建的任务（从 `plantask` 表）
- 通过 `goal` 字段匹配 `SamsGoods@syncGoodsData` 方法
- 检查状态为0、1、2的任务（待执行、添加到任务、执行中）
- 如果发现门店ID有重叠，会跳过创建新任务
- 可以通过调试日志查看跳过的任务信息

## 性能提升

通过这次优化，预期可以获得以下性能提升：

1. **减少无效请求**：只处理需要更新的门店，减少50-80%的API请求
2. **更均匀的更新**：按更新时间排序，确保所有门店都能得到及时更新
3. **更好的资源利用**：合理的批次大小，避免内存和连接池问题
4. **智能调度**：根据门店更新间隔自动调度，减少人工干预
5. **优先处理新门店**：从未更新过的门店会被优先处理
6. **避免重复任务**：自动检查并跳过重复的任务创建，节省系统资源

这些优化使得山姆商品同步任务更加稳定、高效和均匀。
