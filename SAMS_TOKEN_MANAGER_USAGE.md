# 山姆Token管理系统使用说明

## 功能概述

`SamsTokenManager` 类提供了完整的Token管理功能，包括Token的添加、查询、状态管理和使用次数重置等功能。

## 文件位置

- **控制器**: `app/api/controller/Sams/SamsTokenManager.php`
- **前端页面**: `public/sams_token_manager.html`

## API接口

### 1. 重置Token使用次数

**接口地址**: `/index.php/api/Sams.SamsTokenManager/resetTokenUsageCount`
**请求方式**: `POST`

#### 功能说明

将更新时间不是今天的、状态正常的Token使用次数重置为0。

#### 响应示例

```json
{
    "code": 200,
    "msg": "重置Token使用次数成功",
    "data": {
        "affected_rows": 5,
        "reset_time": "2025-01-27 14:30:25",
        "today_start": "2025-01-27 00:00:00"
    }
}
```

### 2. 添加新Token

**接口地址**: `/index.php/api/Sams.SamsTokenManager/addToken`
**请求方式**: `POST`

#### 请求参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| phone | string | 是 | - | 11位手机号 |
| token | string | 是 | - | Token字符串 |
| status | int | 否 | 1 | 状态：1正常，0禁用 |

#### 请求示例

```json
{
    "phone": "13800138000",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "status": 1
}
```

#### 响应示例

```json
{
    "code": 200,
    "msg": "添加Token成功",
    "data": {
        "id": 123,
        "phone": "13800138000",
        "token_preview": "eyJhbGciOiJIUzI1NiIsIn...",
        "status": 1,
        "usage_count": 0,
        "create_time": "2025-01-27 14:30:25"
    }
}
```

### 3. 批量添加Token

**接口地址**: `/index.php/api/Sams.SamsTokenManager/batchAddTokens`
**请求方式**: `POST`

#### 请求参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| batch_data | string | 是 | - | 批量数据，格式：手机号----token，每行一个 |
| status | int | 否 | 1 | 状态：1正常，0禁用 |

#### 数据格式说明

批量数据格式为：`手机号----token`，每行一个，示例：

```
13800138000----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
13800138001----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkxIiwibmFtZSI6IkphbmUgRG9lIiwiaWF0IjoxNTE2MjM5MDIzfQ.T_Tq-4KKvz5z5z5z5z5z5z5z5z5z5z5z5z5z5z5z5z5
13800138002----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkyIiwibmFtZSI6IkJvYiBTbWl0aCIsImlhdCI6MTUxNjIzOTAyNH0.X_Xq-5LLwz6z6z6z6z6z6z6z6z6z6z6z6z6z6z6z6z6
```

#### 响应示例

```json
{
    "code": 200,
    "msg": "批量添加完成：成功3个，失败0个",
    "data": {
        "total_lines": 3,
        "success_count": 3,
        "failed_count": 0,
        "results": [
            {
                "line": 1,
                "phone": "13800138000",
                "token_preview": "eyJhbGciOiJIUzI1NiIsIn...",
                "status": "success"
            }
        ],
        "errors": []
    }
}
```

### 4. 获取Token列表

**接口地址**: `/index.php/api/Sams.SamsTokenManager/getTokenList`
**请求方式**: `GET`

#### 请求参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| page | int | 否 | 1 | 页码 |
| page_size | int | 否 | 20 | 每页数量 |
| status | string | 否 | - | 状态筛选：1正常，0禁用 |

#### 响应示例

```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "list": [
            {
                "id": 123,
                "phone": "13800138000",
                "token_preview": "eyJhbGciOiJIUzI1NiIsIn...",
                "status": 1,
                "status_text": "正常",
                "usage_count": 856,
                "create_time": "2025-01-27 14:30:25",
                "update_time": "2025-01-27 16:45:30"
            }
        ],
        "total": 50,
        "page": 1,
        "page_size": 20
    }
}
```

### 5. 更新Token状态

**接口地址**: `/index.php/api/Sams.SamsTokenManager/updateTokenStatus`
**请求方式**: `POST`

#### 请求参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| id | int | 是 | - | Token ID |
| status | int | 是 | - | 状态：1正常，0禁用 |

### 6. 删除Token

**接口地址**: `/index.php/api/Sams.SamsTokenManager/deleteToken`
**请求方式**: `POST`

#### 请求参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| id | int | 是 | - | Token ID |

### 7. 获取Token统计信息

**接口地址**: `/index.php/api/Sams.SamsTokenManager/getTokenStatistics`
**请求方式**: `GET`

#### 响应示例

```json
{
    "code": 200,
    "msg": "获取统计信息成功",
    "data": {
        "total_tokens": 25,
        "active_tokens": 20,
        "disabled_tokens": 5,
        "high_usage_tokens": 8,
        "today_updated_tokens": 12,
        "avg_usage_count": 456.78
    }
}
```

## 数据库表结构

### sam_token 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 主键ID |
| phone | varchar(11) | 手机号 |
| token | text | Token字符串 |
| status | tinyint | 状态：1正常，0禁用 |
| usage_count | int | 使用次数 |
| create_time | int | 创建时间戳 |
| update_time | int | 更新时间戳 |

## 使用方式

### 1. 网页界面

访问 `http://your-domain/sams_token_manager.html` 使用可视化界面：

- 查看Token统计信息
- 单个添加Token
- 批量添加Token（支持格式：手机号----token）
- 管理Token状态（启用/禁用）
- 删除Token
- 重置Token使用次数
- 分页浏览Token列表

#### 批量添加使用方法

1. 点击"批量添加"按钮切换到批量添加模式
2. 在文本框中输入数据，格式为：`手机号----token`，每行一个
3. 可以点击"查看格式示例"按钮查看示例格式
4. 选择Token状态（正常/禁用）
5. 点击"批量添加Token"按钮提交

**批量数据格式示例**：
```
13800138000----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
13800138001----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkxIiwibmFtZSI6IkphbmUgRG9lIiwiaWF0IjoxNTE2MjM5MDIzfQ.T_Tq-4KKvz5z5z5z5z5z5z5z5z5z5z5z5z5z5z5z5z5
13800138002----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkyIiwibmFtZSI6IkJvYiBTbWl0aCIsImlhdCI6MTUxNjIzOTAyNH0.X_Xq-5LLwz6z6z6z6z6z6z6z6z6z6z6z6z6z6z6z6z6
```

### 2. API调用

```javascript
// 重置Token使用次数
fetch('/index.php/api/Sams.SamsTokenManager/resetTokenUsageCount', {
    method: 'POST'
})
.then(response => response.json())
.then(data => {
    console.log('重置结果:', data);
});

// 添加新Token
const formData = new FormData();
formData.append('phone', '13800138000');
formData.append('token', 'your_token_here');
formData.append('status', 1);

fetch('/index.php/api/Sams.SamsTokenManager/addToken', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    console.log('添加结果:', data);
});

// 批量添加Token
const batchData = `13800138000----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
13800138001----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
13800138002----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`;

const batchFormData = new FormData();
batchFormData.append('batch_data', batchData);
batchFormData.append('status', 1);

fetch('/index.php/api/Sams.SamsTokenManager/batchAddTokens', {
    method: 'POST',
    body: batchFormData
})
.then(response => response.json())
.then(data => {
    console.log('批量添加结果:', data);
    console.log(`成功: ${data.data.success_count}, 失败: ${data.data.failed_count}`);
    if (data.data.errors.length > 0) {
        console.log('错误详情:', data.data.errors);
    }
});
```

## 功能特点

### 1. 自动重置功能
- 只重置状态正常的Token
- 只重置更新时间不是今天的Token
- 提供详细的重置统计信息

### 2. 数据验证
- 手机号格式验证（11位数字）
- 防重复添加（手机号和Token都不能重复）
- 参数完整性验证

### 3. 安全特性
- Token预览显示（只显示前20个字符）
- 操作日志记录
- 错误处理和异常捕获

### 4. 用户友好
- 实时统计信息展示
- 分页浏览支持
- 状态筛选功能
- 批量操作支持

### 5. 管理功能
- Token状态管理（启用/禁用）
- 使用次数统计
- 创建和更新时间跟踪
- 删除功能

## 业务逻辑

### Token使用次数重置逻辑

1. **触发条件**: 手动调用或定时任务
2. **筛选条件**: 
   - 状态为1（正常）
   - 更新时间小于今天00:00:00
3. **重置操作**: 将符合条件的Token的usage_count设置为0
4. **返回结果**: 影响的行数和操作时间

### Token添加逻辑

1. **参数验证**: 手机号格式、Token非空
2. **重复检查**: 手机号和Token都不能重复
3. **数据入库**: 创建新的Token记录
4. **初始状态**: usage_count初始为0

## 注意事项

1. **数据安全**: Token完整内容不会在API响应中返回，只显示预览
2. **并发安全**: 使用数据库事务确保数据一致性
3. **性能考虑**: 分页查询避免大量数据加载
4. **日志记录**: 所有重要操作都有日志记录
5. **错误处理**: 完善的异常处理机制

## 扩展功能

系统支持以下扩展：

1. **定时重置**: 可以配置定时任务自动重置Token使用次数
2. **批量导入**: 支持批量导入Token数据
3. **使用统计**: 详细的Token使用情况分析
4. **告警机制**: Token使用次数超限告警
5. **备份恢复**: Token数据的备份和恢复功能
