---
description: 前端开发指南
globs: 
alwaysApply: false
---
# 前端开发指南

BuildAdmin前端基于Vue 3 + TypeScript + Vite + Element Plus开发，使用组合式API (Setup)。

## 技术栈

- Vue 3.4+ (组合式API)
- TypeScript 5.4+
- Vite 5.2+
- Pinia 2.2+
- Vue Router 4.4+
- Element Plus 2.7+
- SCSS

## 目录结构

前端代码位于 [web](mdc:web) 目录下，主要结构：

```
web/src/
├── api/          // API接口定义
├── components/   // 公共组件
├── lang/         // 多语言
├── layout/       // 布局组件
├── router/       // 路由配置
├── stores/       // 状态管理
├── styles/       // 样式文件
├── utils/        // 工具函数
└── views/        // 页面组件
```

## 开发规范

### 组件开发
- 组件使用 `<script setup lang="ts">` 进行开发
- 组件名称采用 PascalCase 命名方式
- 页面组件放在 views 目录，公共组件放在 components 目录

### API调用
- API接口统一在 [web/src/api](mdc:web/src/api) 目录下定义
- 使用 axios 进行网络请求，已进行全局配置

### 状态管理
- 使用 Pinia 进行状态管理
- 状态定义在 [web/src/stores](mdc:web/src/stores) 目录下

### 样式规范
- 使用 SCSS 预处理器
- 全局样式在 [web/src/styles](mdc:web/src/styles) 目录下
- 组件样式使用 scoped 属性隔离

## 常用命令

```bash
# 开发
npm run dev

# 构建
npm run build

# 代码格式化
npm run format

# 代码检查
npm run lint
```

## 重要文件

- [web/src/main.ts](mdc:web/src/main.ts) - 应用入口
- [web/src/App.vue](mdc:web/src/App.vue) - 根组件
- [web/src/router/index.ts](mdc:web/src/router/index.ts) - 路由配置
- [web/src/stores/index.ts](mdc:web/src/stores/index.ts) - Pinia配置

