---
description: 项目概述
globs: 
alwaysApply: false
---
# BuildAdmin 项目概述

BuildAdmin是一个基于Vue3 + ThinkPHP8开发的后台管理系统框架。项目特点包括：

- 基于ThinkPHP 8.0 + Vue 3.4 + Element Plus 2.7 + TypeScript 5.4开发
- 支持CRUD代码生成、可视化操作
- 内置WEB终端
- 常驻内存运行支持
- 三种布局模式
- 完善的权限管理系统
- 内置全局数据回收站和数据修改保护
- 按需加载模块和语言包
- 自适应多端（PC、平板、手机）

## 主要技术栈

### 后端
- PHP 8.0+
- ThinkPHP 8.0
- Workerman 4.0+
- MySQL

### 前端
- Vue 3.4+
- TypeScript 5.4+
- Vite 5.2+
- Element Plus 2.7+
- Pinia 2.2+
- Vue Router 4.4+
- SCSS

## 重要文件
- 后端入口：[think](mdc:think) - 命令行入口文件
- 前端入口：[web/src/main.ts](mdc:web/src/main.ts) - 前端应用入口
- 后端配置：[composer.json](mdc:composer.json) - PHP依赖配置
- 前端配置：[web/package.json](mdc:web/package.json) - 前端依赖配置

## 官方资源
- 官网：https://uni.buildadmin.com
- 文档：https://doc.buildadmin.com
- 演示：https://demo.buildadmin.com

