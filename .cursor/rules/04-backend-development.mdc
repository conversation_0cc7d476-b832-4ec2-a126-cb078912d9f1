---
description: 后端开发指南
globs: 
alwaysApply: false
---
# 后端开发指南

BuildAdmin后端基于ThinkPHP 8.0开发，支持多应用模式。

## 技术栈

- PHP 8.0+
- ThinkPHP 8.0
- ThinkORM 3.0
- Workerman 4.0+ (用于常驻内存服务)
- MySQL

## 目录结构

后端代码主要分布在以下目录：

```
├── app/                 // 应用目录
│   ├── admin/           // 后台管理应用
│   ├── api/             // API应用
│   └── common/          // 公共模块
├── config/              // 配置文件
├── extend/              // 扩展类库
└── modules/             // 扩展模块
```

## 应用说明

- **admin应用**：后台管理功能
- **api应用**：前端API接口
- **common模块**：公共类库、中间件、服务等

## 开发规范

### 控制器
- 继承 `\app\BaseController` 基类
- 使用注解路由和验证器
- 返回统一的数据格式

### 模型
- 继承 `\think\Model` 类
- 模型放在对应应用的 `model` 目录下
- 使用模型关联进行表关联

### 服务层
- 业务逻辑放在服务层
- 服务类放在对应应用的 `service` 目录下

### 权限验证
- 使用中间件进行权限验证
- 权限定义在数据库中，自动注册

## 重要文件

- [app/BaseController.php](mdc:app/BaseController.php) - 控制器基类
- [app/common.php](mdc:app/common.php) - 公共函数
- [app/middleware.php](mdc:app/middleware.php) - 全局中间件配置
- [app/ExceptionHandle.php](mdc:app/ExceptionHandle.php) - 异常处理类

## 常用命令

```bash
# 创建控制器
php think make:controller admin/Test

# 创建模型
php think make:model admin/Test

# 数据库迁移
php think migrate:run

# 清除缓存
php think clear
```

## 数据库设计

- 表前缀为 `ba_`
- 主键使用 `id` 自增
- 表使用InnoDB引擎
- 字符集使用utf8mb4
