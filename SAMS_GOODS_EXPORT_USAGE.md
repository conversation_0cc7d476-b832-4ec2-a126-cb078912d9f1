# 山姆商品数据导出系统使用说明

## 功能概述

`SamsGoodsExport` 类提供了完整的山姆商品数据导出功能，支持多种格式导出、灵活的筛选条件和分页导出。

## 文件位置

- **控制器**: `app/api/controller/Sams/SamsGoodsExport.php`
- **前端页面**: `public/sams_goods_export.html`

## API接口

### 1. 导出所有商品数据

**接口地址**: `/index.php/api/Sams.SamsGoodsExport/exportAllGoods`
**请求方式**: `POST`

#### 请求参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| format | string | 否 | csv | 导出格式：csv, json, excel |
| store_id | int | 否 | 0 | 门店ID筛选，0表示所有门店 |
| category_id | int | 否 | 0 | 分类ID筛选，0表示所有分类 |
| status | int | 否 | 1 | 商品状态筛选，1正常，0禁用 |
| has_stock | int | 否 | -1 | 库存筛选：-1全部，0无库存，1有库存 |
| date_from | string | 否 | - | 更新时间起始（YYYY-MM-DD） |
| date_to | string | 否 | - | 更新时间结束（YYYY-MM-DD） |
| limit | int | 否 | 0 | 导出数量限制，0为不限制 |

#### 请求示例

```json
{
    "format": "csv",
    "store_id": 1001,
    "category_id": 100,
    "has_stock": 1,
    "date_from": "2025-01-01",
    "date_to": "2025-01-31",
    "limit": 1000
}
```

#### 响应

返回文件下载，文件名格式：
- CSV: `山姆商品数据导出_20250627143000.csv`
- JSON: `山姆商品数据导出_20250627143000.json`
- Excel: `山姆商品数据导出_20250627143000.xls`

### 2. 获取门店列表

**接口地址**: `/index.php/api/Sams.SamsGoodsExport/getStoreList`
**请求方式**: `GET`

#### 响应示例

```json
{
    "code": 200,
    "msg": "获取成功",
    "data": [
        {
            "storeId": 1001,
            "name": "上海杨行DC",
            "city": "上海",
            "type": 4,
            "type_name": "极速达"
        }
    ]
}
```

### 3. 获取分类列表

**接口地址**: `/index.php/api/Sams.SamsGoodsExport/getCategoryList`
**请求方式**: `GET`

#### 请求参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| level | int | 否 | 0 | 分类级别：0全部，1一级，2二级，3三级 |

#### 响应示例

```json
{
    "code": 200,
    "msg": "获取成功",
    "data": [
        {
            "groupingId": 100,
            "name": "食品饮料",
            "level": 1
        }
    ]
}
```

### 4. 获取导出统计信息

**接口地址**: `/index.php/api/Sams.SamsGoodsExport/getExportStatistics`
**请求方式**: `GET`

#### 请求参数

支持与导出接口相同的筛选参数

#### 响应示例

```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "total_goods_records": 25678,
        "independent_goods_count": 12345,
        "total_stores": 42,
        "has_stock_count": 12456,
        "no_stock_count": 3222,
        "last_update": "2025-01-27 14:30:25"
    }
}
```

### 5. 分页导出商品数据

**接口地址**: `/index.php/api/Sams.SamsGoodsExport/exportGoodsByPage`
**请求方式**: `POST`

#### 请求参数

除了基础筛选参数外，还包括：

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| page | int | 否 | 1 | 页码 |
| page_size | int | 否 | 1000 | 每页数量，最大5000 |

### 6. 导出指定门店商品

**接口地址**: `/index.php/api/Sams.SamsGoodsExport/exportStoreGoods`
**请求方式**: `POST`

#### 请求参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| store_id | int | 是 | - | 门店ID |
| format | string | 否 | csv | 导出格式 |

### 7. 导出独立商品数据

**接口地址**: `/index.php/api/Sams.SamsGoodsExport/exportIndependentGoods`
**请求方式**: `POST`

#### 请求参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| format | string | 否 | csv | 导出格式：csv, json, excel |
| category_id | int | 否 | 0 | 分类ID筛选，0表示所有分类 |
| status | int | 否 | 1 | 商品状态筛选，1正常，0禁用 |
| limit | int | 否 | 0 | 导出数量限制，0为不限制 |

#### 说明

此接口导出的是 `sam_goods` 表中的独立商品数据，不包含门店相关信息（价格、库存等），只包含商品基础信息。

## 导出数据字段说明

### CSV/Excel 格式字段

| 字段名 | 说明 |
|--------|------|
| 商品ID | 商品的spuId |
| 商品名称 | 商品名称 |
| 门店ID | 门店ID |
| 门店名称 | 门店名称 |
| 门店城市 | 门店所在城市 |
| 门店类型 | 门店类型（门店/极速达/全球购/全城配） |
| 价格(元) | 商品价格，已转换为元 |
| 库存 | 当前库存数量 |
| 库存状态 | 有库存/无库存 |
| 一级分类 | 一级分类名称 |
| 二级分类 | 二级分类名称 |
| 三级分类 | 三级分类名称 |
| UPC | 商品UPC码 |
| 商品图片 | 商品图片URL |
| 最后更新时间 | 数据最后更新时间 |

### 独立商品导出字段（sam_goods表）

| 字段名 | 说明 |
|--------|------|
| 商品完整数据 | 包含detail字段解析后的所有数据和额外字段的完整JSON |

#### 数据结构说明

导出的数据是完全平级的结构，将原 `detail` 字段中的所有数据解析到顶层：

**原始 detail 字段**：
```json
{
  "name": "商品名称",
  "description": "商品描述",
  "brand": "品牌名称",
  "specifications": "规格信息"
}
```

**最终导出的数据结构**：
```json
{
  "spuId": 12345,
  "name": "商品名称",
  "description": "商品描述",
  "brand": "品牌名称",
  "specifications": "规格信息",
  "categoryId": ",113114,112084,555555,",
  "category_json": ["113114", "112084", "555555"],
  "price": 2999,
  "stock": 100
}
```

**字段说明**：
- `spuId`: 商品ID
- `name`, `description`, `brand`, `specifications` 等: 来自原detail字段的所有数据
- `categoryId`: 分类ID组合，格式：,一级分类ID,二级分类ID,三级分类ID,（前后都有逗号）
- `category_json`: 分类ID的数组格式，包含三个独立的分类ID字符串
- `price`: 该商品在所有门店中的最高价格（分为单位）
- `stock`: 该商品在所有门店中的最高库存数量

**特点**：
- 所有数据都在同一层级，没有嵌套结构
- detail字段中的所有属性都被提取到顶层
- 便于数据处理和分析

### JSON 格式字段

JSON格式包含更多详细字段，包括：
- 商品基本信息（spuId, name, imgUrl等）
- 门店信息（store_id, store_name, store_city等）
- 价格库存信息（price, stock, price_yuan等）
- 分类信息（catLv1, catLv2, catLv3及对应名称）
- 其他扩展字段（seriesId, hostItemId, category1-3等）

## 使用方式

### 1. 网页界面

访问 `http://your-domain/sams_goods_export.html` 使用可视化界面：

- 查看实时统计数据
- 设置筛选条件
- 选择导出格式
- 一键导出数据

### 2. API调用

```javascript
// 导出CSV格式的所有商品数据
fetch('/index.php/api/Sams.SamsGoodsExport/exportAllGoods', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        format: 'csv',
        store_id: 0,
        has_stock: 1
    })
})
.then(response => response.blob())
.then(blob => {
    // 处理下载
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '商品数据.csv';
    a.click();
});
```

## 功能特点

### 1. 多格式支持
- **CSV格式**: 适合Excel打开，支持中文
- **JSON格式**: 包含完整数据结构，适合程序处理
- **Excel格式**: 制表符分隔，直接用Excel打开

### 2. 灵活筛选
- 按门店筛选
- 按分类筛选（支持各级分类）
- 按库存状态筛选
- 按更新时间范围筛选
- 按商品状态筛选

### 3. 性能优化
- 支持分页导出，避免内存溢出
- 数量限制功能
- 优化的数据库查询

### 4. 数据完整性
- 包含商品基本信息
- 包含价格库存信息
- 包含分类层级信息
- 包含门店信息
- 自动转换价格单位（分转元）

### 5. 用户友好
- 实时统计信息展示
- 加载状态提示
- 错误信息提示
- 响应式界面设计

## 注意事项

1. **大数据量导出**: 建议使用分页导出或设置数量限制
2. **服务器性能**: 大量数据导出可能消耗较多服务器资源
3. **文件大小**: CSV和Excel格式文件可能较大，注意网络传输
4. **数据实时性**: 导出的是当前数据库中的数据
5. **权限控制**: 根据需要添加访问权限控制

## 错误处理

常见错误及解决方案：

- **没有找到符合条件的商品数据**: 检查筛选条件是否过于严格
- **门店不存在**: 检查门店ID是否正确
- **导出失败**: 检查服务器日志，可能是内存或权限问题
- **文件下载失败**: 检查网络连接和浏览器设置
