<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>山姆Token管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .content {
            padding: 40px;
        }

        .stats-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-item {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .form-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-left: 4px solid #4facfe;
            padding-left: 15px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .form-group {
            flex: 1;
            min-width: 200px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .table-section {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-disabled {
            background: #f8d7da;
            color: #721c24;
        }

        /* Message 消息提示样式 */
        .message-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
        }

        .message {
            padding: 16px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: flex;
            align-items: center;
            gap: 12px;
            animation: slideInRight 0.3s ease-out;
            position: relative;
            overflow: hidden;
        }

        .message-success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }

        .message-error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }

        .message-warning {
            background: #fffbe6;
            border: 1px solid #ffe58f;
            color: #d48806;
        }

        .message-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }

        .message-icon {
            font-size: 16px;
            flex-shrink: 0;
        }

        .message-content {
            flex: 1;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .message-close {
            position: absolute;
            top: 8px;
            right: 8px;
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: inherit;
            opacity: 0.6;
            padding: 4px;
            line-height: 1;
        }

        .message-close:hover {
            opacity: 1;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .message.slide-out {
            animation: slideOutRight 0.3s ease-in forwards;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            max-width: 400px;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .table-section {
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>山姆Token管理</h1>
            <p>Sam's Club Token Management Center</p>
        </div>

        <div class="content">
            <!-- 统计信息区域 -->
            <div class="stats-section">
                <h3>📊 Token统计</h3>
                <div class="stats-grid" id="statsGrid">
                    <div class="stat-item">
                        <div class="stat-value" id="totalTokens">-</div>
                        <div class="stat-label">Token总数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="activeTokens">-</div>
                        <div class="stat-label">正常Token</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="disabledTokens">-</div>
                        <div class="stat-label">禁用Token</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="avgUsageCount">-</div>
                        <div class="stat-label">平均使用次数</div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="form-section">
                <h3>🔧 快速操作</h3>
                <div class="form-row">
                    <button class="btn btn-warning" onclick="resetTokenUsage()">
                        🔄 重置非今日Token使用次数
                    </button>
                    <button class="btn btn-primary" onclick="refreshData()">
                        📊 刷新统计数据
                    </button>
                </div>
            </div>

            <!-- 添加Token表单 -->
            <div class="form-section">
                <h3>➕ 添加新Token</h3>

                <!-- 切换按钮 -->
                <div class="form-row">
                    <button class="btn btn-primary" onclick="showSingleAdd()" id="singleAddBtn">单个添加</button>
                    <button class="btn btn-primary" onclick="showBatchAdd()" id="batchAddBtn" style="background: #ccc;">批量添加</button>
                </div>

                <!-- 单个添加表单 -->
                <div id="singleAddForm" style="display: block;">
                    <form id="addTokenForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="phone">手机号</label>
                                <input type="text" id="phone" name="phone" placeholder="请输入11位手机号" maxlength="11">
                            </div>
                            <div class="form-group">
                                <label for="token">Token</label>
                                <input type="text" id="token" name="token" placeholder="请输入Token">
                            </div>
                            <div class="form-group">
                                <label for="status">状态</label>
                                <select id="status" name="status">
                                    <option value="1">正常</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-success">添加Token</button>
                    </form>
                </div>

                <!-- 批量添加表单 -->
                <div id="batchAddForm" style="display: none;">
                    <form id="batchAddTokenForm">
                        <div class="form-row">
                            <div class="form-group" style="flex: 3;">
                                <label for="batchData">批量数据</label>
                                <textarea id="batchData" name="batch_data" rows="10" placeholder="请输入批量数据，格式：手机号----token，每行一个&#10;例如：&#10;13800138000----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&#10;13800138001----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." style="width: 100%; padding: 12px 15px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 14px; resize: vertical; min-height: 200px;"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="batchStatus">状态</label>
                                <select id="batchStatus" name="status">
                                    <option value="1">正常</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <button type="submit" class="btn btn-success">批量添加Token</button>
                            <button type="button" class="btn btn-primary" onclick="showBatchExample()">查看格式示例</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Token列表 -->
            <div class="table-section">
                <h3>📋 Token列表</h3>
                <div class="form-row">
                    <div class="form-group" style="max-width: 200px;">
                        <label for="statusFilter">状态筛选</label>
                        <select id="statusFilter" onchange="loadTokenList()">
                            <option value="">全部</option>
                            <option value="1">正常</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </div>
                
                <table class="table" id="tokenTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>手机号</th>
                            <th>Token预览</th>
                            <th>状态</th>
                            <th>使用次数</th>
                            <th>创建时间</th>
                            <th>更新时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tokenTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
                
                <div class="pagination" id="pagination">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </div>
            </div>

        </div>
    </div>

    <!-- Message 消息提示容器 -->
    <div class="message-container" id="messageContainer"></div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h3>正在处理...</h3>
            <p>请稍候</p>
        </div>
    </div>

    <script>
        let currentPage = 1;
        const pageSize = 10;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadTokenList();
            
            // 绑定表单提交事件
            document.getElementById('addTokenForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addToken();
            });

            document.getElementById('batchAddTokenForm').addEventListener('submit', function(e) {
                e.preventDefault();
                batchAddTokens();
            });
        });

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch('/index.php/api/Sams.SamsTokenManager/getTokenStatistics');
                const result = await response.json();
                
                if (result.code === 200) {
                    const stats = result.data;
                    document.getElementById('totalTokens').textContent = stats.total_tokens;
                    document.getElementById('activeTokens').textContent = stats.active_tokens;
                    document.getElementById('disabledTokens').textContent = stats.disabled_tokens;
                    document.getElementById('avgUsageCount').textContent = stats.avg_usage_count;
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 加载Token列表
        async function loadTokenList(page = 1) {
            showLoading(true);
            
            try {
                const status = document.getElementById('statusFilter').value;
                const params = new URLSearchParams({
                    page: page,
                    page_size: pageSize
                });
                
                if (status !== '') {
                    params.append('status', status);
                }
                
                const response = await fetch(`/index.php/api/Sams.SamsTokenManager/getTokenList?${params}`);
                const result = await response.json();
                
                if (result.code === 200) {
                    renderTokenTable(result.data.list);
                    renderPagination(result.data.total, page);
                    currentPage = page;
                } else {
                    showMessage('error', result.msg || '加载Token列表失败');
                }
            } catch (error) {
                console.error('加载Token列表失败:', error);
                showMessage('error', '加载Token列表失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 渲染Token表格
        function renderTokenTable(tokens) {
            const tbody = document.getElementById('tokenTableBody');
            tbody.innerHTML = '';
            
            tokens.forEach(token => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${token.id}</td>
                    <td>${token.phone}</td>
                    <td>${token.token_preview}</td>
                    <td><span class="status-badge ${token.status == 1 ? 'status-active' : 'status-disabled'}">${token.status_text}</span></td>
                    <td>${token.usage_count}</td>
                    <td>${token.create_time}</td>
                    <td>${token.update_time}</td>
                    <td>
                        <button class="btn btn-${token.status == 1 ? 'warning' : 'success'}" style="padding: 4px 8px; font-size: 12px;" onclick="toggleTokenStatus(${token.id}, ${token.status == 1 ? 0 : 1})">
                            ${token.status == 1 ? '禁用' : '启用'}
                        </button>
                        <button class="btn btn-danger" style="padding: 4px 8px; font-size: 12px; margin-left: 5px;" onclick="deleteToken(${token.id})">
                            删除
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染分页
        function renderPagination(total, currentPage) {
            const totalPages = Math.ceil(total / pageSize);
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.textContent = '上一页';
            prevBtn.disabled = currentPage <= 1;
            prevBtn.onclick = () => loadTokenList(currentPage - 1);
            pagination.appendChild(prevBtn);
            
            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    const pageBtn = document.createElement('button');
                    pageBtn.textContent = i;
                    pageBtn.className = i === currentPage ? 'active' : '';
                    pageBtn.onclick = () => loadTokenList(i);
                    pagination.appendChild(pageBtn);
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.style.padding = '8px';
                    pagination.appendChild(ellipsis);
                }
            }
            
            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.textContent = '下一页';
            nextBtn.disabled = currentPage >= totalPages;
            nextBtn.onclick = () => loadTokenList(currentPage + 1);
            pagination.appendChild(nextBtn);
        }

        // 显示单个添加表单
        function showSingleAdd() {
            document.getElementById('singleAddForm').style.display = 'block';
            document.getElementById('batchAddForm').style.display = 'none';
            document.getElementById('singleAddBtn').style.background = '';
            document.getElementById('batchAddBtn').style.background = '#ccc';
        }

        // 显示批量添加表单
        function showBatchAdd() {
            document.getElementById('singleAddForm').style.display = 'none';
            document.getElementById('batchAddForm').style.display = 'block';
            document.getElementById('singleAddBtn').style.background = '#ccc';
            document.getElementById('batchAddBtn').style.background = '';
        }

        // 显示批量添加格式示例
        function showBatchExample() {
            const example = `13800138000----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
13800138001----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkxIiwibmFtZSI6IkphbmUgRG9lIiwiaWF0IjoxNTE2MjM5MDIzfQ.T_Tq-4KKvz5z5z5z5z5z5z5z5z5z5z5z5z5z5z5z5z5
13800138002----eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkyIiwibmFtZSI6IkJvYiBTbWl0aCIsImlhdCI6MTUxNjIzOTAyNH0.X_Xq-5LLwz6z6z6z6z6z6z6z6z6z6z6z6z6z6z6z6z6`;

            document.getElementById('batchData').value = example;
            showMessage('info', '已填入格式示例，请根据实际情况修改', 3000);
        }

        // 添加Token
        async function addToken() {
            const form = document.getElementById('addTokenForm');
            const formData = new FormData(form);

            showLoading(true);

            try {
                const response = await fetch('/index.php/api/Sams.SamsTokenManager/addToken', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.code === 200) {
                    showMessage('success', '添加Token成功');
                    form.reset();
                    loadTokenList(currentPage);
                    loadStatistics();
                } else {
                    showMessage('error', result.msg || '添加Token失败');
                }
            } catch (error) {
                console.error('添加Token失败:', error);
                showMessage('error', '添加Token失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 批量添加Token
        async function batchAddTokens() {
            const form = document.getElementById('batchAddTokenForm');
            const formData = new FormData(form);

            const batchData = document.getElementById('batchData').value.trim();
            if (!batchData) {
                showMessage('warning', '请输入批量数据');
                return;
            }

            showLoading(true);

            try {
                const response = await fetch('/index.php/api/Sams.SamsTokenManager/batchAddTokens', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.code === 200) {
                    const data = result.data;
                    let message = `批量添加完成：成功${data.success_count}个，失败${data.failed_count}个`;

                    // 根据结果显示不同类型的消息
                    if (data.failed_count === 0) {
                        // 全部成功
                        showMessage('success', message, 3000);
                    } else if (data.success_count === 0) {
                        // 全部失败
                        if (data.errors && data.errors.length > 0) {
                            const errorDetails = data.errors.slice(0, 10).join('\n');
                            const fullMessage = message + '\n\n错误详情：\n' + errorDetails;
                            if (data.errors.length > 10) {
                                fullMessage += `\n... 还有${data.errors.length - 10}个错误`;
                            }
                            showMessage('error', fullMessage, 8000);
                        } else {
                            showMessage('error', message, 5000);
                        }
                    } else {
                        // 部分成功
                        showMessage('warning', message, 4000);

                        // 如果有错误，单独显示错误详情
                        if (data.errors && data.errors.length > 0) {
                            setTimeout(() => {
                                const errorDetails = data.errors.slice(0, 8).join('\n');
                                let errorMessage = '错误详情：\n' + errorDetails;
                                if (data.errors.length > 8) {
                                    errorMessage += `\n... 还有${data.errors.length - 8}个错误`;
                                }
                                showMessage('error', errorMessage, 8000);
                            }, 500);
                        }
                    }

                    if (data.success_count > 0) {
                        form.reset();
                        loadTokenList(currentPage);
                        loadStatistics();
                    }
                } else {
                    showMessage('error', result.msg || '批量添加失败');
                }
            } catch (error) {
                console.error('批量添加Token失败:', error);
                showMessage('error', '批量添加失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 切换Token状态
        async function toggleTokenStatus(id, status) {
            if (!confirm(`确定要${status == 1 ? '启用' : '禁用'}这个Token吗？`)) {
                return;
            }
            
            showLoading(true);
            
            try {
                const formData = new FormData();
                formData.append('id', id);
                formData.append('status', status);
                
                const response = await fetch('/index.php/api/Sams.SamsTokenManager/updateTokenStatus', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage('success', '更新状态成功');
                    loadTokenList(currentPage);
                    loadStatistics();
                } else {
                    showMessage('error', result.msg || '更新状态失败');
                }
            } catch (error) {
                console.error('更新状态失败:', error);
                showMessage('error', '更新状态失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 删除Token
        async function deleteToken(id) {
            if (!confirm('确定要删除这个Token吗？此操作不可恢复！')) {
                return;
            }
            
            showLoading(true);
            
            try {
                const formData = new FormData();
                formData.append('id', id);
                
                const response = await fetch('/index.php/api/Sams.SamsTokenManager/deleteToken', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage('success', '删除Token成功');
                    loadTokenList(currentPage);
                    loadStatistics();
                } else {
                    showMessage('error', result.msg || '删除Token失败');
                }
            } catch (error) {
                console.error('删除Token失败:', error);
                showMessage('error', '删除Token失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 重置Token使用次数
        async function resetTokenUsage() {
            if (!confirm('确定要重置所有非今日更新的Token使用次数吗？')) {
                return;
            }
            
            showLoading(true);
            
            try {
                const response = await fetch('/index.php/api/Sams.SamsTokenManager/resetTokenUsageCount', {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage('success', `重置成功，影响${result.data.affected_rows}个Token`);
                    loadTokenList(currentPage);
                    loadStatistics();
                } else {
                    showMessage('error', result.msg || '重置失败');
                }
            } catch (error) {
                console.error('重置失败:', error);
                showMessage('error', '重置失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 刷新数据
        function refreshData() {
            loadStatistics();
            loadTokenList(currentPage);
            showMessage('success', '数据已刷新', 2000);
        }

        // 显示/隐藏加载遮罩
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            overlay.style.display = show ? 'flex' : 'none';
        }

        // 显示Message消息提示
        function showMessage(type, content, duration = 4000) {
            const container = document.getElementById('messageContainer');

            // 创建消息元素
            const message = document.createElement('div');
            message.className = `message message-${type}`;

            // 图标映射
            const icons = {
                success: '✓',
                error: '✕',
                warning: '⚠',
                info: 'ℹ'
            };

            message.innerHTML = `
                <span class="message-icon">${icons[type] || icons.info}</span>
                <div class="message-content">${content}</div>
                <button class="message-close" onclick="closeMessage(this)">×</button>
            `;

            // 添加到容器
            container.appendChild(message);

            // 自动关闭
            setTimeout(() => {
                closeMessage(message.querySelector('.message-close'));
            }, duration);
        }

        // 关闭消息
        function closeMessage(closeBtn) {
            const message = closeBtn.parentElement;
            message.classList.add('slide-out');

            setTimeout(() => {
                if (message.parentElement) {
                    message.parentElement.removeChild(message);
                }
            }, 300);
        }

        // 兼容旧的showAlert函数
        function showAlert(type, content) {
            showMessage(type, content);
        }
    </script>
</body>
</html>
