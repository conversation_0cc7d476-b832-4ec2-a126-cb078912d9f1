<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>山姆商品价格库存查询系统</title>
    <link href="//cdn.jsdmirror.com/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="//cdn.jsdmirror.com/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .main-header h1 {
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .query-form {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            padding: 12px 16px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-1px);
        }

        .goods-names-textarea {
            min-height: 140px;
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            resize: vertical;
        }

        .btn {
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
        }

        .result-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.2);
            overflow: hidden;
        }

        .result-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .status-found {
            border-left: 5px solid #28a745;
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(255,255,255,0.95) 100%);
        }

        .status-delisted {
            border-left: 5px solid #dc3545;
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.05) 0%, rgba(255,255,255,0.95) 100%);
        }

        .status-error {
            border-left: 5px solid #ffc107;
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(255,255,255,0.95) 100%);
        }

        .price-increase {
            color: #dc3545;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(220, 53, 69, 0.2);
        }

        .price-decrease {
            color: #28a745;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(40, 167, 69, 0.2);
        }

        .price-no-change {
            color: #6c757d;
            font-weight: 500;
        }

        .statistics-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .statistics-card h3 {
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .statistics-card h5 {
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }



        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
        }

        .overlay-modal {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            border: 1px solid rgba(255,255,255,0.2);
            max-width: 400px;
            width: 90%;
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        .badge {
            border-radius: 8px;
            padding: 6px 12px;
            font-weight: 600;
        }

        .card-title {
            font-weight: 600;
            color: #2c3e50;
        }

        .text-primary {
            color: #667eea !important;
            font-weight: 700;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .form-text {
            color: #6c757d;
            font-size: 0.875rem;
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .result-card {
            animation: fadeInUp 0.5s ease-out;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .main-header {
                padding: 1.5rem 0;
            }

            .query-form {
                padding: 20px;
                margin: 0 10px 20px 10px;
            }

            .result-card {
                margin: 0 10px 15px 10px;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
    </style>
</head>
<body>
    <!-- 主标题区域 -->
    <div class="main-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="mb-2">
                        <i class="bi bi-search me-3"></i>山姆商品价格库存查询系统
                    </h1>
                    <p class="mb-0 opacity-75">快速查询商品价格变动、库存状态和上下架信息</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid px-4">

        <!-- 查询表单 -->
        <div class="row">
            <div class="col-12">
                <div class="query-form">
                    <form id="queryForm">
                        <div class="row">
                            <div class="col-md-8">
                                <label for="goodsNames" class="form-label">
                                    <i class="bi bi-list-ul"></i> 商品名称 <span class="text-danger">*</span>
                                </label>
                                <textarea
                                    class="form-control goods-names-textarea"
                                    id="goodsNames"
                                    name="goods_names"
                                    placeholder="请输入商品名称，一行一个：&#10;&#10;可口可乐&#10;雪碧&#10;芬达&#10;百事可乐&#10;康师傅冰红茶"
                                    required></textarea>
                                <div class="form-text">请一行输入一个商品名称</div>
                            </div>
                            <div class="col-md-4">
                                <label for="storeId" class="form-label">
                                    <i class="bi bi-shop"></i> 选择门店
                                </label>
                                <select class="form-select" id="storeId" name="store_id">
                                    <option value="0">所有门店</option>
                                </select>
                                <div class="form-text">选择特定门店或查询所有门店</div>
                            </div>

                        </div>
                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-lg me-3 px-4">
                                    <i class="bi bi-search me-2"></i>开始查询
                                </button>
                                <button type="button" class="btn btn-success btn-lg me-3 px-4" id="exportBtn" disabled>
                                    <i class="bi bi-download me-2"></i>导出结果
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg px-4" id="clearBtn">
                                    <i class="bi bi-arrow-clockwise me-2"></i>清空重置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>



        <!-- 统计信息 -->
        <div class="row" id="statisticsRow" style="display: none;">
            <div class="col-12">
                <div class="statistics-card p-4 mb-4">
                    <h5 class="mb-4"><i class="bi bi-bar-chart me-2"></i>查询统计</h5>
                    <div class="row text-center">
                        <div class="col-6 col-md-2 mb-3 mb-md-0">
                            <h3 id="totalQuery" class="mb-1">0</h3>
                            <small class="opacity-75">查询总数</small>
                        </div>
                        <div class="col-6 col-md-2 mb-3 mb-md-0">
                            <h3 id="foundGoods" class="mb-1">0</h3>
                            <small class="opacity-75">找到商品</small>
                        </div>
                        <div class="col-6 col-md-2 mb-3 mb-md-0">
                            <h3 id="priceChanged" class="mb-1">0</h3>
                            <small class="opacity-75">价格变动</small>
                        </div>
                        <div class="col-6 col-md-2 mb-3 mb-md-0">
                            <h3 id="outOfStock" class="mb-1">0</h3>
                            <small class="opacity-75">缺货商品</small>
                        </div>
                        <div class="col-6 col-md-2 mb-3 mb-md-0">
                            <h3 id="delisted" class="mb-1">0</h3>
                            <small class="opacity-75">已下架</small>
                        </div>
                        <div class="col-6 col-md-2">
                            <div id="queryTime" class="small opacity-75"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 查询结果 -->
        <div class="row" id="resultsRow" style="display: none;">
            <div class="col-12">
                <h4 class="mb-4 text-center">
                    <i class="bi bi-list-check me-2"></i>查询结果
                </h4>
                <div id="resultsContainer"></div>
            </div>
        </div>
    </div>

    <!-- 查询等待遮罩 -->
    <div class="overlay" id="queryOverlay">
        <div class="overlay-modal">
            <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">查询中...</span>
            </div>
            <h5 class="text-primary mb-2">正在查询商品信息</h5>
            <p class="text-muted mb-0">请稍候，系统正在为您查询商品价格和库存信息...</p>
        </div>
    </div>

    <!-- 导出等待遮罩 -->
    <div class="overlay" id="exportOverlay">
        <div class="overlay-modal">
            <div class="spinner-border text-success mb-3" role="status">
                <span class="visually-hidden">导出中...</span>
            </div>
            <h5 class="text-success mb-2">正在导出数据</h5>
            <p class="text-muted mb-0">请稍候，系统正在生成CSV文件...</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="//cdn.jsdmirror.com/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 全局变量
        let currentResults = [];
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStoreList();
            initEventListeners();
        });
        
        // 初始化事件监听器
        function initEventListeners() {
            document.getElementById('queryForm').addEventListener('submit', handleQuery);
            document.getElementById('exportBtn').addEventListener('click', handleExport);
            document.getElementById('clearBtn').addEventListener('click', handleClear);
        }
        
        // 加载门店列表
        async function loadStoreList() {
            try {
                console.log('正在加载门店列表...');
                const response = await fetch('/index.php/api/Sams.SamsGoodsQuery/getStoreList');
                console.log('API响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const responseText = await response.text();
                console.log('API响应内容:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON解析失败:', parseError);
                    throw new Error('服务器返回的不是有效的JSON格式');
                }

                if (data.code === 200 && data.data) {
                    const storeSelect = document.getElementById('storeId');

                    // 分离惠州地区和其他地区的门店
                    const huizhouStores = [];
                    const otherStores = [];

                    data.data.forEach(store => {
                        if (store.city && store.city.includes('惠州')) {
                            huizhouStores.push(store);
                        } else {
                            otherStores.push(store);
                        }
                    });

                    // 先添加惠州地区门店
                    if (huizhouStores.length > 0) {
                        const huizhouGroup = document.createElement('optgroup');
                        huizhouGroup.label = '🏪 惠州地区门店';
                        huizhouStores.forEach(store => {
                            const option = document.createElement('option');
                            option.value = store.store_id;
                            option.textContent = `${store.name} - ${store.type_name}`;
                            huizhouGroup.appendChild(option);
                        });
                        storeSelect.appendChild(huizhouGroup);
                    }

                    // 再添加其他地区门店
                    if (otherStores.length > 0) {
                        const otherGroup = document.createElement('optgroup');
                        otherGroup.label = '🌍 其他地区门店';
                        otherStores.forEach(store => {
                            const option = document.createElement('option');
                            option.value = store.store_id;
                            option.textContent = `${store.name} (${store.city}) - ${store.type_name}`;
                            otherGroup.appendChild(option);
                        });
                        storeSelect.appendChild(otherGroup);
                    }
                } else {
                    console.error('门店列表数据格式错误:', data);
                }
            } catch (error) {
                console.error('加载门店列表失败:', error);
                // 如果加载失败，显示错误提示
                const storeSelect = document.getElementById('storeId');
                const errorOption = document.createElement('option');
                errorOption.value = '0';
                errorOption.textContent = '加载门店列表失败，请刷新页面重试';
                errorOption.disabled = true;
                storeSelect.appendChild(errorOption);
            }
        }
        
        // 处理查询
        async function handleQuery(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const queryData = {
                goods_names: formData.get('goods_names'),
                store_id: parseInt(formData.get('store_id')),
                days: 0  // 固定查询所有历史数据
            };
            
            if (!queryData.goods_names.trim()) {
                alert('请输入商品名称');
                return;
            }
            
            showQueryOverlay(true);
            hideResults();
            
            try {
                console.log('正在查询商品信息...', queryData);
                const response = await fetch('/index.php/api/Sams.SamsGoodsQuery/queryGoodsPriceAndStock', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(queryData)
                });

                console.log('查询API响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const responseText = await response.text();
                console.log('查询API响应内容:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON解析失败:', parseError);
                    throw new Error('服务器返回的不是有效的JSON格式');
                }
                
                if (data.code === 200) {
                    currentResults = data.data.results;
                    displayResults(data.data);
                    document.getElementById('exportBtn').disabled = false;
                } else {
                    alert('查询失败: ' + data.msg);
                }
            } catch (error) {
                console.error('查询失败:', error);
                alert('查询失败: ' + error.message);
            } finally {
                showQueryOverlay(false);
            }
        }
        
        // 显示/隐藏查询遮罩
        function showQueryOverlay(show) {
            const overlay = document.getElementById('queryOverlay');
            overlay.style.display = show ? 'flex' : 'none';
        }
        
        // 隐藏结果
        function hideResults() {
            document.getElementById('statisticsRow').style.display = 'none';
            document.getElementById('resultsRow').style.display = 'none';
        }
        
        // 显示查询结果
        function displayResults(data) {
            displayStatistics(data.statistics, data.query_time);
            displayResultCards(data.results);
            
            document.getElementById('statisticsRow').style.display = 'block';
            document.getElementById('resultsRow').style.display = 'block';
        }
        
        // 显示统计信息
        function displayStatistics(stats, queryTime) {
            document.getElementById('totalQuery').textContent = stats.total_query;
            document.getElementById('foundGoods').textContent = stats.found_goods;
            document.getElementById('priceChanged').textContent = stats.price_changed;
            document.getElementById('outOfStock').textContent = stats.out_of_stock;
            document.getElementById('delisted').textContent = stats.delisted;
            document.getElementById('queryTime').innerHTML = `<i class="bi bi-clock"></i><br>${queryTime}`;
        }
        
        // 显示结果卡片
        function displayResultCards(results) {
            const container = document.getElementById('resultsContainer');
            container.innerHTML = '';
            
            results.forEach((result, index) => {
                const card = createResultCard(result, index);
                container.appendChild(card);
            });
        }
        
        // 创建结果卡片
        function createResultCard(result, index) {
            const card = document.createElement('div');
            card.className = `card result-card status-${result.status}`;
            
            const statusIcon = getStatusIcon(result.status);
            const statusText = getStatusText(result.status);
            const priceChangeInfo = getPriceChangeInfo(result.price_change_info);
            
            card.innerHTML = `
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center mb-3">
                                ${statusIcon}
                                <h5 class="card-title mb-0 ms-2 me-3">${result.goods_name}</h5>
                                <span class="badge bg-${getStatusColor(result.status)}">${statusText}</span>
                            </div>
                            ${result.matched_name ? `
                                <div class="mb-2">
                                    <i class="bi bi-arrow-right text-primary me-2"></i>
                                    <span class="text-muted">匹配商品: </span>
                                    <strong>${result.matched_name}</strong>
                                </div>
                            ` : ''}
                            <div class="row">
                                ${result.store_name ? `
                                    <div class="col-sm-6 mb-2">
                                        <i class="bi bi-shop text-primary me-2"></i>
                                        <span class="text-muted">门店: </span>
                                        <span>${result.store_name}</span>
                                    </div>
                                ` : ''}
                                ${result.spuId ? `
                                    <div class="col-sm-6 mb-2">
                                        <i class="bi bi-upc text-primary me-2"></i>
                                        <span class="text-muted">商品ID: </span>
                                        <code class="text-dark">${result.spuId}</code>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center text-md-end">
                                ${result.current_price !== null ? `
                                    <div class="price-section mb-3">
                                        <h3 class="text-primary mb-1 fw-bold">¥${result.current_price}</h3>
                                        <div class="d-flex justify-content-center justify-content-md-end align-items-center mb-2">
                                            <i class="bi bi-box text-muted me-1"></i>
                                            <span class="text-muted">库存: </span>
                                            <span class="fw-semibold ms-1 ${result.current_stock > 0 ? 'text-success' : 'text-danger'}">${result.current_stock}</span>
                                        </div>
                                        ${priceChangeInfo}
                                    </div>
                                    <small class="text-muted">
                                        <i class="bi bi-clock me-1"></i>
                                        ${result.last_update || ''}
                                    </small>
                                ` : `
                                    <div class="alert alert-light text-center mb-0">
                                        <i class="bi bi-info-circle text-muted me-2"></i>
                                        ${result.message}
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            return card;
        }
        
        // 获取状态图标
        function getStatusIcon(status) {
            const icons = {
                'found': '<i class="bi bi-check-circle-fill text-success fs-4"></i>',
                'delisted': '<i class="bi bi-x-circle-fill text-danger fs-4"></i>',
                'error': '<i class="bi bi-exclamation-triangle-fill text-warning fs-4"></i>'
            };
            return icons[status] || '<i class="bi bi-question-circle fs-4"></i>';
        }
        
        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'found': '正常在售',
                'delisted': '已下架',
                'error': '查询出错'
            };
            return texts[status] || status;
        }
        
        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                'found': 'success',
                'delisted': 'danger',
                'error': 'warning'
            };
            return colors[status] || 'secondary';
        }
        
        // 获取价格变动信息
        function getPriceChangeInfo(priceChangeInfo) {
            if (!priceChangeInfo) {
                return '<p class="mb-1 price-no-change"><i class="bi bi-question"></i> 无价格信息</p>';
            }

            // 处理无数据情况
            if (priceChangeInfo.change_type === 'no_data') {
                return '<p class="mb-1 price-no-change"><i class="bi bi-info-circle"></i> 暂无历史数据</p>';
            }

            // 处理单条记录情况
            if (priceChangeInfo.change_type === 'single_record') {
                return '<p class="mb-1 price-no-change"><i class="bi bi-record-circle"></i> 仅有单条记录</p>';
            }

            // 处理无变动情况
            if (!priceChangeInfo.changed) {
                return `<p class="mb-1 price-no-change"><i class="bi bi-dash"></i> 价格无变动 <small>(${priceChangeInfo.history_count}条记录)</small></p>`;
            }

            const isIncrease = priceChangeInfo.change_type === 'increase';
            const icon = isIncrease ? 'bi-arrow-up' : 'bi-arrow-down';
            const colorClass = isIncrease ? 'price-increase' : 'price-decrease';
            const changeText = isIncrease ? '涨价' : '降价';

            return `
                <p class="mb-1 ${colorClass}">
                    <i class="bi ${icon}"></i> ${changeText} ¥${Math.abs(priceChangeInfo.change_amount)}
                    (${priceChangeInfo.change_percent > 0 ? '+' : ''}${priceChangeInfo.change_percent}%)
                    <small>(${priceChangeInfo.history_count}条记录)</small>
                </p>
                ${priceChangeInfo.analysis_note ? `<small class="text-muted">${priceChangeInfo.analysis_note}</small>` : ''}
            `;
        }

        // 显示/隐藏导出遮罩
        function showExportOverlay(show) {
            const overlay = document.getElementById('exportOverlay');
            overlay.style.display = show ? 'flex' : 'none';
        }

        // 处理导出
        async function handleExport() {
            if (currentResults.length === 0) {
                alert('没有可导出的数据');
                return;
            }
            
            const formData = new FormData(document.getElementById('queryForm'));
            const queryData = {
                goods_names: formData.get('goods_names'),
                store_id: parseInt(formData.get('store_id')),
                days: 0  // 固定查询所有历史数据
            };

            // 显示导出遮罩
            showExportOverlay(true);

            try {
                const response = await fetch('/index.php/api/Sams.SamsGoodsQuery/exportQueryResults', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(queryData)
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `商品价格库存查询_${new Date().toISOString().slice(0,19).replace(/:/g, '')}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    // 延迟隐藏遮罩，让用户看到完成状态
                    setTimeout(() => {
                        showExportOverlay(false);
                    }, 500);
                } else {
                    showExportOverlay(false);
                    alert('导出失败');
                }
            } catch (error) {
                console.error('导出失败:', error);
                showExportOverlay(false);
                alert('导出失败: ' + error.message);
            }
        }
        
        // 处理清空重置
        function handleClear() {
            document.getElementById('queryForm').reset();
            hideResults();
            currentResults = [];
            document.getElementById('exportBtn').disabled = true;
        }
    </script>
</body>
</html>
