
### 介绍
🌈 基于 Vue3.3 + ThinkPHP8 + TypeScript + Vite + Pinia + Element Plus 等流行技术栈的后台管理系统，支持常驻内存运行、可视化CRUD代码生成、自带WEB终端、自适应多端、同时提供Web、WebNuxt、Server端、内置全局数据回收站和字段级数据修改保护、自动注册路由、无限子级权限管理等，无需授权即可免费商用，希望能帮助大家实现快速开发。

### 主要特性
**🚀 CRUD代码生成：**
图形化拖拽生成后台增删改查代码，自动创建数据表；大气且实用的表格，多达24种表单组件支持，行拖拽排序，受权限控制的编辑和删除等等，并支持关联表，可为您节省大量开发时间。

**💥 内置WEB终端：**
我们内置了一个WEB终端以实现一些理想中的功能，比如：虽然是基于vue3的系统，但你在安装本系统时，并不需要手动执行`npm install`和`npm build`命令。且后续本终端将为您提供更多方便、快捷的服务。

**👍 流行且稳定的技术栈：**
除了基于`ThinkPHP8`前后端分离架构外，我们的`Vue3`使用了`Setup`、状态管理使用`Pinia`、并使用了`TypeScript`、`Vite`等可以为你的知识面添砖加瓦的技术栈。

**🎨 模块市场：**
一键安装数据导出、短信发送、云存储、单页或是纯前端技术栈的学习案例项目等等，随时随地为系统添砖加瓦，系统能够自动维护`package.json`和`composer.json`并通过内置终端自动完成模块所需依赖的安装，若您愿意成为模块开发者，模块可以：覆盖系统任何文件或为系统新增文件，您的模块经由官方审核即可上架。

**🔀 前后端分离：**
`web`文件夹内包含：`干净`(不含后端代码)、`完整`(所有前端代码文件均在此内) 的前端代码文件，对前端开发者友好，作为纯前端开发者，您可以将BAdmin当做学习与资源的社群，本系统可为您准备好案例和模板等所需要的环境，而您只需专注于学习或工作，不需要会任何后端代码！（邀您：[和我们一起](https://jq.qq.com/?_wv=1027&k=c8a7iSk8) ）

**⚡️ 常驻内存：**
系统内置的功能均可常驻内存运行，享受比传统框架快上数十倍的性能提升！目前[Workerman模块](https://modules.buildadmin.com/workerman)可提供框架的常驻内存`HTTP服务`，同时该模块还提供了开箱即用的`WebSocket服务`。

**🚚 按需加载：**
前端的页面组件和语言包均是在使用到它们时，才从网络异步加载，服务端则是基于`TP8`和`PSR规范`天生拥有真正的按需加载能力，所以，您无需考虑`我并不需要多语言`、`我并不需要某个后台功能`这类的问题，不需要不使用或隐藏即可。

**🌴 数据回收与反悔：**
内置全局数据回收站，并且提供字段级数据修改记录和修改对比，随时回滚和还原，安全且无感。

**✨ 高颜值：**
提供三种布局模式，其中默认布局使用无边框设计风格，它并没有强行填满屏幕的每一个缝然后使用边框线进行分隔，所有的功能版块，都像是悬浮在屏幕上的，同时又将屏幕空间及其合理的利用了。

**🔐 权限验证：**
可视化的管理权限，然后根据权限动态的注册路由、菜单、页面、按钮(权限节点)、支持无限父子级权限分组、前后端搭配鉴权，自由分派页面和按钮权限。

**📝 未来可期：**
我们正在持续维护系统，并着手开发更多基础设施模块，按需一键安装，甚至提供开箱即用的各行业完整应用。

**🧱 一举多得：**
后台自适应PC、平板、手机等多种场景的支持，轻松应对各种需求。

**💖 其他杂项：**
角色组/管理员/管理员日志、 会员/会员组/会员余额、积分日志、系统配置/控制台/附件管理/个人资料管理等等、更多特性等你探索...
