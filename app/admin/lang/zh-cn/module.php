<?php
return [
    'Order not found'                                           => '订单找不到啦！',
    'Module already exists'                                     => '模块已存在！',
    'package download failed'                                   => '包下载失败！',
    'package check failed'                                      => '包检查失败！',
    'No permission to write temporary files'                    => '没有权限写入临时文件！',
    'Zip file not found'                                        => '找不到压缩包文件',
    'Unable to open the zip file'                               => '无法打开压缩包文件',
    'Unable to extract ZIP file'                                => '无法提取ZIP文件',
    'Unable to package zip file'                                => '无法打包zip文件',
    'Basic configuration of the Module is incomplete'           => '模块基础配置不完整',
    'Module package file does not exist'                        => '模块包文件不存在',
    'Module file conflicts'                                     => '模块文件存在冲突，请手动处理！',
    'Configuration file has no write permission'                => '配置文件无写入权限',
    'The current state of the module cannot be set to disabled' => '模块当前状态无法设定为禁用',
    'The current state of the module cannot be set to enabled'  => '模块当前状态无法设定为启用',
    'Module file updated'                                       => '模块文件有更新',
    'Please disable the module first'                           => '请先禁用模块',
    'Please disable the module before updating'                 => '更新前请先禁用模块',
    'The directory required by the module is occupied'          => '模块所需目录已被占用',
    'Install module'                                            => '安装模块',
    'Unload module'                                             => '卸载模块',
    'Update module'                                             => '更新模块',
    'Change module state'                                       => '改变模块状态',
    'Upload install module'                                     => '上传安装模块',
    'Please login to the official website account first'        => '请先使用BuildAdmin官网账户登录到模块市场~',
    'composer config %s conflict'                               => 'composer 配置项 %s 存在冲突',
];