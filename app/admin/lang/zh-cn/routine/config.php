<?php
return [
    'Basics'                                                                                            => '基础配置',
    'Mail'                                                                                              => '邮件配置',
    'Config group'                                                                                      => '配置分组',
    'Site Name'                                                                                         => '站点名称',
    'Backend entrance'                                                                                  => '自定义后台入口',
    'Config Quick entrance'                                                                             => '快捷配置入口',
    'Record number'                                                                                     => '备案号',
    'Version number'                                                                                    => '版本号',
    'time zone'                                                                                         => '时区',
    'No access ip'                                                                                      => '禁止访问IP',
    'smtp server'                                                                                       => 'SMTP 服务器',
    'smtp port'                                                                                         => 'SMTP 端口',
    'smtp user'                                                                                         => 'SMTP 用户名',
    'smtp pass'                                                                                         => 'SMTP 密码',
    'smtp verification'                                                                                 => 'SMTP 验证方式',
    'smtp sender mail'                                                                                  => 'SMTP 发件人邮箱',
    'Variable name'                                                                                     => '变量名',
    'Test mail sent successfully~'                                                                      => '测试邮件发送成功~',
    'This is a test email'                                                                              => '这是一封测试邮件',
    'Congratulations, receiving this email means that your email service has been configured correctly' => '恭喜您，收到此邮件代表您的邮件服务已配置正确；这是邮件主体 <b>在主体中可以使用Html!</b>',
    'The current page configuration item was updated successfully'                                      => '当前页配置项更新成功！',
    'Backend entrance rule'                                                                             => '后台入口请以 / 开头，且只包含数字和字母。',
];