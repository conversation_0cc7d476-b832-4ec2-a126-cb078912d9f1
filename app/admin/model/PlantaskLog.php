<?php

namespace app\admin\model;

use think\Model;

/**
 * PlantaskLog
 */
class PlantaskLog extends Model
{
    // 表主键
    protected $pk = 'log_id';

    // 表名
    protected $name = 'plantask_log';

    public $append = [
        'start_time_text',
        'end_time_text',
        'expend_time_text',
    ];


    /**
     * 记录日志
     * @param int $task_id 任务ID
     * @param int $status 状态 1 成功 2 失败
     * @param int $start_time 开始时间ms
     * @param int|null $end_time    结束时间ms
     * @param int|null $expend_time 耗时ms
     * @param string|null $response 响应信息
     * @return void
     */
    public static function log
    (
        int $task_id,
        int $status,
        float $start_time,
        ? float $end_time = 0,
        ? int $expend_time = 0,
        ? string $response = ''
    ) {
        $log = new self();
        $log->task_id = $task_id;
        $log->start_time = $start_time;
        $log->end_time = $end_time;
        $log->expend_time = $expend_time;
        $log->status = $status;
        $log->response = $response;
        $log->save();
    }


    public function getStartTimeTextAttr($value, $data)
    {
        if (empty($data['start_time'])) {
            return '';
        }
        return date("Y-m-d H:i:s", $data['start_time']);
    }
    public function getEndTimeTextAttr($value, $data)
    {
        if (empty($data['end_time'])) {
            return '';
        }
        return date("Y-m-d H:i:s", $data['end_time']);
    }
    public function getExpendTimeTextAttr($value, $data)
    {
        if (empty($data['expend_time'])) {
            return '';
        }
        return round($data['expend_time'] / 1000, 3);
    }
}
