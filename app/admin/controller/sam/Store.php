<?php

namespace app\admin\controller\sam;

use app\common\controller\Backend;

/**
 * 山姆门店管理
 */
class Store extends Backend
{
    /**
     * Store模型对象
     * @var object
     * @phpstan-var \app\common\model\sam\Store
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id', 'create_time', 'update_time'];

    protected string|array $quickSearchField = ['id', 'name', 'storeId', 'postcode', 'address', 'city', 'remark'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\common\model\sam\Store();
    }

    /**
     * 查看
     * @throws Throwable
     */
    public function index(): void
    {
        if ($this->request->param('select')) {
            $this->select();
        }

        list($where, $alias, $limit, $order) = $this->queryBuilder();
        $res = $this->model
            ->field($this->indexField)
            ->withJoin($this->withJoinTable, $this->withJoinType)
            ->alias($alias)
            ->where($where)
            ->order($order)
            ->paginate($limit);


        $this->success('', [
            'list'   => $res->items(),
            'total'  => $res->total(),
            'remark' => get_route_remark(),
        ]);
    }
    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\admin\library\traits\Backend 中对应的方法至此进行重写
     */
}