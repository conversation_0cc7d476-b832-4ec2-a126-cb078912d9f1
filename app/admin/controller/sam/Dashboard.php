<?php

namespace app\admin\controller\sam;

use app\common\controller\Backend;
use app\common\model\sam\Store;
use app\common\model\sam\Goods;
use app\common\model\sam\Categories;
use app\common\model\sam\store\Goods as StoreGoods;
use app\admin\model\PlantaskLog;
use app\admin\model\Plantask;
use think\facade\Cache;

/**
 * 山姆会员商店数据统计
 */
class Dashboard extends Backend
{
    /**
     * 无需登录的方法,*表示全部
     */
    protected array $noNeedLogin = [];

    /**
     * 无需鉴权的方法,*表示全部
     */
    protected array $noNeedPermission = [];

    public function initialize(): void
    {
        parent::initialize();
    }

    /**
     * 获取统计数据
     */
    public function index(): void
    {

        $cacheKey = 'sam_dashboard_stats';
        $stats = Cache::get($cacheKey);

        if (!$stats) {
            $stats = $this->getStatistics();
            // 缓存1分钟
            Cache::set($cacheKey, $stats, 60);
        }

        $this->success('', [
            'remark' => '欢迎使用山姆会员商店数据统计系统',
            'lastSyncTime' => date('Y-m-d H:i:s'),
            'syncStatus' => 'success',
            'syncStatusText' => '同步正常',
            'stats' => $stats
        ]);
    }

    /**
     * 获取统计数据
     */
    private function getStatistics(): array
    {
        // 门店总数及变化
        $storeCount = Store::count();
        $storeChange = $this->getStoreCountChange();

        // 商品总数及变化
        $goodsCount = Goods::count();
        $goodsChange = $this->getGoodsCountChange();

        // 商品分类数及变化
        $categoriesCount = Categories::where('status', 1)->count();
        $categoriesChange = $this->getCategoriesCountChange();

        // 今日同步次数及变化
        $syncCount = $this->getTodaySyncCount();
        $syncChange = $this->getSyncCountChange();

        return [
            'storeCount' => $storeCount,
            'storeChange' => $storeChange,
            'goodsCount' => $goodsCount,
            'goodsChange' => $goodsChange,
            'categoriesCount' => $categoriesCount,
            'categoriesChange' => $categoriesChange,
            'syncCount' => $syncCount,
            'syncChange' => $syncChange
        ];
    }

    /**
     * 获取商品数量趋势数据
     */
    public function goodsTrend(): void
    {
        $cacheKey = 'sam_goods_trend';
        $trendData = Cache::get($cacheKey);

        if (!$trendData) {
            $trendData = $this->getGoodsTrendData();
            Cache::set($cacheKey, $trendData, 1800); // 缓存30分钟
        }

        $this->success('', $trendData);
    }

    /**
     * 获取门店商品分布数据
     */
    public function storeDistribution(): void
    {
        $cacheKey = 'sam_store_distribution';
        $distributionData = Cache::get($cacheKey);

        if (!$distributionData) {
            $distributionData = $this->getStoreDistributionData();
            Cache::set($cacheKey, $distributionData, 1800);
        }

        $this->success('', $distributionData);
    }

    /**
     * 获取商品分类占比数据
     */
    public function categoryDistribution(): void
    {
        $cacheKey = 'sam_category_distribution';
        $categoryData = Cache::get($cacheKey);

        if (!$categoryData) {
            $categoryData = $this->getCategoryDistributionData();
            Cache::set($cacheKey, $categoryData, 1800);
        }

        $this->success('', $categoryData);
    }

    /**
     * 获取门店地区分布数据
     */
    public function storeRegion(): void
    {
        $cacheKey = 'sam_store_region';
        $regionData = Cache::get($cacheKey);

        if (!$regionData) {
            $regionData = $this->getStoreRegionData();
            Cache::set($cacheKey, $regionData, 1800);
        }

        $this->success('', $regionData);
    }

    /**
     * 获取最近活动数据
     */
    public function recentActivities(): void
    {
        $activities = $this->getRecentActivitiesData();
        $this->success('', $activities);
    }

    /**
     * 获取今日同步次数
     */
    private function getTodaySyncCount(): int
    {
        $todayStart = strtotime(date('Y-m-d'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        // 获取山姆相关的任务ID
        $samsTasks = Plantask::where('goal', '\app\api\controller\Sams\SamsGoods@syncGoodsData')
            ->column('task_id');

        if (empty($samsTasks)) {
            return 0;
        }

        // 统计今日这些任务的执行次数
        $todaySyncCount = PlantaskLog::whereIn('task_id', $samsTasks)
            ->where('start_time', '>=', $todayStart)
            ->where('start_time', '<=', $todayEnd)
            ->where('status', 1) // 只统计成功的执行
            ->count();

        return $todaySyncCount;
    }

    /**
     * 获取门店数量变化
     */
    private function getStoreCountChange(): array
    {
        $todayStart = strtotime(date('Y-m-d'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));
        $yesterdayStart = strtotime(date('Y-m-d', strtotime('-1 day')));
        $yesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));

        // 今日新增门店数
        $todayCount = Store::where('create_time', '>=', $todayStart)
            ->where('create_time', '<=', $todayEnd)
            ->count();

        // 昨日新增门店数
        $yesterdayCount = Store::where('create_time', '>=', $yesterdayStart)
            ->where('create_time', '<=', $yesterdayEnd)
            ->count();

        $change = $todayCount - $yesterdayCount;

        return [
            'value' => $change,
            'type' => $change > 0 ? 'increase' : ($change < 0 ? 'decrease' : 'stable'),
            'text' => $change > 0 ? "+{$change} 较昨日" : ($change < 0 ? "{$change} 较昨日" : "无变化")
        ];
    }

    /**
     * 获取商品数量变化
     */
    private function getGoodsCountChange(): array
    {
        $todayStart = strtotime(date('Y-m-d'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));
        $yesterdayStart = strtotime(date('Y-m-d', strtotime('-1 day')));
        $yesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));

        // 今日新增商品数
        $todayCount = Goods::where('create_time', '>=', $todayStart)
            ->where('create_time', '<=', $todayEnd)
            ->count();

        // 昨日新增商品数
        $yesterdayCount = Goods::where('create_time', '>=', $yesterdayStart)
            ->where('create_time', '<=', $yesterdayEnd)
            ->count();

        $change = $todayCount - $yesterdayCount;

        return [
            'value' => $change,
            'type' => $change > 0 ? 'increase' : ($change < 0 ? 'decrease' : 'stable'),
            'text' => $change > 0 ? "+{$change} 今日新增" : ($change < 0 ? "{$change} 今日减少" : "无变化")
        ];
    }

    /**
     * 获取分类数量变化
     */
    private function getCategoriesCountChange(): array
    {
        $lastWeekStart = strtotime(date('Y-m-d', strtotime('-7 days')));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        // 本周新增分类数
        $thisWeekCount = Categories::where('status', 1)
            ->where('create_time', '>=', $lastWeekStart)
            ->where('create_time', '<=', $todayEnd)
            ->count();

        // 分类变化较少，通常以周为单位统计
        return [
            'value' => $thisWeekCount,
            'type' => $thisWeekCount > 0 ? 'increase' : 'stable',
            'text' => $thisWeekCount > 0 ? "+{$thisWeekCount} 本周新增" : "无变化"
        ];
    }

    /**
     * 获取同步次数变化
     */
    private function getSyncCountChange(): array
    {
        $todayStart = strtotime(date('Y-m-d'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));
        $yesterdayStart = strtotime(date('Y-m-d', strtotime('-1 day')));
        $yesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));

        // 获取山姆相关的任务ID
        $samsTasks = Plantask::where('goal', '\app\api\controller\Sams\SamsGoods@syncGoodsData')
            ->column('task_id');

        if (empty($samsTasks)) {
            return [
                'value' => 0,
                'type' => 'stable',
                'text' => '无变化'
            ];
        }

        // 今日同步次数
        $todaySync = PlantaskLog::whereIn('task_id', $samsTasks)
            ->where('start_time', '>=', $todayStart)
            ->where('start_time', '<=', $todayEnd)
            ->where('status', 1)
            ->count();

        // 昨日同步次数
        $yesterdaySync = PlantaskLog::whereIn('task_id', $samsTasks)
            ->where('start_time', '>=', $yesterdayStart)
            ->where('start_time', '<=', $yesterdayEnd)
            ->where('status', 1)
            ->count();

        $change = $todaySync - $yesterdaySync;

        return [
            'value' => $change,
            'type' => $change > 0 ? 'increase' : ($change < 0 ? 'decrease' : 'stable'),
            'text' => $change > 0 ? "+{$change} 较昨日" : ($change < 0 ? "{$change} 较昨日" : "无变化")
        ];
    }

    /**
     * 获取商品趋势数据
     */
    private function getGoodsTrendData(): array
    {
        // 获取最近7个月的商品数量趋势
        $months = [];
        $data = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m', strtotime("-{$i} months"));
            $months[] = date('n月', strtotime("-{$i} months"));

            // 统计该月份的商品数量（使用时间戳）
            $endTime = strtotime($date . '-31 23:59:59');
            $count = Goods::where('create_time', '<=', $endTime)->count();
            $data[] = $count;
        }

        return [
            'months' => $months,
            'data' => $data
        ];
    }

    /**
     * 获取门店商品分布数据
     */
    private function getStoreDistributionData(): array
    {
        // 使用Store模型获取门店及其商品数量
        $storeData = Store::withCount('storeGoods')
            ->order('store_goods_count', 'desc')
            ->limit(6)
            ->select();

        $stores = [];
        $data = [];

        foreach ($storeData as $store) {
            $stores[] = $store->name ?? '门店' . $store->storeId;
            $data[] = (int)$store->store_goods_count;
        }

        return [
            'stores' => $stores,
            'data' => $data
        ];
    }

    /**
     * 获取商品分类占比数据
     */
    private function getCategoryDistributionData(): array
    {
        // 使用Categories模型获取一级分类及其商品数量
        $categories = Categories::where('level', 1)
            ->where('status', 1)
            ->limit(8)
            ->select();

        $categoryNames = [];
        $categoryData = [];

        foreach ($categories as $category) {
            // 统计该分类下的商品数量（使用catLv1字段）
            $goodsCount = Goods::where('catLv1', $category->groupingId)
                ->where('status', 1)
                ->count();

            $categoryNames[] = $category->name ?? '分类' . $category->groupingId;
            $categoryData[] = $goodsCount;
        }

        // 过滤掉商品数量为0的分类
        $validData = [];
        foreach ($categoryNames as $index => $name) {
            if ($categoryData[$index] > 0) {
                $validData[$name] = $categoryData[$index];
            }
        }

        // 按商品数量排序
        arsort($validData);

        return [
            'categories' => array_keys($validData),
            'data' => array_values($validData)
        ];
    }

    /**
     * 获取门店地区分布数据
     */
    private function getStoreRegionData(): array
    {
        // 方案1：直接按城市统计（推荐，更直观）
        if ($this->shouldUseDirectCityStats()) {
            return $this->getDirectCityStats();
        }

        // 方案2：按地区分组统计
        return $this->getRegionGroupStats();
    }

    /**
     * 判断是否使用直接城市统计
     */
    private function shouldUseDirectCityStats(): bool
    {
        // 如果门店数量较少（<20家），直接显示城市
        $storeCount = Store::count();
        return $storeCount < 20;
    }

    /**
     * 直接按城市统计门店分布
     */
    private function getDirectCityStats(): array
    {
        // 直接按城市分组统计
        $cityStats = Store::field('city, COUNT(*) as store_count')
            ->where('city', '<>', '')
            ->where('city', 'not null')
            ->group('city')
            ->having('store_count > 0')
            ->order('store_count', 'desc')
            ->limit(8)
            ->select()
            ->toArray();

        $cities = [];
        $data = [];

        foreach ($cityStats as $stat) {
            $cities[] = $stat['city'];
            $data[] = (int)$stat['store_count'];
        }

        return [
            'regions' => $cities,
            'data' => $data
        ];
    }

    /**
     * 按地区分组统计门店分布
     */
    private function getRegionGroupStats(): array
    {
        // 定义主要城市到地区的映射关系
        $cityRegionMap = [
            // 华南地区（广东、广西、海南）
            '深圳' => '华南地区', '广州' => '华南地区', '东莞' => '华南地区', '佛山' => '华南地区',
            '珠海' => '华南地区', '中山' => '华南地区', '惠州' => '华南地区', '江门' => '华南地区',
            '南宁' => '华南地区', '柳州' => '华南地区', '海口' => '华南地区', '三亚' => '华南地区',

            // 华东地区（上海、江苏、浙江、安徽、福建、江西、山东）
            '上海' => '华东地区', '杭州' => '华东地区', '南京' => '华东地区', '苏州' => '华东地区',
            '无锡' => '华东地区', '宁波' => '华东地区', '温州' => '华东地区', '合肥' => '华东地区',
            '福州' => '华东地区', '厦门' => '华东地区', '南昌' => '华东地区', '济南' => '华东地区',
            '青岛' => '华东地区', '烟台' => '华东地区',

            // 华北地区（北京、天津、河北、山西、内蒙古）
            '北京' => '华北地区', '天津' => '华北地区', '石家庄' => '华北地区', '唐山' => '华北地区',
            '太原' => '华北地区', '大同' => '华北地区', '呼和浩特' => '华北地区', '包头' => '华北地区',

            // 华中地区（河南、湖北、湖南）
            '武汉' => '华中地区', '郑州' => '华中地区', '长沙' => '华中地区', '洛阳' => '华中地区',
            '株洲' => '华中地区', '湘潭' => '华中地区', '开封' => '华中地区',

            // 西南地区（重庆、四川、贵州、云南、西藏）
            '成都' => '西南地区', '重庆' => '西南地区', '昆明' => '西南地区', '贵阳' => '西南地区',
            '绵阳' => '西南地区', '德阳' => '西南地区', '曲靖' => '西南地区', '遵义' => '西南地区',

            // 西北地区（陕西、甘肃、青海、宁夏、新疆）
            '西安' => '西北地区', '兰州' => '西北地区', '银川' => '西北地区', '西宁' => '西北地区',
            '乌鲁木齐' => '西北地区', '宝鸡' => '西北地区', '咸阳' => '西北地区',

            // 东北地区（辽宁、吉林、黑龙江）
            '沈阳' => '东北地区', '大连' => '东北地区', '长春' => '东北地区', '哈尔滨' => '东北地区',
            '鞍山' => '东北地区', '吉林' => '东北地区', '齐齐哈尔' => '东北地区', '大庆' => '东北地区',
        ];

        // 获取所有门店的城市信息
        $stores = Store::field('city')
            ->where('city', '<>', '')
            ->where('city', 'not null')
            ->select();

        $regionCount = [];

        foreach ($stores as $store) {
            $city = trim($store->city);

            // 查找城市对应的地区
            $region = $this->getCityRegion($city, $cityRegionMap);

            // 统计地区数量
            if (!isset($regionCount[$region])) {
                $regionCount[$region] = 0;
            }
            $regionCount[$region]++;
        }

        // 按数量排序
        arsort($regionCount);

        return [
            'regions' => array_keys($regionCount),
            'data' => array_values($regionCount)
        ];
    }

    /**
     * 根据城市名称获取对应地区
     */
    private function getCityRegion(string $city, array $cityRegionMap): string
    {
        // 精确匹配
        if (isset($cityRegionMap[$city])) {
            return $cityRegionMap[$city];
        }

        // 模糊匹配
        foreach ($cityRegionMap as $cityName => $regionName) {
            if (strpos($city, $cityName) !== false) {
                return $regionName;
            }
        }

        return '其他地区';
    }

    /**
     * 获取最近活动数据
     */
    private function getRecentActivitiesData(): array
    {
        $activities = [];

        // 1. 获取最近新增的商品
        $recentGoods = $this->getRecentGoodsActivities();
        $activities = array_merge($activities, $recentGoods);

        // 2. 获取最近更新的门店
        $recentStores = $this->getRecentStoreActivities();
        $activities = array_merge($activities, $recentStores);

        // 3. 获取最近的门店商品同步
        $recentSync = $this->getRecentSyncActivities();
        $activities = array_merge($activities, $recentSync);

        // 4. 获取最近的分类更新
        $recentCategories = $this->getRecentCategoryActivities();
        $activities = array_merge($activities, $recentCategories);

        // 按时间排序并限制数量
        usort($activities, function($a, $b) {
            return $b['timestamp'] - $a['timestamp'];
        });

        // 移除timestamp字段并限制返回数量
        $result = array_slice($activities, 0, 10);
        foreach ($result as &$activity) {
            unset($activity['timestamp']);
        }

        return $result;
    }

    /**
     * 获取最近商品新增活动
     */
    private function getRecentGoodsActivities(): array
    {
        $activities = [];
        $oneDayAgo = time() - 86400; // 24小时前

        // 获取最近24小时新增的商品
        $recentGoods = Goods::where('create_time', '>=', $oneDayAgo)
            ->order('create_time', 'desc')
            ->limit(5)
            ->select();

        foreach ($recentGoods as $goods) {
            $activities[] = [
                'type' => 'success',
                'title' => '新增商品',
                'description' => '新增商品：' . ($goods->name ?? '商品ID:' . $goods->id),
                'time' => $this->formatTimeAgo($goods->create_time),
                'timestamp' => $goods->create_time
            ];
        }

        return $activities;
    }

    /**
     * 获取最近门店更新活动
     */
    private function getRecentStoreActivities(): array
    {
        $activities = [];
        $oneDayAgo = time() - 86400;

        // 获取最近24小时有更新的门店
        $recentStores = Store::where('update_time', '>=', $oneDayAgo)
            ->where('update_time', '>', 'create_time') // 确保是更新而不是新增
            ->order('update_time', 'desc')
            ->limit(3)
            ->select();

        foreach ($recentStores as $store) {
            $activities[] = [
                'type' => 'info',
                'title' => '门店更新',
                'description' => ($store->name ?? '门店ID:' . $store->storeId) . ' 信息已更新',
                'time' => $this->formatTimeAgo($store->update_time),
                'timestamp' => $store->update_time
            ];
        }

        return $activities;
    }

    /**
     * 获取最近同步活动
     */
    private function getRecentSyncActivities(): array
    {
        $activities = [];
        $oneDayAgo = time() - 86400;

        // 获取山姆相关的任务ID
        $samsTasks = Plantask::where('task_name', 'like', '%山姆%')
            ->where('status', '<>', 2)
            ->column('task_id', 'task_name');

        if (empty($samsTasks)) {
            return $activities;
        }

        // 获取最近24小时的同步任务执行记录
        $recentLogs = PlantaskLog::whereIn('task_id', array_keys($samsTasks))
            ->where('start_time', '>=', $oneDayAgo)
            ->where('status', 1) // 只获取成功的执行
            ->order('start_time', 'desc')
            ->limit(5)
            ->select();

        foreach ($recentLogs as $log) {
            $taskName = $samsTasks[$log->task_id] ?? '未知任务';

            // 解析任务名称，提取门店信息
            $description = $this->parseTaskDescription($taskName, $log);

            $activities[] = [
                'type' => 'info',
                'title' => '数据同步',
                'description' => $description,
                'time' => $this->formatTimeAgo($log->start_time),
                'timestamp' => $log->start_time
            ];
        }

        return $activities;
    }

    /**
     * 解析任务描述
     */
    private function parseTaskDescription(string $taskName, $log): string
    {
        // 根据任务名称生成描述
        if (strpos($taskName, '门店') !== false) {
            // 提取门店ID或批次信息
            if (preg_match('/门店(\d+)/', $taskName, $matches)) {
                $storeId = $matches[1];
                $store = Store::where('storeId', $storeId)->find();
                $storeName = $store ? $store->name : "门店{$storeId}";
                return "{$storeName} 商品数据同步完成";
            } elseif (strpos($taskName, '批次') !== false) {
                return "批量门店商品数据同步完成";
            }
            return "门店数据同步完成";
        } elseif (strpos($taskName, '分类') !== false) {
            return "商品分类数据同步完成";
        } elseif (strpos($taskName, '云仓') !== false) {
            return "云仓数据同步完成";
        } else {
            return "山姆数据同步完成";
        }
    }

    /**
     * 获取最近分类活动
     */
    private function getRecentCategoryActivities(): array
    {
        $activities = [];
        $oneWeekAgo = time() - 604800; // 7天前

        // 获取最近一周新增的分类
        $recentCategories = Categories::where('create_time', '>=', $oneWeekAgo)
            ->where('status', 1)
            ->order('create_time', 'desc')
            ->limit(2)
            ->select();

        foreach ($recentCategories as $category) {
            $activities[] = [
                'type' => 'warning',
                'title' => '新增分类',
                'description' => '新增商品分类：' . ($category->name ?? '分类ID:' . $category->groupingId),
                'time' => $this->formatTimeAgo($category->create_time),
                'timestamp' => $category->create_time
            ];
        }

        return $activities;
    }

    /**
     * 格式化时间为相对时间
     */
    private function formatTimeAgo(int $timestamp): string
    {
        $now = time();
        $diff = $now - $timestamp;

        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return $minutes . '分钟前';
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return $hours . '小时前';
        } elseif ($diff < 604800) {
            $days = floor($diff / 86400);
            return $days . '天前';
        } else {
            return date('m-d H:i', $timestamp);
        }
    }
}