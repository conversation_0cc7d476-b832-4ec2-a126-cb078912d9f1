<?php

namespace app\admin\controller\sam;

use app\common\controller\Backend;

/**
 * 山姆账号管理
 */
class Token extends Backend
{
    /**
     * Token模型对象
     * @var object
     * @phpstan-var \app\common\model\sam\Token
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id', 'create_time', 'update_time'];

    protected string|array $quickSearchField = ['id', 'phone', 'token', 'remark'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\common\model\sam\Token();
    }


    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\admin\library\traits\Backend 中对应的方法至此进行重写
     */
}