<?php

namespace app\admin\controller\sam;

use app\common\controller\Backend;
use ba\Tree;

/**
 * 山姆分类管理
 */
class Categories extends Backend
{
    /**
     * Categories模型对象
     * @var object
     * @phpstan-var \app\common\model\sam\Categories
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id', 'create_time', 'update_time'];

    //protected string|array $quickSearchField = ['id', 'name', 'groupingId', 'level', 'remark'];
    protected string|array $quickSearchField = ['name'];
    private Tree $tree;

    public function initialize(): void
    {
        parent::initialize();
        $this->tree  = Tree::instance();
        $this->model = new \app\common\model\sam\Categories();
    }

    /**
     * 重写查看方法
     * @throws Throwable
     */
    public function index(): void
    {
        $this->request->filter(['strip_tags', 'trim']);

        list($where, $alias) = $this->queryBuilder();
        $res = $this->model
            ->field($this->indexField)
            ->alias($alias)
            ->where($where)
            ->select()
            ->toArray();

        // 使用groupingId作为主键，pid作为父级字段
        $res = $this->tree->assembleChild($res, 'pid', 'groupingId');
        if ($this->request->param('select')) {
            $res = $this->tree->assembleTree($this->tree->getTreeArray($res));
        }

        $this->success('', [
            'list'   => $res,
            'remark' => get_route_remark(),
        ]);
    }

    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\admin\library\traits\Backend 中对应的方法至此进行重写
     */
}