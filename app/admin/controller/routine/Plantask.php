<?php

namespace app\admin\controller\routine;

use app\admin\model\PlantaskLog;
use app\common\controller\Backend;
use modules\plantask\library\Helper;
use modules\plantask\library\Parser;
use modules\plantask\library\Redis;
use modules\plantask\library\Task as TaskLib;
use think\facade\Config;
use Throwable;

/**
 * 
 */
class Plantask extends Backend
{
    /**
     * Plantask模型对象
     * @var object
     * @phpstan-var \app\admin\model\Plantask
     */
    protected object $model;

    protected string|array $defaultSortField = ['weigh desc', 'task_id desc'];

    protected string|array $quickSearchField = ['task_name', 'remark'];


    /**
     * 无需登录的方法，访问本控制器的此方法，无需管理员登录,有单独的验证
     * @var array
     */
    protected array $noNeedLogin = [];

    /**
     * 无需鉴权的方法 
     * @var array
     */
    protected array $noNeedPermission = ['getServerStatus'];



    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\admin\model\Plantask();
    }


    /**
     * 查看
     * @throws Throwable
     */
    public function index(): void
    {
        if ($this->request->param('select')) {
            $this->select();
        }
        $status = $this->request->param('status');
        $unread_error = $this->request->param('unread_error');
        list($where, $alias, $limit, $order) = $this->queryBuilder();
        if (!empty($status)) {
            $where[] = [
                'status',
                'in',
                match (intval($status)) {
                    1 => [0, 1, 2, 10],
                    3 => [3, 4],
                    default => []
                }
            ];
        }
        if(!empty($unread_error)){
            $task_ids = PlantaskLog::distinct(true)->where('unread_err', 1)->order('start_time asc')->column('task_id');
            $where[] = [
                'task_id',
                'in',
                $task_ids
            ];
        }
        $res = $this->model
            ->field($this->indexField)
            ->withJoin($this->withJoinTable, $this->withJoinType)
            ->alias($alias)
            ->where($where)
            ->order($order)
            ->paginate($limit);
        $task_ids = $res->column('task_id');
        $err_count = PlantaskLog::whereIn('task_id', $task_ids)->where('unread_err', 1)->group('task_id')->column('COUNT(log_id)', 'task_id');
        $res->each(function ($item) use ($err_count) {
            if (TaskLib::getTaskStyleByRule($item->rule) === TaskLib::TIMER_STYLE) {
                $item['rule'] = [Helper::formatSeconds($item['rule'])];
            } else {
                $item['rule'] = preg_split('/[\\s]+/i', trim($item['rule']));
            }
            if ($item['repeat_times'] === 1) {
                $item['rule'] = [];
            }
            if (in_array($item['status'], [0, 1, 2, 10])) {
                $item['status'] = 1;
            } else if (in_array($item['status'], [3])) {
                $item['status'] = 3;
            } else {
                $item['status'] = 4;
            }
            $item['unread_error_count'] = $err_count[$item['task_id']] ?? 0;
        });
        $unread_error_total = PlantaskLog::where('unread_err', 1)->count();
        $server_status = Helper::serverIsRunning();
        try {
            $redis = Redis::instance();
            $last_time = $redis->get('task:distribute:time');
            $listen_task_interval = Config::get('plantask.listen_task_interval');
            $now_time = time();
            if ($last_time && $last_time + $listen_task_interval + 2 > $now_time) {
                $server_status = true;
            }
            $redis->close();
        } catch (\Throwable $th) {
            //throw $th;
        }

        $this->success('', [
            'list'   => $res->items(),
            'total'  => $res->total(),
            'remark' => get_route_remark(),
            'server_status' => $server_status,
            'unread_error_total'    =>  $unread_error_total
        ]);
    }

    /**
     * 添加
     */
    public function add(): void
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (!$data) {
                $this->error(__('Parameter %s can not be empty', ['']));
            }

            $result = false;
            try {
                if (!empty($data['params'])) {
                    $data['params'] = json_decode(html_entity_decode($data['params']), true);
                    if (is_null($data['params'])) {
                        throw new \think\Exception('params参数必须为可解析的json格式');
                    }
                    $data['params'] = json_encode($data['params'], JSON_UNESCAPED_UNICODE);
                }

                if (!empty($data['rule']) && TaskLib::getTaskStyleByRule($data['rule']) === TaskLib::CRON_STYLE) {
                    $parser = new Parser();
                    $parser->checkRule($data['rule']);
                }

                $task_id = TaskLib::add(
                    $data['task_name'],
                    $data['type'],
                    $data['goal'] ?? '',
                    $data['params'] ?? '',
                    $data['repeat_times'],
                    $data['rule'] ?? '',
                    strtotime($data['run_time'])
                );
            } catch (Throwable $e) {
                $this->error($e->getMessage());
            }
            $this->success(__('Added successfully'));
        }

        $this->error(__('Parameter error'));
    }

    /**
     * 编辑
     * @throws Throwable
     */
    public function edit(): void
    {
        $pk  = $this->model->getPk();
        $id  = $this->request->param($pk);
        $row = $this->model->find($id);
        if (!$row) {
            $this->error(__('Record not found'));
        }

        $dataLimitAdminIds = $this->getDataLimitAdminIds();
        if ($dataLimitAdminIds && !in_array($row[$this->dataLimitField], $dataLimitAdminIds)) {
            $this->error(__('You have no permission'));
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (!$data) {
                $this->error(__('Parameter %s can not be empty', ['']));
            }

            $data   = $this->excludeFields($data);
            $result = false;
            try {

                if (!empty($data['params'])) {
                    $data['params'] = json_decode(html_entity_decode($data['params']), true);
                    if (is_null($data['params'])) {
                        throw new \think\Exception('params参数必须为可解析的json格式');
                    }
                    $data['params'] = json_encode($data['params'], JSON_UNESCAPED_UNICODE);
                }

                if (!empty($data['rule']) && TaskLib::getTaskStyleByRule($data['rule']) === TaskLib::CRON_STYLE) {
                    $parser = new Parser();
                    $parser->checkRule($data['rule']);
                }

                if (!empty($data['run_time'])) {
                    $data['run_time'] = strtotime($data['run_time']);
                }

                if (isset($data['status']) && intval($data['status']) === 0) {
                    $result = TaskLib::start($id);
                } else if (isset($data['status']) && intval($data['status']) === 3) {
                    $result = TaskLib::stop($id);
                } else {
                    $result = TaskLib::edit($row['task_id'], $data);
                }
            } catch (Throwable $e) {
                $this->error($e->getMessage());
            }
            if ($result !== false) {
                $this->success('操作成功');
            } else {
                $this->error('操作失败');
            }
        }

        if (!empty($row['run_time'])) {
            $row['run_time'] = date('Y-m-d H:i:s', $row['run_time']);
        }

        //重复次数
        $row['repeat_times_type'] = match ($row['repeat_times']) {
            0 => 0,
            1 => 1,
            default => 2
        };
        $row['rule_type'] = TaskLib::getTaskStyleByRule($row['rule']) === 'TIMER' ? 'seconds' : 'crontab';
        $row['cron_rule'] = null;
        if ($row['rule_type'] === 'crontab') {
            $row['cron_rule'] = $row['rule'] ? explode(' ', $row['rule']) : null;
        }

        $this->success('', [
            'row' => $row
        ]);
    }


    /**
     * 删除
     * @param array $ids
     * @throws Throwable
     */
    public function del(array $ids = []): void
    {
        if (!$this->request->isDelete() || !$ids) {
            $this->error(__('Parameter error'));
        }

        $where             = [];
        $dataLimitAdminIds = $this->getDataLimitAdminIds();
        if ($dataLimitAdminIds) {
            $where[] = [$this->dataLimitField, 'in', $dataLimitAdminIds];
        }

        $pk      = $this->model->getPk();
        $where[] = [$pk, 'in', $ids];

        $count = 1;
        $data  = $this->model->where($where)->select();

        try {
            foreach ($data as $v) {
                TaskLib::delete($v->task_id);
                PlantaskLog::where('task_id', $v->task_id)->delete();
            }
        } catch (Throwable $e) {
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success(__('Deleted successfully'));
        } else {
            $this->error(__('No rows were deleted'));
        }
    }

    /**
     * 检查cron表达式
     */
    public function checkCronRule()
    {
        $rule = $this->request->param('rule');
        try {
            $parser = new Parser();
            $parser->checkRule($rule);

            $runtime_list = [];
            $runtime = time();
            for ($i = 0; $i < 10; $i++) {
                $runtime = $parser->nextRunTime($rule, $runtime);
                if (!$runtime) {
                    throw new \think\Exception('cron表达式格式不正确');
                }
                $runtime_list[] = date('Y-m-d H:i:s', $runtime);
            }
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->success('', compact('runtime_list'));
    }

    /**
     * 获取日志
     * @return void
     */
    public function getTasklog()
    {
        $task_id = $this->request->param('task_id');
        $start_time = $this->request->param('start_time');
        $status = $this->request->param('status');
        try {
            $condition = [];
            $condition[] = ['task_id', '=', $task_id];
            if (!empty($start_time) && count($start_time) === 2) {
                $condition[] = ['start_time', 'between', [strtotime($start_time[0]), strtotime($start_time[1])]];
            }
            if (isset($status) && $status !== '' && in_array($status, [0, 1])) {
                $condition[] = ['status', '=', $status];
            }
            $list = PlantaskLog::where($condition)->order('unread_err DESC,start_time DESC')->paginate(10)->each(function ($item) {
                $item['response_short'] = $item['response'] ? ( mb_strlen($item['response']) > 50 ? mb_substr($item['response'], 0, 50) . '...' : $item['response']) : '';
            });
            $log_ids = $list->column('log_id');
            if ($log_ids) {
                PlantaskLog::whereIn('log_id', $log_ids)->update(['unread_err' => 0]);
            }
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->success('', $list);
    }

    /**
     * 清空日志
     * @return void
     */
    public function deleteTask()
    {
        $task_id = $this->request->param('task_id');
        $start_time = $this->request->param('start_time');
        try {
            if (!$task_id) {
                throw new \think\Exception('参数有误');
            }
            $condition = [];
            $condition[] = ['task_id', '=', $task_id];
            if (!empty($start_time) && count($start_time) === 2) {
                $condition[] = ['start_time', 'between', [strtotime($start_time[0]), strtotime($start_time[1])]];
            }
            PlantaskLog::where($condition)->delete();
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->success('操作成功');
    }


    /**
     * 下载日志
     */
    public function downloadLog()
    {
        $task_id = $this->request->param('task_id');
        $start_time = $this->request->param('start_time');
        try {
            if (!$task_id) {
                throw new \think\Exception('参数有误');
            }
            $condition = [];
            $condition[] = ['task_id', '=', $task_id];
            if (!empty($start_time) && count($start_time) === 2) {
                $condition[] = ['start_time', 'between', [strtotime($start_time[0]), strtotime($start_time[1])]];
            }
            $loglist = PlantaskLog::where($condition)->order('start_time asc')->cursor();
            $file_content = '';
            foreach ($loglist as $log) {
                $file_content .= $log['start_time_text'] . '       ' . ($log->status ? '执行成功' : '执行失败') .  '       耗时: ' . $log['expend_time_text'] . ' ms' . PHP_EOL;
                $file_content .= '----------------------------------------------------------------------------' . PHP_EOL;
                $file_content .= $log->response . PHP_EOL . PHP_EOL;
            }
            $file_path = runtime_path() . 'taskId_' . $task_id . '_' . date('YmdHis') . '.log';

            file_put_contents($file_path, $file_content);
            Helper::sendToBrowser($file_path);
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->success();
    }

    /**
     * 测试执行一次
     */
    public function executeOnce()
    {
        $task_id = $this->request->param('task_id');
        try {
            TaskLib::testHandle($task_id);
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->success('执行成功');
    }
}
