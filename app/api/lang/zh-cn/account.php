<?php
return [
    'username'                                                                 => '用户名',
    'nickname'                                                                 => '昵称',
    'birthday'                                                                 => '生日',
    'email'                                                                    => '电子邮箱',
    'mobile'                                                                   => '手机号',
    'password'                                                                 => '密码',
    'captcha'                                                                  => '验证码',
    'Old password error'                                                       => '旧密码错误',
    'Data updated successfully~'                                               => '资料更新成功~',
    'Please input correct password'                                            => '请输入正确的密码',
    'nicknameChsDash'                                                          => '用户名只能是汉字、字母、数字和下划线_及破折号-',
    'Password has been changed~'                                               => '密码已修改~',
    'Password has been changed, please login again~'                           => '密码已修改，请重新登录~',
    'Account does not exist~'                                                  => '账户不存在~',
    'Failed to modify password, please try again later~'                       => '修改密码失败，请稍后重试~',
    'Please enter the correct verification code'                               => '请输入正确的验证码！',
    '%s has been registered'                                                   => '%s已被注册，请直接登录~',
    'email format error'                                                       => '电子邮箱格式错误！',
    'mobile format error'                                                      => '手机号格式错误！',
    'You need to verify your account before modifying the binding information' => '您需要先通过账户验证才能修改绑定信息！',
    'Password error'                                                           => '密码错误！',
    'email is occupied'                                                        => '电子邮箱地址已被占用！',
    'mobile is occupied'                                                       => '手机号已被占用！',
];