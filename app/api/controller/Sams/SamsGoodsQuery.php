<?php

namespace app\api\controller\Sams;

use app\common\model\sam\Goods;
use app\common\model\sam\store\Goods as StoreGoods;
use app\common\model\sam\Store;
use think\facade\Log;
use think\Request;

/**
 * 山姆商品价格库存查询
 */
class SamsGoodsQuery extends SamsBase
{
    /**
     * 一键查询商品价格变动和库存状态
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function queryGoodsPriceAndStock(Request $request)
    {
        try {
            // 获取参数
            $goodsNames = $request->param('goods_names', '', 'trim');
            $storeId = $request->param('store_id', 0, 'intval');
            $days = $request->param('days', 7, 'intval'); // 查询最近几天的价格变动，默认7天
            
            if (empty($goodsNames)) {
                return $this->error('请输入商品名称');
            }
            
            // 处理商品名称（支持多种分隔符）
            $namesArray = $this->parseGoodsNames($goodsNames);
            
            if (empty($namesArray)) {
                return $this->error('请输入有效的商品名称');
            }
            
            // 查询结果
            $results = [];
            $statistics = [
                'total_query' => count($namesArray),
                'found_goods' => 0,
                'price_changed' => 0,
                'out_of_stock' => 0,
                'delisted' => 0
            ];
            
            foreach ($namesArray as $goodsName) {
                $result = $this->queryGoodsInfo($goodsName, $storeId, $days);
                $results[] = $result;
                
                // 统计
                if ($result['status'] === 'found') {
                    $statistics['found_goods']++;
                    if ($result['price_changed']) {
                        $statistics['price_changed']++;
                    }
                    if ($result['current_stock'] == 0) {
                        $statistics['out_of_stock']++;
                    }
                } else {
                    $statistics['delisted']++;
                }
            }
            
            return $this->success('查询完成', [
                'results' => $results,
                'statistics' => $statistics,
                'query_time' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\Throwable $e) {
            Log::error('商品查询失败: ' . $e->getMessage());
            return $this->error('查询失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 解析商品名称字符串
     *
     * @param string $goodsNames
     * @return array
     */
    protected function parseGoodsNames($goodsNames)
    {
        // 只支持换行分隔，一行一个商品名称
        $names = preg_split('/[\r\n]+/', $goodsNames);

        // 清理空白和去重
        $cleanNames = [];
        foreach ($names as $name) {
            $name = trim($name);
            if (!empty($name) && !in_array($name, $cleanNames)) {
                $cleanNames[] = $name;
            }
        }

        return $cleanNames;
    }
    
    /**
     * 查询单个商品信息
     * 
     * @param string $goodsName
     * @param int $storeId
     * @param int $days
     * @return array
     */
    protected function queryGoodsInfo($goodsName, $storeId, $days)
    {
        try {
            // 查找商品
            $goods = Goods::where('name', 'like', '%' . $goodsName . '%')
                          ->where('status', 1)
                          ->find();
            
            if (!$goods) {
                return [
                    'goods_name' => $goodsName,
                    'status' => 'delisted',
                    'message' => '商品已下架或不存在',
                    'spuId' => null,
                    'current_price' => null,
                    'current_stock' => null,
                    'price_changed' => false,
                    'price_change_info' => null
                ];
            }
            
            // 查询商品在门店的信息
            $storeGoodsQuery = StoreGoods::where('sam_goods_id', $goods->spuId)
                                        ->where('status', 1);
            
            if ($storeId > 0) {
                $storeGoodsQuery->where('sam_store_id', $storeId);
            }
            
            $storeGoods = $storeGoodsQuery->order('update_time', 'desc')->find();
            
            if (!$storeGoods) {
                return [
                    'goods_name' => $goodsName,
                    'status' => 'delisted',
                    'message' => '商品在指定门店已下架',
                    'spuId' => $goods->spuId,
                    'matched_name' => $goods->name,
                    'current_price' => null,
                    'current_stock' => null,
                    'price_changed' => false,
                    'price_change_info' => null
                ];
            }
            
            // 查询价格历史用于分析价格变动
            $startTime = time() - ($days * 24 * 3600);
            $priceHistory = $this->getPriceHistory($goods->spuId, $storeGoods->sam_store_id, $startTime);

            // 如果指定天数内没有历史数据，则查询所有历史数据
            if (empty($priceHistory)) {
                $priceHistory = $this->getPriceHistory($goods->spuId, $storeGoods->sam_store_id, 0);
            }

            // 分析价格变动
            $priceChangeInfo = $this->analyzePriceChange($priceHistory, $storeGoods->price);
            
            return [
                'goods_name' => $goodsName,
                'status' => 'found',
                'message' => '商品找到',
                'spuId' => $goods->spuId,
                'matched_name' => $goods->name,
                'store_id' => $storeGoods->sam_store_id,
                'store_name' => $this->getStoreName($storeGoods->sam_store_id),
                'current_price' => $storeGoods->price / 100, // 转换为元
                'current_stock' => $storeGoods->stock,
                'last_update' => date('Y-m-d H:i:s', $storeGoods->update_time),
                'price_changed' => $priceChangeInfo['changed'],
                'price_change_info' => $priceChangeInfo
            ];
            
        } catch (\Throwable $e) {
            Log::error("查询商品 {$goodsName} 失败: " . $e->getMessage());
            return [
                'goods_name' => $goodsName,
                'status' => 'error',
                'message' => '查询出错: ' . $e->getMessage(),
                'spuId' => null,
                'current_price' => null,
                'current_stock' => null,
                'price_changed' => false,
                'price_change_info' => null
            ];
        }
    }
    
    /**
     * 获取价格历史
     *
     * @param string $spuId
     * @param int $storeId
     * @param int $startTime 开始时间，0表示查询所有历史
     * @return array
     */
    protected function getPriceHistory($spuId, $storeId, $startTime)
    {
        try {
            $query = StoreGoods::where('sam_goods_id', $spuId)
                              ->where('sam_store_id', $storeId);

            // 如果指定了开始时间且不为0，则添加时间条件
            if ($startTime > 0) {
                $query->where('update_time', '>=', $startTime);
            }

            $history = $query->field('price, stock, update_time')
                            ->order('update_time', 'desc') // 改为倒序，最新的在前面
                            ->limit(20) // 限制最多返回20条记录
                            ->select();

            $priceHistory = [];
            foreach ($history as $record) {
                $priceHistory[] = [
                    'price' => $record->price / 100, // 转换为元
                    'stock' => $record->stock,
                    'date' => date('Y-m-d H:i:s', $record->update_time)
                ];
            }

            // 如果有数据，按时间正序排列用于价格变动分析
            if (!empty($priceHistory)) {
                $priceHistory = array_reverse($priceHistory);
            }

            return $priceHistory;
        } catch (\Throwable $e) {
            Log::error("获取价格历史失败: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 分析价格变动
     *
     * @param array $priceHistory
     * @param int $currentPrice
     * @return array
     */
    protected function analyzePriceChange($priceHistory, $currentPrice)
    {
        $currentPriceYuan = $currentPrice / 100;

        if (empty($priceHistory)) {
            return [
                'changed' => false,
                'change_type' => 'no_data',
                'change_amount' => 0,
                'change_percent' => 0,
                'first_price' => $currentPriceYuan,
                'current_price' => $currentPriceYuan,
                'history_count' => 0,
                'analysis_note' => '暂无历史数据'
            ];
        }

        if (count($priceHistory) < 2) {
            return [
                'changed' => false,
                'change_type' => 'single_record',
                'change_amount' => 0,
                'change_percent' => 0,
                'first_price' => $currentPriceYuan,
                'current_price' => $currentPriceYuan,
                'history_count' => count($priceHistory),
                'analysis_note' => '仅有单条历史记录'
            ];
        }

        $firstPrice = $priceHistory[0]['price'] * 100; // 转换回分
        $changeAmount = $currentPrice - $firstPrice;
        $changePercent = $firstPrice > 0 ? round(($changeAmount / $firstPrice) * 100, 2) : 0;

        $changeType = 'no_change';
        if ($changeAmount > 0) {
            $changeType = 'increase';
        } elseif ($changeAmount < 0) {
            $changeType = 'decrease';
        }

        // 计算分析时间范围
        $firstDate = $priceHistory[0]['date'];
        $lastDate = end($priceHistory)['date'];
        $analysisNote = "基于 {$firstDate} 至 {$lastDate} 的数据分析";

        return [
            'changed' => abs($changeAmount) >= 1, // 变动超过1分才算有变化
            'change_type' => $changeType,
            'change_amount' => $changeAmount / 100, // 转换为元
            'change_percent' => $changePercent,
            'first_price' => $firstPrice / 100,
            'current_price' => $currentPriceYuan,
            'history_count' => count($priceHistory),
            'analysis_note' => $analysisNote
        ];
    }
    
    /**
     * 获取门店名称
     * 
     * @param int $storeId
     * @return string
     */
    protected function getStoreName($storeId)
    {
        try {
            $store = Store::where('storeId', $storeId)->find();
            return $store ? $store->name : "门店ID: {$storeId}";
        } catch (\Throwable $e) {
            return "门店ID: {$storeId}";
        }
    }

    /**
     * 获取门店列表
     *
     * @param Request $request
     * @return \think\Response
     */
    public function getStoreList(Request $request)
    {
        try {
            $stores = Store::where('status', 1)
                          ->field('storeId, name, city, type')
                          ->select();

            $storeList = [];
            $huizhouStores = [];
            $otherStores = [];

            foreach ($stores as $store) {
                $storeData = [
                    'store_id' => $store->storeId,
                    'name' => $store->name,
                    'city' => $store->city,
                    'type' => $store->type,
                    'type_name' => $this->getStoreTypeName($store->type)
                ];

                // 优先显示惠州地区的门店
                if ($store->city && strpos($store->city, '惠州') !== false) {
                    $huizhouStores[] = $storeData;
                } else {
                    $otherStores[] = $storeData;
                }
            }

            // 对惠州门店按名称排序
            usort($huizhouStores, function($a, $b) {
                return strcmp($a['name'], $b['name']);
            });

            // 对其他门店按城市和名称排序
            usort($otherStores, function($a, $b) {
                $cityCompare = strcmp($a['city'], $b['city']);
                if ($cityCompare === 0) {
                    return strcmp($a['name'], $b['name']);
                }
                return $cityCompare;
            });

            // 合并结果，惠州门店在前
            $storeList = array_merge($huizhouStores, $otherStores);

            return $this->success('获取成功', $storeList);
        } catch (\Throwable $e) {
            Log::error('获取门店列表失败: ' . $e->getMessage());
            return $this->error('获取门店列表失败');
        }
    }

    /**
     * 导出查询结果
     *
     * @param Request $request
     * @return \think\Response
     */
    public function exportQueryResults(Request $request)
    {
        try {
            // 获取查询参数
            $goodsNames = $request->param('goods_names', '', 'trim');
            $storeId = $request->param('store_id', 0, 'intval');
            $days = $request->param('days', 7, 'intval');

            if (empty($goodsNames)) {
                return $this->error('请输入商品名称');
            }

            // 处理商品名称
            $namesArray = $this->parseGoodsNames($goodsNames);

            // 查询数据
            $results = [];
            foreach ($namesArray as $goodsName) {
                $results[] = $this->queryGoodsInfo($goodsName, $storeId, $days);
            }

            // 生成CSV数据
            $csvData = $this->generateCsvData($results);

            // 设置响应头
            $filename = '商品价格库存查询_' . date('YmdHis') . '.csv';

            return response($csvData, 200, [
                'Content-Type' => 'text/csv; charset=UTF-8',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                'Cache-Control' => 'max-age=0'
            ]);

        } catch (\Throwable $e) {
            Log::error('导出查询结果失败: ' . $e->getMessage());
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成CSV数据
     *
     * @param array $results
     * @return string
     */
    protected function generateCsvData($results)
    {
        // CSV头部
        $csv = "\xEF\xBB\xBF"; // UTF-8 BOM
        $csv .= "查询商品名称,状态,匹配商品名称,商品ID,门店名称,当前价格(元),当前库存,最后更新时间\n";

        foreach ($results as $result) {
            $row = [
                $result['goods_name'],
                $this->getStatusText($result['status']),
                $result['matched_name'] ?? '',
                $result['spuId'] ?? '',
                $result['store_name'] ?? '',
                $result['current_price'] ?? '',
                $result['current_stock'] ?? '',
                $result['last_update'] ?? ''
            ];

            // 处理CSV特殊字符
            $row = array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row);

            $csv .= implode(',', $row) . "\n";
        }

        return $csv;
    }

    /**
     * 获取状态文本
     *
     * @param string $status
     * @return string
     */
    protected function getStatusText($status)
    {
        $statusMap = [
            'found' => '正常在售',
            'delisted' => '已下架',
            'error' => '查询出错'
        ];

        return $statusMap[$status] ?? $status;
    }

    /**
     * 获取价格变动文本
     *
     * @param array|null $priceChangeInfo
     * @return string
     */
    protected function getPriceChangeText($priceChangeInfo)
    {
        if (!$priceChangeInfo) {
            return '无数据';
        }

        $changeTypeMap = [
            'increase' => '涨价',
            'decrease' => '降价',
            'no_change' => '无变动',
            'no_data' => '无历史数据',
            'single_record' => '仅单条记录'
        ];

        return $changeTypeMap[$priceChangeInfo['change_type']] ?? '未知';
    }

    /**
     * 获取门店类型名称
     *
     * @param int $type
     * @return string
     */
    protected function getStoreTypeName($type)
    {
        $typeMap = [
            2 => '门店',
            4 => '极速达',
            8 => '全球购',
            256 => '全城配'
        ];

        return $typeMap[$type] ?? "类型{$type}";
    }
}
