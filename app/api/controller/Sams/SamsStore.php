<?php

namespace app\api\controller\Sams;

use app\common\model\SamClub;
use app\common\model\sam\Store;
use think\facade\Log;

/**
 * 山姆会员店门店数据采集
 */
class SamsStore extends SamsBase
{
    /**
     * 获取山姆门店列表并写入数据库
     */
    public function getStores()
    {
        return $this->handleDataFetch(
            '山姆门店数据自动更新',                 // 任务名称
            'SamsStore',                          // 控制器名称
            'syncStores',                         // 方法名称
            '0 9 * * *',                          // cron表达式 - 每天上午9点执行
            '自动同步山姆会员店门店数据',          // 任务备注
            function($samClub, $instance) {      // 获取数据的回调
                // 获取有效的token
                $tokenInfo = $instance->getValidToken();
                if (!$tokenInfo) {
                    Log::error('没有找到有效的token');
                    return false;
                }
                
                $token = $tokenInfo['token'];
                
                // 调用API获取数据
                $result = $samClub->get_store_list($token, '', $instance->getProxyIp());
                return $instance->handleApiResponse($result, '获取门店数据失败', $token);
            },
            function($data, $instance) {         // 处理数据的回调
                if (!isset($data['addressList'])) {
                    return 0;
                }
                
                return $this->processAndSaveStores($data['addressList']);
            },
            '门店数据获取并保存成功',              // 成功消息
            '获取门店数据失败',                   // 错误消息
            Store::class                          // 模型类名
        );
    }
    
    /**
     * 批量保存门店数据
     * 
     * @param array $stores 门店数据数组
     * @param string $remark 备注
     * @return int 成功保存的数量
     */
    protected static function batchSaveStores($stores, $remark = '')
    {
        if (empty($stores)) {
            return 0;
        }
        
        return self::batchSaveData(
            $stores,
            'storeId',
            function($store, $now) use ($remark) {
                // 处理经纬度
                $location = '';
                if (isset($store['longitude']) && isset($store['latitude']) && 
                    !empty($store['longitude']) && !empty($store['latitude'])) {
                    $location = $store['longitude'] . ',' . $store['latitude'];
                }
                
                // 获取门店类型
                $type = isset($store['storeType']) ? $store['storeType'] : '2';
                
                return [
                    'name' => $store['storeName'],
                    'storeId' => $store['storeId'],
                    'type' => $type,
                    'city' => $store['cityName'] ?? '',
                    'address' => $store['storeAddress'] ?? '',
                    'location' => $location,
                    'postcode' => isset($store['districtCode']) ? $store['districtCode'] : (isset($store['cityCode']) ? $store['cityCode'] : ''),
                    'status' => intval($store['status'] ?? 1),
                    'update_time' => $now,
                    'remark' => $remark ?: ($store['provinceName'] ?? '') . ($store['cityName'] ?? '') . ($store['districtName'] ?? '')
                ];
            },
            Store::class
        );
    }
    
    /**
     * 保存门店数据
     * 
     * @param array $store 门店数据
     * @param string $remark 备注
     * @return bool 是否成功
     */
    protected static function saveStore($store, $remark = '')
    {
        // 调用批量处理方法，传入单个门店作为数组
        return self::batchSaveStores([$store], $remark) > 0;
    }
    
    /**
     * 处理并保存门店数据
     * 
     * @param array $addressList 地址列表数据
     * @return int 处理的门店数量
     */
    protected function processAndSaveStores($addressList)
    {
        $storeCount = 0;
        
        // 收集所有门店数据
        $allStores = [];
        
        // 遍历地址列表
        foreach ($addressList as $address) {
            // 检查是否有门店列表
            if (!isset($address['storeList']) || empty($address['storeList'])) {
                continue;
            }
            
            // 省市区信息
            $remark = ($address['provinceName'] ?? '') . ($address['cityName'] ?? '') . ($address['districtName'] ?? '');
            
            // 遍历门店并补充信息
            foreach ($address['storeList'] as $store) {
                // 补充省市区信息
                $store['provinceName'] = $address['provinceName'] ?? '';
                $store['cityName'] = $address['cityName'] ?? '';
                $store['districtName'] = $address['districtName'] ?? '';
                
                $allStores[] = $store;
            }
        }
        
        // 批量保存所有门店
        if (!empty($allStores)) {
            $storeCount = self::batchSaveStores($allStores);
        }
        
        return $storeCount;
    }
    
    /**
     * 供计划任务调用的同步门店数据方法
     * 
     * @param array $params 包含token等参数的数组
     * @return bool 返回false将终止任务执行
     */
    public static function syncStores($params = [])
    {
        return parent::handleSync(
            function($samClub, $instance, $device_id, $ip) {
                // 获取有效的token
                $tokenInfo = $instance->getValidToken();
                if (!$tokenInfo) {
                    Log::error('同步门店数据失败: 没有找到有效的token');
                    return false;
                }
                
                $token = $tokenInfo['token'];
                
                // 调用API获取数据
                $result = $samClub->get_store_list($token, $device_id, $ip);
                return $instance->handleApiResponse($result, '获取门店数据失败', $token);
            },
            function($data, $instance, $device_id, $ip, $samClub) {
                if (!isset($data['addressList'])) {
                    return;
                }
                
                $addressList = $data['addressList'];
                
                // 处理门店数据
                foreach ($addressList as $address) {
                    // 检查是否有门店列表
                    if (!isset($address['storeList']) || empty($address['storeList'])) {
                        continue;
                    }
                    
                    // 遍历门店并保存
                    foreach ($address['storeList'] as $store) {
                        // 省市区信息
                        $remark = ($address['provinceName'] ?? '') . ($address['cityName'] ?? '') . ($address['districtName'] ?? '');
                        
                        // 补充省市区信息
                        $store['provinceName'] = $address['provinceName'] ?? '';
                        $store['cityName'] = $address['cityName'] ?? '';
                        $store['districtName'] = $address['districtName'] ?? '';
                        
                        self::saveStore($store, $remark);
                    }
                }
            },
            '获取门店数据失败',
            Store::class
        );
    }
} 