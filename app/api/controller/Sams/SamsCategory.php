<?php

namespace app\api\controller\Sams;

use app\common\model\SamClub;
use app\common\model\sam\Categories;
use think\facade\Log;

/**
 * 山姆会员店分类数据采集
 */
class SamsCategory extends SamsBase
{


    /**
     * 获取山姆分类列表并写入数据库
     */
    public function getCategories()
    {
        return $this->handleDataFetch(
            '山姆分类自动更新',                 // 任务名称
            'SamsCategory',                    // 控制器名称
            'syncCategories',                  // 方法名称
            '0 8 * * *',                      // cron表达式 - 每天上午8点执行
            '自动同步山姆商城分类数据',         // 任务备注
            function($samClub, $instance) {    // 获取数据的回调
                // 获取有效的token
                $tokenInfo = $instance->getValidToken();
                if (!$tokenInfo) {
                    Log::error('没有找到有效的token');
                    return false;
                }

                $token = $tokenInfo['token'];

                // 调用API获取数据
                $result = $samClub->query_navigation($token, '', $instance->getProxyIp());
                return $instance->handleApiResponse($result, '获取分类数据失败', $token);
            },
            function($data, $instance) {      // 处理数据的回调
                if (!isset($data['dataList'])) {
                    return 0;
                }
                
                $categories = $data['dataList'];
                $samClub = new SamClub();
                
                // 遍历一级分类并保存
                foreach ($categories as $category) {
                    // 保存分类
                    self::saveCategory($category, 1, 0, '');

                    // 获取二级分类，传递IP参数
                    self::getSubCategoriesAndSave($category['groupingId'], $instance, '', $instance->getProxyIp());
                }
                
                return Categories::count();
            },
            '分类获取并保存成功',              // 成功消息
            '获取分类数据失败',               // 错误消息
            Categories::class                 // 模型类名
        );
    }
    
    /**
     * 批量保存分类数据的预处理方法
     * 
     * @param array $categories 分类数据数组
     * @param int $level 级别
     * @param int|string $pid 父ID
     * @param string $remark 备注
     */
    protected static function batchSaveCategories($categories, $level, $pid, $remark)
    {
        if (empty($categories)) {
            return;
        }
        
        self::batchSaveData(
            $categories,
            'groupingId', 
            function($category, $now) use ($level, $pid, $remark) {
                $name = $category['title'] ?? '';
                $groupingId = $category['groupingId'] ?? '';
                $currentLevel = $category['level'] ?? $level;
                
                return [
                    'name' => $name,
                    'groupingId' => $groupingId,
                    'level' => $currentLevel,
                    'pid' => $pid,
                    'remark' => $remark,
                    'status' => 1,
                    'update_time' => $now
                ];
            },
            Categories::class
        );
    }
    
    /**
     * 保存分类数据
     * 
     * @param array $category 分类数据
     * @param int $level 级别
     * @param int|string $pid 父ID
     * @param string $remark 备注
     */
    protected static function saveCategory($category, $level, $pid, $remark)
    {
        // 调用批量处理方法，传入单个分类作为数组
        self::batchSaveCategories([$category], $level, $pid, $remark);
    }
    
    /**
     * 获取子分类并保存
     * 
     * @param string $groupingId 分类ID
     * @param SamsBase $instance SamsBase实例，用于获取token
     * @param string $device_id 设备ID
     * @param string $ip IP地址
     * @param SamClub $samClub SamClub实例
     */
    protected static function getSubCategoriesAndSave($groupingId, $instance, $device_id = '', $ip = '', $samClub = null)
    {
        if ($samClub === null) {
            $samClub = new SamClub();
        }

        // 获取有效的token
        $tokenInfo = $instance->getValidToken();
        if (!$tokenInfo) {
            Log::error('获取子分类失败: 没有找到有效的token');
            return;
        }

        $token = $tokenInfo['token'];

        // 如果没有传入IP，则从系统配置获取
        if (empty($ip)) {
            $ip = $instance->getProxyIp();
        }

        $result = $samClub->query_children($groupingId, $token, $device_id, $ip);
        $resultData = $instance->handleApiResponse($result, "获取子分类失败 (groupingId: {$groupingId})", $token);
        
        if (!$resultData || !isset($resultData['data'])) {
            return;
        }
        
        $subCategories = $resultData['data'];
        
        // 批量保存二级分类
        self::batchSaveCategories($subCategories, 2, $groupingId, '');
        
        // 收集所有三级分类
        $allChildren = [];
        foreach ($subCategories as $subCategory) {
            if (isset($subCategory['children']) && !empty($subCategory['children'])) {
                foreach ($subCategory['children'] as $childCategory) {
                    // 设置父ID
                    $childCategory['parentId'] = $subCategory['groupingId'];
                    $allChildren[] = $childCategory;
                }
            }
        }
        
        // 批量处理三级分类
        if (!empty($allChildren)) {
            // 收集所有三级分类并设置父ID
            foreach ($allChildren as &$childCategory) {
                $childCategory['pid'] = $childCategory['parentId'] ?? '';
                $childCategory['level'] = $childCategory['level'] ?? 3;
            }
            
            // 使用通用的批量保存方法
            self::batchSaveData(
                $allChildren,
                'groupingId',
                function($category, $now) {
                    return [
                        'name' => $category['title'] ?? '',
                        'groupingId' => $category['groupingId'] ?? '',
                        'level' => $category['level'] ?? 3,
                        'pid' => $category['pid'] ?? $category['parentId'] ?? '',
                        'remark' => '',
                        'status' => 1,
                        'update_time' => $now
                    ];
                },
                Categories::class
            );
        }
    }
    
    /**
     * 供计划任务调用的同步分类方法
     * 
     * @param array $params 包含token等参数的数组
     * @return bool 返回false将终止任务执行
     */
    public static function syncCategories($params = [])
    {
        try {
            return parent::handleSync(
                function($samClub, $instance, $device_id, $ip) {
                    // 获取有效的token
                    $tokenInfo = $instance->getValidToken();
                    if (!$tokenInfo) {
                        Log::error('同步分类数据失败: 没有找到有效的token');
                        return false;
                    }

                    $token = $tokenInfo['token'];

                    // 如果没有传入IP，则从系统配置获取
                    if (empty($ip)) {
                        $ip = $instance->getProxyIp();
                    }

                    // 调用API获取数据
                    $result = $samClub->query_navigation($token, $device_id, $ip);
                    return $instance->handleApiResponse($result, '获取分类数据失败', $token);
                },
                function($data, $instance, $device_id, $ip, $samClub) {
                    if (!isset($data['dataList'])) {
                        return;
                    }
                    
                    $categories = $data['dataList'];
                    
                    // 遍历一级分类并保存
                    foreach ($categories as $category) {
                        // 保存分类
                        self::saveCategory($category, 1, 0, '');
                        
                        // 获取二级分类
                        self::getSubCategoriesAndSave($category['groupingId'], $instance, $device_id, $ip, $samClub);
                    }
                },
                '获取分类数据失败',
                Categories::class
            );
        } catch (\Throwable $e) {
            Log::error('同步分类数据异常: ' . $e->getMessage());
            return false;
        }
    }
} 