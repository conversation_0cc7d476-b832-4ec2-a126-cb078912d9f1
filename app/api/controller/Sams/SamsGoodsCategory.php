<?php

namespace app\api\controller\Sams;

use app\common\model\sam\Goods;
use app\common\model\sam\Categories;
use app\common\model\sam\store\Goods as StoreGoods;
use think\facade\Log;
use think\Request;
use think\Response;

/**
 * 山姆会员店商品分类查询API
 */
class SamsGoodsCategory extends SamsBase
{
    /**
     * 通过商品ID查询商品分类等内容
     * 
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function getGoodsCategoryById(Request $request)
    {
        try {
            // 获取参数
            $goodsId = $request->param('goods_id', '', 'trim'); // 商品ID (spuId)
            $include_store_info = $request->param('include_store_info', 0, 'intval'); // 是否包含门店信息
            $store_id = $request->param('store_id', 0, 'intval'); // 指定门店ID
            
            // 验证参数
            if (empty($goodsId)) {
                return $this->error('商品ID不能为空');
            }
            
            // 查询商品信息
            $goods = Goods::where('spuId', $goodsId)
                         ->where('status', 1)
                         ->find();
            
            if (!$goods) {
                return $this->error('商品不存在或已下架');
            }
            var_dump();
            
            // 构建返回数据
            $result = [
                'goods_info' => [
                    'spuId' => $goods->spuId,
                    'name' => $goods->name,
                    'imgUrl' => $goods->imgUrl,
                    'status' => $goods->status,
                    'create_time' => $goods->create_time,
                    'update_time' => $goods->update_time
                ],
                'category_info' => [
                    'catLv1' => $goods->catLv1 ?? 0,
                    'catLv2' => $goods->catLv2 ?? 0,
                    'catLv3' => $goods->catLv3 ?? 0,
                    'category1' => $goods->category1 ?? 0,
                    'category2' => $goods->category2 ?? 0,
                    'category3' => $goods->category3 ?? 0,
                    'seriesId' => $goods->seriesId ?? 0,
                    'hostItemId' => $goods->hostItemId ?? 0
                ]
            ];
            
            // 获取分类名称
            $categoryNames = $this->getCategoryNames([
                $goods->catLv1 ?? 0,
                $goods->catLv2 ?? 0,
                $goods->catLv3 ?? 0
            ]);
            
            $result['category_info']['catLv1Name'] = $categoryNames[$goods->catLv1 ?? 0] ?? '未知';
            $result['category_info']['catLv2Name'] = $categoryNames[$goods->catLv2 ?? 0] ?? '未知';
            $result['category_info']['catLv3Name'] = $categoryNames[$goods->catLv3 ?? 0] ?? '未知';
            
            // 如果需要包含门店信息
            if ($include_store_info) {
                $storeGoodsQuery = StoreGoods::where('sam_goods_id', $goodsId)
                                           ->where('status', 1);
                
                // 如果指定了门店ID，只查询该门店
                if ($store_id > 0) {
                    $storeGoodsQuery->where('sam_store_id', $store_id);
                }
                
                $storeGoods = $storeGoodsQuery->select();
                
                $result['store_info'] = [];
                foreach ($storeGoods as $storeGood) {
                    $result['store_info'][] = [
                        'store_id' => $storeGood->sam_store_id,
                        'price' => $storeGood->price,
                        'stock' => $storeGood->stock,
                        'status' => $storeGood->status,
                        'update_time' => $storeGood->update_time
                    ];
                }
            }
            
            return $this->success('查询成功', $result);
            
        } catch (\Throwable $e) {
            $this->debugLog('error', '查询商品分类失败: ' . $e->getMessage());
            return $this->error('查询商品分类失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 批量查询商品分类信息
     * 
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function batchGetGoodsCategory(Request $request)
    {
        try {
            // 获取参数
            $goodsIds = $request->param('goods_ids', '', 'trim'); // 商品ID列表，逗号分隔
            $include_store_info = $request->param('include_store_info', 0, 'intval');
            $store_id = $request->param('store_id', 0, 'intval');
            
            // 验证参数
            if (empty($goodsIds)) {
                return $this->error('商品ID列表不能为空');
            }
            
            // 解析商品ID列表
            $goodsIdArray = array_filter(array_map('trim', explode(',', $goodsIds)));
            if (empty($goodsIdArray)) {
                return $this->error('商品ID列表格式错误');
            }
            
            // 限制查询数量，避免性能问题
            if (count($goodsIdArray) > 100) {
                return $this->error('单次查询商品数量不能超过100个');
            }
            
            // 查询商品信息
            $goodsList = Goods::whereIn('spuId', $goodsIdArray)
                            ->where('status', 1)
                            ->select();
            
            if ($goodsList->isEmpty()) {
                return $this->error('未找到任何商品');
            }
            
            // 收集所有分类ID
            $categoryIds = [];
            foreach ($goodsList as $goods) {
                if ($goods->catLv1) $categoryIds[] = $goods->catLv1;
                if ($goods->catLv2) $categoryIds[] = $goods->catLv2;
                if ($goods->catLv3) $categoryIds[] = $goods->catLv3;
            }
            $categoryIds = array_unique($categoryIds);
            
            // 获取分类名称
            $categoryNames = $this->getCategoryNames($categoryIds);
            
            // 构建返回数据
            $result = [];
            foreach ($goodsList as $goods) {
                $goodsData = [
                    'goods_info' => [
                        'spuId' => $goods->spuId,
                        'name' => $goods->name,
                        'imgUrl' => $goods->imgUrl,
                        'status' => $goods->status,
                        'create_time' => $goods->create_time,
                        'update_time' => $goods->update_time
                    ],
                    'category_info' => [
                        'catLv1' => $goods->catLv1 ?? 0,
                        'catLv2' => $goods->catLv2 ?? 0,
                        'catLv3' => $goods->catLv3 ?? 0,
                        'catLv1Name' => $categoryNames[$goods->catLv1 ?? 0] ?? '未知',
                        'catLv2Name' => $categoryNames[$goods->catLv2 ?? 0] ?? '未知',
                        'catLv3Name' => $categoryNames[$goods->catLv3 ?? 0] ?? '未知',
                        'category1' => $goods->category1 ?? 0,
                        'category2' => $goods->category2 ?? 0,
                        'category3' => $goods->category3 ?? 0,
                        'seriesId' => $goods->seriesId ?? 0,
                        'hostItemId' => $goods->hostItemId ?? 0
                    ]
                ];
                
                // 如果需要包含门店信息
                if ($include_store_info) {
                    $storeGoodsQuery = StoreGoods::where('sam_goods_id', $goods->spuId)
                                               ->where('status', 1);
                    
                    if ($store_id > 0) {
                        $storeGoodsQuery->where('sam_store_id', $store_id);
                    }
                    
                    $storeGoods = $storeGoodsQuery->select();
                    
                    $goodsData['store_info'] = [];
                    foreach ($storeGoods as $storeGood) {
                        $goodsData['store_info'][] = [
                            'store_id' => $storeGood->sam_store_id,
                            'price' => $storeGood->price,
                            'stock' => $storeGood->stock,
                            'status' => $storeGood->status,
                            'update_time' => $storeGood->update_time
                        ];
                    }
                }
                
                $result[] = $goodsData;
            }
            
            return $this->success('批量查询成功', [
                'total' => count($result),
                'goods_list' => $result
            ]);
            
        } catch (\Throwable $e) {
            $this->debugLog('error', '批量查询商品分类失败: ' . $e->getMessage());
            return $this->error('批量查询商品分类失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 根据分类查询商品列表
     * 
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function getGoodsByCategory(Request $request)
    {
        try {
            // 获取参数
            $catLv1 = $request->param('catLv1', 0, 'intval');
            $catLv2 = $request->param('catLv2', 0, 'intval');
            $catLv3 = $request->param('catLv3', 0, 'intval');
            $page = $request->param('page', 1, 'intval');
            $limit = $request->param('limit', 20, 'intval');
            $include_store_info = $request->param('include_store_info', 0, 'intval');
            $store_id = $request->param('store_id', 0, 'intval');
            
            // 验证参数
            if ($catLv1 <= 0 && $catLv2 <= 0 && $catLv3 <= 0) {
                return $this->error('至少需要指定一个分类ID');
            }
            
            // 限制分页参数
            $page = max(1, $page);
            $limit = min(100, max(1, $limit)); // 限制每页最多100条
            
            // 构建查询条件
            $query = Goods::where('status', 1);
            
            if ($catLv1 > 0) {
                $query->where('catLv1', $catLv1);
            }
            if ($catLv2 > 0) {
                $query->where('catLv2', $catLv2);
            }
            if ($catLv3 > 0) {
                $query->where('catLv3', $catLv3);
            }
            
            // 分页查询
            $goodsList = $query->page($page, $limit)
                             ->order('update_time', 'desc')
                             ->select();
            
            // 获取总数
            $total = $query->count();
            
            if ($goodsList->isEmpty()) {
                return $this->success('查询成功', [
                    'total' => 0,
                    'page' => $page,
                    'limit' => $limit,
                    'goods_list' => []
                ]);
            }
            
            // 收集分类ID并获取分类名称
            $categoryIds = [$catLv1, $catLv2, $catLv3];
            $categoryNames = $this->getCategoryNames($categoryIds);
            
            // 构建返回数据
            $result = [];
            foreach ($goodsList as $goods) {
                $goodsData = [
                    'goods_info' => [
                        'spuId' => $goods->spuId,
                        'name' => $goods->name,
                        'imgUrl' => $goods->imgUrl,
                        'status' => $goods->status,
                        'create_time' => $goods->create_time,
                        'update_time' => $goods->update_time
                    ],
                    'category_info' => [
                        'catLv1' => $goods->catLv1 ?? 0,
                        'catLv2' => $goods->catLv2 ?? 0,
                        'catLv3' => $goods->catLv3 ?? 0,
                        'catLv1Name' => $categoryNames[$goods->catLv1 ?? 0] ?? '未知',
                        'catLv2Name' => $categoryNames[$goods->catLv2 ?? 0] ?? '未知',
                        'catLv3Name' => $categoryNames[$goods->catLv3 ?? 0] ?? '未知'
                    ]
                ];
                
                // 如果需要包含门店信息
                if ($include_store_info) {
                    $storeGoodsQuery = StoreGoods::where('sam_goods_id', $goods->spuId)
                                               ->where('status', 1);
                    
                    if ($store_id > 0) {
                        $storeGoodsQuery->where('sam_store_id', $store_id);
                    }
                    
                    $storeGoods = $storeGoodsQuery->select();
                    
                    $goodsData['store_info'] = [];
                    foreach ($storeGoods as $storeGood) {
                        $goodsData['store_info'][] = [
                            'store_id' => $storeGood->sam_store_id,
                            'price' => $storeGood->price,
                            'stock' => $storeGood->stock,
                            'status' => $storeGood->status,
                            'update_time' => $storeGood->update_time
                        ];
                    }
                }
                
                $result[] = $goodsData;
            }
            
            return $this->success('查询成功', [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'category_info' => [
                    'catLv1' => $catLv1,
                    'catLv2' => $catLv2,
                    'catLv3' => $catLv3,
                    'catLv1Name' => $categoryNames[$catLv1] ?? '未知',
                    'catLv2Name' => $categoryNames[$catLv2] ?? '未知',
                    'catLv3Name' => $categoryNames[$catLv3] ?? '未知'
                ],
                'goods_list' => $result
            ]);
            
        } catch (\Throwable $e) {
            $this->debugLog('error', '根据分类查询商品失败: ' . $e->getMessage());
            return $this->error('根据分类查询商品失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取分类名称映射
     * 
     * @param array $categoryIds 分类ID数组
     * @return array 分类ID => 分类名称的映射
     */
    protected function getCategoryNames($categoryIds)
    {
        if (empty($categoryIds)) {
            return [];
        }
        
        // 过滤掉0和空值
        $categoryIds = array_filter($categoryIds, function($id) {
            return !empty($id) && $id > 0;
        });
        
        if (empty($categoryIds)) {
            return [];
        }
        
        try {
            $categories = Categories::whereIn('groupingId', $categoryIds)
                                  ->where('status', 1)
                                  ->select();
            
            $categoryNames = [];
            foreach ($categories as $category) {
                $categoryNames[$category->groupingId] = $category->name;
            }
            
            return $categoryNames;
        } catch (\Throwable $e) {
            $this->debugLog('error', '获取分类名称失败: ' . $e->getMessage());
            return [];
        }
    }
}
