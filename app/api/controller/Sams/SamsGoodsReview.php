<?php

namespace app\api\controller\Sams;

use app\common\model\SamClub;
use app\common\model\sam\Goods;
use app\common\model\sam\store\Goods as StoreGoods;
use think\facade\Log;
use think\Request;
use think\Response;


/**
 * 山姆会员店商品评价数据采集
 */
class SamsGoodsReview extends SamsBase
{
    /**
     * 最大重试次数
     */
    protected static $maxRetries = 3;
    
    /**
     * 重试间隔（毫秒）
     */
    protected static $retryDelay = 500;

    /**
     * 获取商品评价并保存到数据库
     * 
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function getGoodsReview(Request $request)
    {
        // 获取参数
        $spuId = $request->param('spu_id', '', 'trim');
        $create_task = $request->param('create_task', 0, 'intval');
        $review_count = $request->param('review_count', 200, 'intval'); // 需要获取的评价数量，默认60个

        // 如果需要创建多个任务
        if ($create_task) {
            $tokenInfo = $this->getValidToken();
            if (!$tokenInfo) {
                return $this->error('没有找到有效的token');
            }

            // 获取任务参数
            $batch_size = $request->param('batch_size', 10, 'intval'); // 每个任务处理的商品数量
            $review_count_per_goods = $request->param('review_count', 60, 'intval'); // 每个商品获取的评价数量

            return $this->createMultipleTasks($batch_size, $review_count_per_goods, $tokenInfo);
        }
        
        // 直接获取商品评价
        if (empty($spuId)) {
            return $this->error('商品ID不能为空');
        }
        
        return $this->handleDataFetch(
            '山姆商品评价获取',                // 任务名称
            'SamsGoodsReview',               // 控制器名称
            'syncGoodsReview',               // 方法名称
            '0 4 * * *',                    // cron表达式
            '单次同步山姆商品评价数据',        // 任务备注
            function($samClub, $instance) use ($spuId, $review_count) {    // 获取数据的回调
                return $instance->getMultiPageReviews($samClub, $spuId, $review_count);
            },
            function($data, $instance) use ($spuId) {      // 处理数据的回调
                // handleDataFetch已经提取了data部分，直接处理
                return $this->saveGoodsReview(['data' => $data], $spuId);
            },
            '商品评价获取并保存成功',          // 成功消息
            '获取商品评价失败',               // 错误消息
            Goods::class                     // 模型类名
        );
    }

    /**
     * 创建多个评价采集任务
     *
     * @param int $batchSize 每个任务处理的商品数量
     * @param int $reviewCount 每个商品获取的评价数量
     * @param array $tokenInfo token信息
     * @return Response 响应对象
     */
    protected function createMultipleTasks($batchSize, $reviewCount, $tokenInfo)
    {
        try {
            // 先获取所有符合条件的商品ID
            $allSpuIds = Goods::alias('g')
                ->join('sam_store_goods sg', 'g.spuId = sg.sam_goods_id')
                ->where('g.status', 1)
                ->where('sg.sam_store_id', 6114)
                ->whereRaw('g.evaluate IS NULL OR g.evaluate = ""')
                ->column('g.spuId');

            if (empty($allSpuIds)) {
                return $this->error('没有需要采集评价的商品');
            }

            $totalGoods = count($allSpuIds);

            // 将商品ID分成多个批次
            $spuIdBatches = array_chunk($allSpuIds, $batchSize);

            // 任务数量由分批结果自动决定
            $actualTasks = count($spuIdBatches);

            $tasksCreated = [];
            $successCount = 0;
            $failCount = 0;

            // 创建多个任务，每个任务处理一个批次的商品ID
            for ($i = 0; $i < $actualTasks; $i++) {
                $batchSpuIds = $spuIdBatches[$i];

                // 创建任务名称和描述
                $taskName = "山姆商品评价采集任务-批次" . ($i + 1);
                $taskDescription = "批次" . ($i + 1) . "商品评价数据采集，商品数量: " . count($batchSpuIds);

                // 设置任务参数
                $taskParams = [
                    'spu_ids' => $batchSpuIds,  // 直接传递商品ID数组
                    'review_count' => $reviewCount,
                    'task_index' => $i + 1
                ];

                // 创建单次任务
                $taskCreated = self::createTask(
                    $taskName,
                    'SamsGoodsReview',
                    'syncGoodsReviewBatch',
                    '0 4 * * *',  // 提供一个有效的cron表达式
                    $taskDescription,
                    $taskParams,
                    1  // 设置为执行一次
                );

                if ($taskCreated) {
                    $successCount++;
                    $tasksCreated[] = [
                        'task_index' => $i + 1,
                        'task_name' => $taskName,
                        'spu_ids_count' => count($batchSpuIds),
                        'spu_ids' => $batchSpuIds,
                        'review_count' => $reviewCount,
                        'status' => 'success'
                    ];
                } else {
                    $failCount++;
                    $tasksCreated[] = [
                        'task_index' => $i + 1,
                        'task_name' => $taskName,
                        'status' => 'failed'
                    ];
                }

                // 避免创建任务过快
                usleep(100000); // 休眠100毫秒
            }

            return $this->success('创建多个商品评价采集任务完成', [
                'token_phone' => $tokenInfo['phone'],
                'total_goods' => $totalGoods,
                'batch_size' => $batchSize,
                'tasks_created' => $actualTasks,
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'tasks_detail' => $tasksCreated
            ]);

        } catch (\Throwable $e) {
            Log::error('创建多个评价采集任务失败: ' . $e->getMessage());
            return $this->error('创建多个任务失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取多页评价数据
     *
     * @param SamClub $samClub SamClub实例
     * @param string $spuId 商品ID
     * @param int $reviewCount 需要获取的评价数量
     * @return array|false 成功时返回合并后的评价数据，失败时返回false
     */
    protected function getMultiPageReviews($samClub, $spuId, $reviewCount)
    {
        $allReviews = [];
        $pageNum = 1;
        $pageSize = 20; // 固定每页20条
        $totalCollected = 0;

        // 计算需要获取多少页
        $maxPages = ceil($reviewCount / $pageSize);

        Log::info("开始获取商品评价 (spuId: {$spuId}), 目标数量: {$reviewCount}, 预计页数: {$maxPages}");

        while ($totalCollected < $reviewCount && $pageNum <= $maxPages) {
            $retry = 0;
            $success = false;

            while ($retry < self::$maxRetries && !$success) {
                if ($retry > 0) {
                    Log::info("商品评价获取重试 #{$retry} (spuId: {$spuId}, 页码: {$pageNum})");
                    usleep(self::$retryDelay * 1000); // 重试前延迟
                }

                // 获取有效的token
                $tokenInfo = $this->getValidToken();
                if (!$tokenInfo) {
                    Log::error('没有找到有效的token，重试中...');
                    $retry++;
                    continue;
                }

                $token = $tokenInfo['token'];

                // 调用API获取数据
                $options = [
                    'pageNum' => $pageNum,
                    'pageSize' => $pageSize,
                    'tagIdList' => '',
                    'desc' => 1,
                    'commentLevel' => 3,
                    'queryContentType' => 2
                ];

                try {
                    $result = $samClub->get_goods_comment_list($spuId, $token, '', $this->getProxyIp(), $options);
                    $resultData = $this->handleApiResponse($result, "获取商品评价失败 (spuId: {$spuId}, 页码: {$pageNum})", $token);

                    if ($resultData && isset($resultData['data']) && isset($resultData['data']['pageList'])) {
                        $pageReviews = $resultData['data']['pageList'];

                        // 如果当前页没有评价了，说明已经到最后一页
                        if (empty($pageReviews)) {
                            Log::info("第{$pageNum}页无评价数据，停止获取 (spuId: {$spuId})");
                            break;
                        }

                        // 合并评价数据
                        $allReviews = array_merge($allReviews, $pageReviews);
                        $totalCollected = count($allReviews);

                        Log::info("第{$pageNum}页获取成功 (spuId: {$spuId}), 本页: " . count($pageReviews) . ", 总计: {$totalCollected}");

                        $success = true;

                        // 如果已经获取到足够的评价，截取到目标数量
                        if ($totalCollected >= $reviewCount) {
                            $allReviews = array_slice($allReviews, 0, $reviewCount);
                            break;
                        }

                        // 避免请求过快
                        usleep(300000); // 休眠300毫秒
                    } else {
                        $retry++;
                    }
                } catch (\Throwable $e) {
                    Log::error("获取第{$pageNum}页评价异常 (spuId: {$spuId}): " . $e->getMessage());
                    $retry++;
                }
            }

            if (!$success) {
                Log::error("第{$pageNum}页获取失败，已重试{$retry}次 (spuId: {$spuId})");
                break; // 如果某页失败，停止继续获取
            }

            $pageNum++;
        }

        if (empty($allReviews)) {
            Log::error("未获取到任何评价数据 (spuId: {$spuId})");
            return false;
        }

        // 构建返回数据格式，与原API格式保持一致
        $finalData = [
            'data' => [
                'pageNum' => 1,
                'pageSize' => count($allReviews),
                'totalCount' => count($allReviews),
                'pageList' => $allReviews
            ]
        ];

        Log::info("商品评价获取完成 (spuId: {$spuId}), 最终获取: " . count($allReviews) . " 条评价");

        return $finalData;
    }

    /**
     * 保存商品评价数据
     *
     * @param array $data 评价数据
     * @param string $spuId 商品ID
     * @return bool 是否保存成功
     */
    protected function saveGoodsReview($data, $spuId)
    {
        try {
            // 检查数据是否为空
            if (empty($data['data'])) {
                Log::error("商品评价数据为空 (spuId: {$spuId})");
                return false;
            }

            // 查找商品
            $goods = Goods::where('spuId', $spuId)->find();

            if (!$goods) {
                Log::error("未找到商品 (spuId: {$spuId})");
                return false;
            }

            $reviewData = $data['data'];
            $processedReviews = [];

            // 处理pageList中的评价数据，只保留指定字段
            if (isset($reviewData['pageList']) && !empty($reviewData['pageList'])) {
                foreach ($reviewData['pageList'] as $review) {
                    $commentScore = $review['commentScore'];
                    // 只取五星商品
                    if ($commentScore < 4) {
                        continue;
                    }
                    $processedReview = [
                        'commentContent' => $review['commentContent'] ?? '',
                        'commentImageUrls' => $review['commentImageUrls'] ?? [],
                        'userName' => $review['userName'] ?? '',
                        'userHeadUrl' => $review['userHeadUrl'] ?? '',
                        'commentTime' => $review['commentTime'] ?? ''
                    ];
                    $processedReviews[] = $processedReview;
                }
            }

            // 将处理后的评价数据保存到evaluate字段
            $goods->evaluate = json_encode($processedReviews, JSON_UNESCAPED_UNICODE);
            $goods->save();

            Log::info("商品评价保存成功 (spuId: {$spuId}), 评价数量: " . count($processedReviews));
            return true;

        } catch (\Throwable $e) {
            Log::error("保存商品评价失败 (spuId: {$spuId}): " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 批量获取商品评价
     * 
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function batchGetGoodsReview(Request $request)
    {
        set_time_limit(0); // 不限制执行时间
        ini_set('memory_limit', '512M'); // 设置内存限制
        
        // 获取参数
        $limit = $request->param('limit', 50, 'intval');
        $review_count = $request->param('review_count', 60, 'intval'); // 每个商品需要获取的评价数量
        
        // 查询需要获取评价的商品（通过门店商品关联查询）
        $goods = Goods::alias('g')
            ->join('sam_store_goods sg', 'g.spuId = sg.sam_goods_id')
            ->where('g.status', 1)
            ->where('sg.sam_store_id', 6114)
            ->whereRaw('g.evaluate IS NULL OR g.evaluate = ""')
            ->field('g.*')
            ->limit($limit)
            ->select();
        
        if ($goods->isEmpty()) {
            return $this->error('未找到需要采集评价的商品');
        }
        
        $samClub = new SamClub();
        $successCount = 0;
        $failCount = 0;
        $results = [];
        
        foreach ($goods as $item) {
            try {
                // 使用多页获取方法
                $resultData = $this->getMultiPageReviews($samClub, $item->spuId, $review_count);

                if ($resultData && isset($resultData['data'])) {
                    // 保存商品评价
                    $this->saveGoodsReview(['data' => $resultData['data']], $item->spuId);
                    $successCount++;

                    $results[] = [
                        'spuId' => $item->spuId,
                        'name' => $item->name,
                        'review_count' => count($resultData['data']['pageList'] ?? []),
                        'status' => 'success'
                    ];
                } else {
                    $failCount++;

                    $results[] = [
                        'spuId' => $item->spuId,
                        'name' => $item->name,
                        'status' => 'failed'
                    ];
                }

                // 避免请求过快
                usleep(500000); // 休眠500毫秒，因为可能有多次API调用
            } catch (\Throwable $e) {
                Log::error("处理商品评价失败 (spuId: {$item->spuId}): " . $e->getMessage());
                $failCount++;

                $results[] = [
                    'spuId' => $item->spuId,
                    'name' => $item->name,
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $this->success('批量获取商品评价完成', [
            'total' => count($goods),
            'success' => $successCount,
            'failed' => $failCount,
            'results' => $results
        ]);
    }
    
    /**
     * 供计划任务调用的同步商品评价方法
     * 
     * @param array $params 包含参数的数组
     * @return bool 返回false将终止任务执行
     */
    public static function syncGoodsReview($params = [])
    {
        try {
            $instance = new self();
            $samClub = new SamClub();
            
            // 解析参数
            if (is_string($params)) {
                $params = json_decode($params, true) ?: [];
            } elseif (!is_array($params)) {
                $params = [];
            }
            
            // 获取批次大小参数，默认为30
            $batchSize = isset($params['batch_size']) ? intval($params['batch_size']) : 500;
            $reviewCount = isset($params['review_count']) ? intval($params['review_count']) : 200;

            // 查询需要获取评价的商品（通过门店商品关联查询）
            $goods = Goods::alias('g')
                ->join('sam_store_goods sg', 'g.spuId = sg.sam_goods_id')
                ->where('g.status', 1)
                ->where('sg.sam_store_id', 6114)
                ->whereRaw('g.evaluate IS NULL OR g.evaluate = ""')
                ->field('g.*')
                ->limit($batchSize)
                ->select();
            
            if ($goods->isEmpty()) {
                Log::info('没有需要同步评价的商品');
                return true;
            }
            
            $successCount = 0;
            $failCount = 0;
            
            foreach ($goods as $item) {
                try {
                    // 使用多页获取方法
                    $resultData = $instance->getMultiPageReviews($samClub, $item->spuId, $reviewCount);

                    if ($resultData && isset($resultData['data'])) {
                        // 保存商品评价
                        $instance->saveGoodsReview(['data' => $resultData['data']], $item->spuId);
                        $successCount++;
                    } else {
                        $failCount++;
                    }

                    // 避免请求过快
//                    usleep(500000); // 休眠500毫秒，因为可能有多次API调用
                } catch (\Throwable $e) {
                    Log::error("处理商品评价失败 (spuId: {$item->spuId}): " . $e->getMessage());
                    $failCount++;
                }
            }
            
            Log::info("商品评价同步完成: 成功 {$successCount}, 失败 {$failCount}");
            return true;
        } catch (\Throwable $e) {
            Log::error('同步商品评价异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 供计划任务调用的批次同步商品评价方法
     *
     * @param array $params 包含参数的数组
     * @return bool 返回false将终止任务执行
     */
    public static function syncGoodsReviewBatch($params = [])
    {
        try {
            $instance = new self();
            $samClub = new SamClub();

            // 解析参数
            if (is_string($params)) {
                $params = json_decode($params, true) ?: [];
            } elseif (!is_array($params)) {
                $params = [];
            }

            // 获取任务参数
            $spuIds = isset($params['spu_ids']) ? $params['spu_ids'] : [];
            $reviewCount = isset($params['review_count']) ? intval($params['review_count']) : 60;
            $taskIndex = isset($params['task_index']) ? intval($params['task_index']) : 1;

            if (empty($spuIds)) {
                Log::info("批次{$taskIndex}没有分配到商品ID");
                return true;
            }

            Log::info("开始执行批次{$taskIndex}评价采集任务，商品数量: " . count($spuIds) . "，评价数量: {$reviewCount}");

            // 根据商品ID查询商品信息
            $goods = Goods::whereIn('spuId', $spuIds)
                ->where('status', 1)
                ->select();

            if ($goods->isEmpty()) {
                Log::info("批次{$taskIndex}没有找到有效的商品");
                return true;
            }

            $successCount = 0;
            $failCount = 0;

            foreach ($goods as $item) {
                try {
                    // 使用多页获取方法
                    $resultData = $instance->getMultiPageReviews($samClub, $item->spuId, $reviewCount);

                    if ($resultData && isset($resultData['data'])) {
                        // 保存商品评价
                        $instance->saveGoodsReview(['data' => $resultData['data']], $item->spuId);
                        $successCount++;

                        Log::info("批次{$taskIndex}商品评价采集成功 (spuId: {$item->spuId}), 评价数量: " . count($resultData['data']['pageList'] ?? []));
                    } else {
                        $failCount++;
                        Log::error("批次{$taskIndex}商品评价采集失败 (spuId: {$item->spuId})");
                    }

                    // 避免请求过快
                    usleep(500000); // 休眠500毫秒，因为可能有多次API调用
                } catch (\Throwable $e) {
                    Log::error("批次{$taskIndex}处理商品评价失败 (spuId: {$item->spuId}): " . $e->getMessage());
                    $failCount++;
                }
            }

            Log::info("批次{$taskIndex}商品评价同步完成: 成功 {$successCount}, 失败 {$failCount}");
            return true;
        } catch (\Throwable $e) {
            Log::error("批次同步商品评价异常: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 通过商品ID获取评价数据
     *
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function getEvaluateBySpuId(Request $request)
    {
        try {
            // 获取参数
            $spuId = $request->param('spu_id', '', 'trim');

            // 验证参数
            if (empty($spuId)) {
                return $this->error('商品ID不能为空');
            }

            // 查询商品评价数据
            $goods = Goods::where('spuId', $spuId)
                         ->where('status', 1)
                         ->field('spuId, name, evaluate')
                         ->find();

            if (!$goods) {
                return $this->error('商品不存在或已下架');
            }

            // 解析评价数据
            $evaluateData = [];
            if (!empty($goods->evaluate)) {
                $evaluateData = json_decode($goods->evaluate, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $evaluateData = [];
                }
            }

            // 构建返回数据
            $result = [
                'spuId' => $goods->spuId,
                'name' => $goods->name,
                'evaluate_count' => count($evaluateData),
                'evaluate_data' => $evaluateData
            ];

            return $this->success('获取评价数据成功', $result);

        } catch (\Throwable $e) {
            Log::error("获取商品评价数据失败 (spuId: {$spuId}): " . $e->getMessage());
            return $this->error('获取评价数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量通过商品ID获取评价数据
     *
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function batchGetEvaluateBySpuIds(Request $request)
    {
        try {
            // 获取参数
            $spuIds = $request->param('spu_ids', '', 'trim'); // 商品ID列表，逗号分隔

            // 验证参数
            if (empty($spuIds)) {
                return $this->error('商品ID列表不能为空');
            }

            // 解析商品ID列表
            $spuIdArray = array_filter(array_map('trim', explode(',', $spuIds)));
            if (empty($spuIdArray)) {
                return $this->error('商品ID列表格式错误');
            }

            // 限制查询数量，避免性能问题
            if (count($spuIdArray) > 100) {
                return $this->error('单次查询商品数量不能超过100个');
            }

            // 查询商品评价数据
            $goodsList = Goods::whereIn('spuId', $spuIdArray)
                            ->where('status', 1)
                            ->field('spuId, name, evaluate')
                            ->select();

            if ($goodsList->isEmpty()) {
                return $this->error('未找到任何商品');
            }

            // 处理评价数据
            $result = [];
            foreach ($goodsList as $goods) {
                // 解析评价数据
                $evaluateData = [];
                if (!empty($goods->evaluate)) {
                    $evaluateData = json_decode($goods->evaluate, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        $evaluateData = [];
                    }
                }

                $result[] = [
                    'spuId' => $goods->spuId,
                    'name' => $goods->name,
                    'evaluate_count' => count($evaluateData),
                    'evaluate_data' => $evaluateData
                ];
            }

            return $this->success('批量获取评价数据成功', [
                'total' => count($result),
                'goods_list' => $result
            ]);

        } catch (\Throwable $e) {
            Log::error("批量获取商品评价数据失败: " . $e->getMessage());
            return $this->error('批量获取评价数据失败: ' . $e->getMessage());
        }
    }
}
