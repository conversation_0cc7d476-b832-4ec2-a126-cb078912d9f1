<?php

namespace app\api\controller\Sams;

use app\common\model\SamClub;
use app\common\model\sam\Goods;
use think\facade\Log;
use think\Request;
use think\Response;


/**
 * 山姆会员店商品评价数据采集
 */
class SamsGoodsReview extends SamsBase
{
    /**
     * 最大重试次数
     */
    protected static $maxRetries = 3;
    
    /**
     * 重试间隔（毫秒）
     */
    protected static $retryDelay = 500;

    /**
     * 获取商品评价并保存到数据库
     * 
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function getGoodsReview(Request $request)
    {
        // 获取参数
        $spuId = $request->param('spu_id', '', 'trim');
        $create_task = $request->param('create_task', 0, 'intval');
        $page_num = $request->param('page_num', 1, 'intval');

        // 如果需要创建定时任务
        if ($create_task) {
            $tokenInfo = $this->getValidToken();
            if (!$tokenInfo) {
                return $this->error('没有找到有效的token');
            }
            
            // 创建一个每天执行一次的任务
            $taskName = "山姆商品评价数据每日更新";
            $taskDescription = "自动同步山姆会员店商品评价数据";
            
            // 创建定时任务，每天凌晨4点执行
            $taskCreated = self::createTask(
                $taskName, 
                'SamsGoodsReview',
                'syncGoodsReview', 
                '0 4 * * *',  // 每天凌晨4点执行
                $taskDescription,
                [],
                0  // 设置为无限循环
            );
            
            return $this->success('创建商品评价数据定时任务完成', [
                'token_phone' => $tokenInfo['phone'],
                'task_created' => $taskCreated ? 'success' : 'failed'
            ]);
        }
        
        // 直接获取商品评价
        if (empty($spuId)) {
            return $this->error('商品ID不能为空');
        }
        
        return $this->handleDataFetch(
            '山姆商品评价获取',                // 任务名称
            'SamsGoodsReview',               // 控制器名称
            'syncGoodsReview',               // 方法名称
            '0 4 * * *',                    // cron表达式 - 每天凌晨4点执行
            '自动同步山姆商品评价数据',        // 任务备注
            function($samClub, $instance) use ($spuId, $page_num) {    // 获取数据的回调
                // 添加重试逻辑
                $retry = 0;
                
                while ($retry < self::$maxRetries) {
                    if ($retry > 0) {
                        Log::info("商品评价获取重试 #{$retry} (spuId: {$spuId})");
                        usleep(self::$retryDelay * 1000); // 重试前延迟
                    }
                    
                    // 获取有效的token
                    $tokenInfo = $instance->getValidToken();
                    if (!$tokenInfo) {
                        Log::error('没有找到有效的token，重试中...');
                        $retry++;
                        continue;
                    }
                    
                    $token = $tokenInfo['token'];
                    
                    // 调用API获取数据
                    $options = [
                        'pageNum' => $page_num,
                        'pageSize' => 20,  // 固定为20，不能自定义
                        'tagIdList' => '',
                        'desc' => 1,
                        'commentLevel' => 3,
                        'queryContentType' => 2
                    ];
                    
                    $result = $samClub->get_goods_comment_list($spuId, $token, '', $instance->getProxyIp(), $options);
                    $resultData = $instance->handleApiResponse($result, '获取商品评价失败', $token);
                    
                    if ($resultData && isset($resultData['data'])) {
                        return $resultData;
                    }
                    
                    $retry++;
                }
                
                Log::error("获取商品评价失败，已重试{$retry}次 (spuId: {$spuId})");
                return false;
            },
            function($data, $instance) use ($spuId) {      // 处理数据的回调
                // handleDataFetch已经提取了data部分，直接处理
                return $this->saveGoodsReview(['data' => $data], $spuId);
            },
            '商品评价获取并保存成功',          // 成功消息
            '获取商品评价失败',               // 错误消息
            Goods::class                     // 模型类名
        );
    }
    
    /**
     * 保存商品评价数据
     *
     * @param array $data 评价数据
     * @param string $spuId 商品ID
     * @return bool 是否保存成功
     */
    protected function saveGoodsReview($data, $spuId)
    {
        try {
            // 检查数据是否为空
            if (empty($data['data'])) {
                Log::error("商品评价数据为空 (spuId: {$spuId})");
                return false;
            }

            // 查找商品
            $goods = Goods::where('spuId', $spuId)->find();

            if (!$goods) {
                Log::error("未找到商品 (spuId: {$spuId})");
                return false;
            }

            $reviewData = $data['data'];
            $processedReviews = [];

            // 处理pageList中的评价数据，只保留指定字段
            if (isset($reviewData['pageList']) && !empty($reviewData['pageList'])) {
                foreach ($reviewData['pageList'] as $review) {
                    $processedReview = [
                        'commentContent' => $review['commentContent'] ?? '',
                        'commentImageUrls' => $review['commentImageUrls'] ?? [],
                        'userName' => $review['userName'] ?? '',
                        'userHeadUrl' => $review['userHeadUrl'] ?? '',
                        'commentTime' => $review['commentTime'] ?? ''
                    ];
                    $processedReviews[] = $processedReview;
                }
            }

            // 将处理后的评价数据保存到evaluate字段
            $goods->evaluate = json_encode($processedReviews, JSON_UNESCAPED_UNICODE);
            $goods->save();

            Log::info("商品评价保存成功 (spuId: {$spuId}), 评价数量: " . count($processedReviews));
            return true;

        } catch (\Throwable $e) {
            Log::error("保存商品评价失败 (spuId: {$spuId}): " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 批量获取商品评价
     * 
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function batchGetGoodsReview(Request $request)
    {
        set_time_limit(0); // 不限制执行时间
        ini_set('memory_limit', '512M'); // 设置内存限制
        
        // 获取参数
        $limit = $request->param('limit', 50, 'intval');
        
        // 查询需要获取评价的商品，优先获取evaluate为NULL的商品
        $goods = Goods::where('status', 1)
            ->whereRaw('evaluate IS NULL OR evaluate = ""')
            ->limit($limit)
            ->select();
        
        if ($goods->isEmpty()) {
            return $this->error('未找到需要采集评价的商品');
        }
        
        $samClub = new SamClub();
        $successCount = 0;
        $failCount = 0;
        $results = [];
        
        foreach ($goods as $item) {
            try {
                $retry = 0;
                $success = false;
                
                while ($retry < self::$maxRetries && !$success) {
                    if ($retry > 0) {
                        Log::info("商品评价获取重试 #{$retry} (spuId: {$item->spuId})");
                        usleep(self::$retryDelay * 1000); // 重试前延迟
                    }
                    
                    // 每次API调用前获取最新的有效token
                    $tokenInfo = $this->getValidToken();
                    if (!$tokenInfo) {
                        Log::error("获取token失败，重试中... (spuId: {$item->spuId})");
                        $retry++;
                        continue;
                    }
                    
                    $token = $tokenInfo['token'];
                    
                    // 调用API获取数据
                    $options = [
                        'pageNum' => 1,
                        'pageSize' => 20,  // 固定为20，不能自定义
                        'tagIdList' => '',
                        'desc' => 1,
                        'commentLevel' => 3,
                        'queryContentType' => 2
                    ];
                    
                    $result = $samClub->get_goods_comment_list($item->spuId, $token, '', $this->getProxyIp(), $options);
                    $resultData = $this->handleApiResponse($result, "获取商品评价失败 (spuId: {$item->spuId})", $token);
                    
                    if (!$resultData || !isset($resultData['data'])) {
                        $retry++;
                        continue;
                    }
                    
                    // 保存商品评价，将data包装在一个数组中，与saveGoodsReview方法的期望格式一致
                    $this->saveGoodsReview(['data' => $resultData['data']], $item->spuId);
                    $successCount++;
                    
                    $results[] = [
                        'spuId' => $item->spuId,
                        'name' => $item->name,
                        'status' => 'success',
                        'retries' => $retry
                    ];
                    
                    $success = true;
                }
                
                if (!$success) {
                    Log::error("商品评价获取失败，已重试{$retry}次 (spuId: {$item->spuId})");
                    $failCount++;
                    
                    $results[] = [
                        'spuId' => $item->spuId,
                        'name' => $item->name,
                        'status' => 'failed',
                        'retries' => $retry
                    ];
                }
                
                // 避免请求过快
                usleep(300000); // 休眠300毫秒
            } catch (\Throwable $e) {
                Log::error("处理商品评价失败 (spuId: {$item->spuId}): " . $e->getMessage());
                $failCount++;
                
                $results[] = [
                    'spuId' => $item->spuId,
                    'name' => $item->name,
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $this->success('批量获取商品评价完成', [
            'total' => count($goods),
            'success' => $successCount,
            'failed' => $failCount,
            'results' => $results
        ]);
    }
    
    /**
     * 供计划任务调用的同步商品评价方法
     * 
     * @param array $params 包含参数的数组
     * @return bool 返回false将终止任务执行
     */
    public static function syncGoodsReview($params = [])
    {
        try {
            $instance = new self();
            $samClub = new SamClub();
            
            // 解析参数
            if (is_string($params)) {
                $params = json_decode($params, true) ?: [];
            } elseif (!is_array($params)) {
                $params = [];
            }
            
            // 获取批次大小参数，默认为30
            $batchSize = isset($params['batch_size']) ? intval($params['batch_size']) : 30;
            
            // 查询需要获取评价的商品
            $goods = Goods::where('status', 1)
                ->whereRaw('evaluate IS NULL OR evaluate = ""')
                ->limit($batchSize)
                ->select();
            
            if ($goods->isEmpty()) {
                Log::info('没有需要同步评价的商品');
                return true;
            }
            
            $successCount = 0;
            $failCount = 0;
            
            foreach ($goods as $item) {
                try {
                    $retry = 0;
                    $success = false;
                    
                    while ($retry < self::$maxRetries && !$success) {
                        if ($retry > 0) {
                            Log::info("商品评价获取重试 #{$retry} (spuId: {$item->spuId})");
                            usleep(self::$retryDelay * 1000); // 重试前延迟
                        }
                        
                        // 每次API调用前获取最新的有效token
                        $tokenInfo = $instance->getValidToken();
                        if (!$tokenInfo) {
                            Log::error("获取token失败，重试中... (spuId: {$item->spuId})");
                            $retry++;
                            continue;
                        }
                        
                        $token = $tokenInfo['token'];
                        
                        // 调用API获取数据
                        $options = [
                            'pageNum' => 1,
                            'pageSize' => 20,  // 固定为20，不能自定义
                            'tagIdList' => '',
                            'desc' => 1,
                            'commentLevel' => 3,
                            'queryContentType' => 2
                        ];
                        
                        $result = $samClub->get_goods_comment_list($item->spuId, $token, '', $instance->getProxyIp(), $options);
                        $resultData = $instance->handleApiResponse($result, "获取商品评价失败 (spuId: {$item->spuId})", $token);
                        
                        if (!$resultData || !isset($resultData['data'])) {
                            $retry++;
                            continue;
                        }
                        
                        // 保存商品评价，将data包装在一个数组中，与saveGoodsReview方法的期望格式一致
                        $instance->saveGoodsReview(['data' => $resultData['data']], $item->spuId);
                        $successCount++;
                        
                        $success = true;
                    }
                    
                    if (!$success) {
                        Log::error("商品评价获取失败，已重试{$retry}次 (spuId: {$item->spuId})");
                        $failCount++;
                    }
                    
                    // 避免请求过快
                    usleep(300000); // 休眠300毫秒
                } catch (\Throwable $e) {
                    Log::error("处理商品评价失败 (spuId: {$item->spuId}): " . $e->getMessage());
                    $failCount++;
                }
            }
            
            Log::info("商品评价同步完成: 成功 {$successCount}, 失败 {$failCount}");
            return true;
        } catch (\Throwable $e) {
            Log::error('同步商品评价异常: ' . $e->getMessage());
            return false;
        }
    }
}
