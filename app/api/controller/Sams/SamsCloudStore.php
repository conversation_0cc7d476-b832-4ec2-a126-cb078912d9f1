<?php

namespace app\api\controller\Sams;

use app\common\model\SamClub;
use app\common\model\sam\Store;
use app\common\model\sam\store\Goods as StoreGoods;
use think\facade\Log;

/**
 * 山姆会员店云仓数据采集
 */
class SamsCloudStore extends SamsBase
{
    /**
     * 获取山姆云仓列表并写入数据库
     */
    public function getCloudStores()
    {
        return $this->handleDataFetch(
            '山姆云仓数据自动更新',                 // 任务名称
            'SamsCloudStore',                     // 控制器名称
            'syncCloudStores',                    // 方法名称
            '0 10 * * *',                         // cron表达式 - 每天上午10点执行
            '自动同步山姆会员店云仓数据',          // 任务备注
            function($samClub, $instance) {      // 获取数据的回调
                // 获取有效的token
                $tokenInfo = $instance->getValidToken();
                if (!$tokenInfo) {
                    Log::error('没有找到有效的token');
                    return false;
                }
                
                $token = $tokenInfo['token'];
                
                // 调用API获取数据
                $result = $samClub->get_stores_ticket($token, '', $instance->getProxyIp());
                return $instance->handleApiResponse($result, '获取云仓数据失败', $token);
            },
            function($data, $instance) {         // 处理数据的回调
                return $this->processAndSaveCloudStores($data);
            },
            '云仓数据获取并保存成功',              // 成功消息
            '获取云仓数据失败',                   // 错误消息
            Store::class                         // 模型类名
        );
    }

    /**
     * 处理并保存云仓数据
     * 
     * @param array $data 云仓数据
     * @return int 处理的云仓数量
     */
    protected function processAndSaveCloudStores($data)
    {
        // 检查数据结构
        if (!isset($data['storeScopeList']) || empty($data['storeScopeList'])) {
            return 0;
        }
        
        // 批量保存所有云仓
        return $this->batchSaveStores($data['storeScopeList'], '');
    }
    
    /**
     * 批量保存门店数据
     *
     * @param array $stores 门店数据数组
     * @param string $remark 备注
     * @return int 成功保存的数量
     */
    protected function batchSaveStores($stores, $remark = '')
    {
        if (empty($stores)) {
            return 0;
        }

        // 保存门店数据
        $savedCount = self::batchSaveData(
            $stores,
            'storeId',
            function($store, $now) use ($remark) {
                // 处理经纬度
                $location = '';
                if (isset($store['longitude']) && isset($store['latitude']) &&
                    !empty($store['longitude']) && !empty($store['latitude'])) {
                    $location = $store['longitude'] . ',' . $store['latitude'];
                }

                // 获取门店类型
                $type = isset($store['storeType']) ? $store['storeType'] : '2';

                return [
                    'name' => $store['storeName'],
                    'storeId' => $store['storeId'],
                    'type' => $type,
                    'city' => $store['cityName'] ?? '',
                    'address' => $store['storeAddress'] ?? '',
                    'location' => $location,
                    'postcode' => isset($store['districtCode']) ? $store['districtCode'] : (isset($store['cityCode']) ? $store['cityCode'] : ''),
                    'status' => intval($store['status'] ?? 1),
                    'update_time' => $now,
                    'remark' => $remark ?: ($store['provinceName'] ?? '') . ($store['cityName'] ?? '') . ($store['districtName'] ?? '')
                ];
            },
            Store::class
        );

        // 保存完成后，统计并更新每个云仓的商品数量
        $this->updateStoreGoodsCount($stores);

        return $savedCount;
    }

    /**
     * 统计并更新门店商品数量
     *
     * @param array $stores 门店数据数组
     * @return void
     */
    protected function updateStoreGoodsCount($stores)
    {
        try {
            foreach ($stores as $store) {
                if (!isset($store['storeId'])) {
                    continue;
                }

                $storeId = $store['storeId'];

                // 统计该门店的商品数量
                $goodsCount = StoreGoods::where('sam_store_id', $storeId)->count();

                // 更新门店的商品数量字段
                Store::where('storeId', $storeId)->update([
                    'store_goods_count' => $goodsCount,
                    'update_time' => time()
                ]);

                Log::info("云仓商品数量统计完成 | 门店ID: {$storeId} | 商品数量: {$goodsCount}");
            }
        } catch (\Throwable $e) {
            Log::error("统计云仓商品数量失败: " . $e->getMessage());
        }
    }

    /**
     * 供计划任务调用的同步云仓数据方法
     * 
     * @param array $params 包含token等参数的数组
     * @return bool 返回false将终止任务执行
     */
    public static function syncCloudStores($params = [])
    {
        return parent::handleSync(
            function($samClub, $instance, $device_id, $ip) {
                // 获取有效的token
                $tokenInfo = $instance->getValidToken();
                if (!$tokenInfo) {
                    Log::error('同步云仓数据失败: 没有找到有效的token');
                    return false;
                }
                
                $token = $tokenInfo['token'];
                
                // 调用API获取数据
                $result = $samClub->get_stores_ticket($token, $device_id, $ip);
                return $instance->handleApiResponse($result, '获取云仓数据失败', $token);
            },
            function($data, $instance, $device_id, $ip, $samClub) {
                if (!isset($data['storeScopeList'])) {
                    return;
                }
                
                $storeList = $data['storeScopeList'];
                
                // 实例化当前类，以便调用实例方法
                $instance = new self();
                
                // 批量保存所有云仓
                $instance->batchSaveStores($storeList, '');
            },
            '获取云仓数据失败',
            Store::class
        );
    }
} 