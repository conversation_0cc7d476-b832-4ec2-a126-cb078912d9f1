<?php

namespace app\api\controller\Sams;

use app\common\model\SamClub;
use app\common\model\sam\Goods;
use think\facade\Log;
use think\Request;
use think\Response;

/**
 * 山姆会员店商品详情数据采集
 */
class SamsGoodsDetail extends SamsBase
{
    /**
     * 最大重试次数
     */
    protected static $maxRetries = 3;
    
    /**
     * 重试间隔（毫秒）
     */
    protected static $retryDelay = 500;

    /**
     * 获取商品详情并保存到数据库
     * 
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function getGoodsDetail(Request $request)
    {
        // 获取参数
        $spuId = $request->param('spu_id', '', 'trim');
        $create_task = $request->param('create_task', 0, 'intval');

        // 如果需要创建定时任务
        if ($create_task) {
            $tokenInfo = $this->getValidToken();
            if (!$tokenInfo) {
                return $this->error('没有找到有效的token');
            }
            
            // 创建一个每天执行一次的任务
            $taskName = "山姆商品详情数据每日更新";
            $taskDescription = "自动同步山姆会员店商品详情数据";
            
            // 创建定时任务，每天凌晨3点执行
            $taskCreated = self::createTask(
                $taskName, 
                'SamsGoodsDetail',
                'syncGoodsDetail', 
                '0 6 * * *',  // 每天上午11点执行
                $taskDescription,
                [],
                0  // 设置为无限循环
            );
            
            return $this->success('创建商品详情数据定时任务完成', [
                'token_phone' => $tokenInfo['phone'],
                'task_created' => $taskCreated ? 'success' : 'failed'
            ]);
        }
        
        // 直接获取商品详情
        if (empty($spuId)) {
            return $this->error('商品ID不能为空');
        }
        
        return $this->handleDataFetch(
            '山姆商品详情获取',                // 任务名称
            'SamsGoodsDetail',               // 控制器名称
            'syncGoodsDetail',               // 方法名称
            '0 6 * * *',                    // cron表达式 - 每天上午11点执行
            '自动同步山姆商品详情数据',        // 任务备注
            function($samClub, $instance) use ($spuId) {    // 获取数据的回调
                // 添加重试逻辑
                $retry = 0;
                
                while ($retry < self::$maxRetries) {
                    if ($retry > 0) {
                        Log::info("商品详情获取重试 #{$retry} (spuId: {$spuId})");
                        usleep(self::$retryDelay * 1000); // 重试前延迟
                    }
                    
                    // 获取有效的token
                    $tokenInfo = $instance->getValidToken();
                    if (!$tokenInfo) {
                        Log::error('没有找到有效的token，重试中...');
                        $retry++;
                        continue;
                    }
                    
                    $token = $tokenInfo['token'];
                    
                    // 调用API获取数据
                    $result = $samClub->get_product_detail($spuId, $token, '', $instance->getProxyIp());
                    $resultData = $instance->handleApiResponse($result, '获取商品详情失败', $token);
                    
                    if ($resultData && isset($resultData['data'])) {
                        return $resultData;
                    }
                    
                    $retry++;
                }
                
                Log::error("获取商品详情失败，已重试{$retry}次 (spuId: {$spuId})");
                return false;
            },
            function($data, $instance) use ($spuId) {      // 处理数据的回调
                // handleDataFetch已经提取了data部分，直接处理
                return $this->saveGoodsDetail(['data' => $data], $spuId);
            },
            '商品详情获取并保存成功',          // 成功消息
            '获取商品详情失败',               // 错误消息
            Goods::class                     // 模型类名
        );
    }
    
    /**
     * 保存商品详情数据
     * 
     * @param array $data 详情数据
     * @param string $spuId 商品ID
     * @return bool 是否保存成功
     */
    protected function saveGoodsDetail($data, $spuId)
    {
        try {
            // 检查数据是否为空
            if (empty($data['data'])) {
                Log::error("商品详情数据为空 (spuId: {$spuId})");
                return false;
            }
            
            // 查找商品
            $goods = Goods::where('spuId', $spuId)->find();
            
            if (!$goods) {
                Log::error("未找到商品 (spuId: {$spuId})");
                return false;
            }
            
            // 处理hostUpc数据
            $hostUpc = $data['data']['spuExtDTO']['hostUpc'] ?? [];
            $formattedHostUpc = [];
            
            // 如果hostUpc是数组，则转换为指定格式
            if (!empty($hostUpc) && is_array($hostUpc)) {
                foreach ($hostUpc as $upc) {
                    if (is_string($upc)) {
                        $formattedHostUpc[] = [
                            'key' => '',
                            'value' => $upc
                        ];
                    }
                }
                
                // 将格式化后的hostUpc保存到hostUpc字段
                if (!empty($formattedHostUpc)) {
                    $goods->hostUpc = $formattedHostUpc;
                }
            }


            // 将整个data数据保存到detail字段
            $goods->detail = json_encode($data['data'], JSON_UNESCAPED_UNICODE);
            $goods->save();
            
            return true;
        } catch (\Throwable $e) {
            Log::error("保存商品详情失败 (spuId: {$spuId}): " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 批量获取商品详情
     * 
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function batchGetGoodsDetail(Request $request)
    {
        set_time_limit(0); // 不限制执行时间
        ini_set('memory_limit', '512M'); // 设置内存限制
        
        // 获取参数
        $limit = $request->param('limit', 50, 'intval');
        
        // 查询需要获取详情的商品，优先获取detail为NULL的商品
        $goods = Goods::where('status', 1)
            ->whereRaw('detail IS NULL OR detail = ""')
            ->limit($limit)
            ->select();
        
        if ($goods->isEmpty()) {
            return $this->error('未找到需要采集详情的商品');
        }
        
        $samClub = new SamClub();
        $successCount = 0;
        $failCount = 0;
        $results = [];
        
        foreach ($goods as $item) {
            try {
                $retry = 0;
                $success = false;
                
                while ($retry < self::$maxRetries && !$success) {
                    if ($retry > 0) {
                        Log::info("商品详情获取重试 #{$retry} (spuId: {$item->spuId})");
                        usleep(self::$retryDelay * 1000); // 重试前延迟
                    }
                    
                    // 每次API调用前获取最新的有效token
                    $tokenInfo = $this->getValidToken();
                    if (!$tokenInfo) {
                        Log::error("获取token失败，重试中... (spuId: {$item->spuId})");
                        $retry++;
                        continue;
                    }
                    
                    $token = $tokenInfo['token'];
                    
                    // 调用API获取数据
                    $result = $samClub->get_product_detail($item->spuId, $token, '', $this->getProxyIp());
                    $resultData = $this->handleApiResponse($result, "获取商品详情失败 (spuId: {$item->spuId})", $token);
                    
                    if (!$resultData || !isset($resultData['data'])) {
                        $retry++;
                        continue;
                    }
                    
                    // 保存商品详情，将data包装在一个数组中，与saveGoodsDetail方法的期望格式一致
                    $this->saveGoodsDetail(['data' => $resultData['data']], $item->spuId);
                    $successCount++;
                    
                    $results[] = [
                        'spuId' => $item->spuId,
                        'title' => $resultData['data']['title'] ?? '',
                        'status' => 'success',
                        'retries' => $retry
                    ];
                    
                    $success = true;
                }
                
                if (!$success) {
                    $failCount++;
                    
                    $results[] = [
                        'spuId' => $item->spuId,
                        'status' => 'failed',
                        'error' => "达到最大重试次数({$retry}次)后仍然失败"
                    ];
                }
                
                // 避免请求过快
                usleep(200000); // 休眠200毫秒
            } catch (\Throwable $e) {
                Log::error("处理商品详情失败 (spuId: {$item->spuId}): " . $e->getMessage());
                $failCount++;
                
                $results[] = [
                    'spuId' => $item->spuId,
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $this->success('批量获取商品详情完成', [
            'total' => count($goods),
            'success' => $successCount,
            'fail' => $failCount,
            'results' => $results
        ]);
    }
    
    /**
     * 供计划任务调用的同步商品详情方法
     * 
     * @param array $params 任务参数数组
     * @return bool 返回false将终止任务执行
     */
    public static function syncGoodsDetail($params = [])
    {
        try {
            $instance = new self();
            $samClub = new SamClub();
            
            // 查询需要获取详情的商品，优先获取detail为NULL的商品
            $goods = Goods::where('status', 1)
                ->whereRaw('detail IS NULL OR detail = ""')
                ->select();
            
            if ($goods->isEmpty()) {
                Log::info('没有需要同步详情的商品');
                return true;
            }
            
            $successCount = 0;
            $failCount = 0;
            
            foreach ($goods as $item) {
                try {
                    $retry = 0;
                    $success = false;
                    
                    while ($retry < self::$maxRetries && !$success) {
                        if ($retry > 0) {
                            Log::info("商品详情获取重试 #{$retry} (spuId: {$item->spuId})");
                            usleep(self::$retryDelay * 1000); // 重试前延迟
                        }
                        
                        // 每次API调用前获取最新的有效token
                        $tokenInfo = $instance->getValidToken();
                        if (!$tokenInfo) {
                            Log::error("获取token失败，重试中... (spuId: {$item->spuId})");
                            $retry++;
                            continue;
                        }
                        
                        $token = $tokenInfo['token'];
                        
                        // 调用API获取数据
                        $result = $samClub->get_product_detail($item->spuId, $token, '', $instance->getProxyIp());
                        $resultData = $instance->handleApiResponse($result, "获取商品详情失败 (spuId: {$item->spuId})", $token);
                        
                        if (!$resultData || !isset($resultData['data'])) {
                            $retry++;
                            continue;
                        }
                        
                        // 保存商品详情，将data包装在一个数组中，与saveGoodsDetail方法的期望格式一致
                        $instance->saveGoodsDetail(['data' => $resultData['data']], $item->spuId);
                        $successCount++;
                        
                        $success = true;
                    }
                    
                    if (!$success) {
                        Log::error("商品详情获取失败，已重试{$retry}次 (spuId: {$item->spuId})");
                        $failCount++;
                    }
                    
                    // 避免请求过快
                    usleep(200000); // 休眠200毫秒
                } catch (\Throwable $e) {
                    Log::error("处理商品详情失败 (spuId: {$item->spuId}): " . $e->getMessage());
                    $failCount++;
                }
            }
            
            Log::info("商品详情同步完成: 成功 {$successCount}, 失败 {$failCount}");
            return true;
        } catch (\Throwable $e) {
            Log::error('同步商品详情异常: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取未分类的商品数据(catLv1、catLv2、catLv3等于0或为空或为NULL)
     * 
     * @param Request $request 请求对象
     * @return Response 响应对象
     */
    public function getUncategorizedGoods(Request $request)
    {
        try {
            // 查询所有未分类商品（分类为0或空或NULL）
            $goods = Goods::where('status', 1)
                ->where(function($query) {
                    $query->whereRaw('catLv1 = 0 OR catLv1 = "" OR catLv1 IS NULL');
                })
                ->where(function($query) {
                    $query->whereRaw('catLv2 = 0 OR catLv2 = "" OR catLv2 IS NULL');
                })
                ->where(function($query) {
                    $query->whereRaw('catLv3 = 0 OR catLv3 = "" OR catLv3 IS NULL');
                })
                ->field('id, spuId, name, category1, category2, category3')
                ->order('id', 'desc')
                ->select();
            
            // 获取总数
            $total = count($goods);
            
            if ($total == 0) {
                return $this->success('没有找到未分类商品', [
                    'total' => 0,
                    'list' => []
                ]);
            }
            
            // 处理结果
            $list = [];
            foreach ($goods as $item) {
                $list[] = [
                    'id' => $item->id,
                    'spuId' => $item->spuId,
                    'name' => $item->name,
                    'category1' => $item->category1 ?: '',
                    'category2' => $item->category2 ?: '',
                    'category3' => $item->category3 ?: ''
                ];
            }
            
            // 记录更新结果
            $updateResults = [];
            $updateCount = 0;
            
            // 遍历list
            foreach ($list as $item) {
                // 获取category1、category2、category3
                $category1 = $item['category1'];
                $category2 = $item['category2'];
                $category3 = $item['category3'];
                
                // 通过category1、category2、category3查找对应的商品，取相同最多的catLv1、catLv2和catLv3更新
                $updateInfo = $this->updateCategoryLevels($item['id'], $category1, $category2, $category3);
                
                if ($updateInfo['updated']) {
                    $updateCount++;
                    $updateResults[] = [
                        'id' => $item['id'],
                        'spuId' => $item['spuId'],
                        'name' => $item['name'],
                        'old_categories' => [
                            'category1' => $category1,
                            'category2' => $category2,
                            'category3' => $category3
                        ],
                        'new_categories' => [
                            'catLv1' => $updateInfo['catLv1'],
                            'catLv2' => $updateInfo['catLv2'],
                            'catLv3' => $updateInfo['catLv3']
                        ]
                    ];
                }
            }
            
            return $this->success('获取未分类商品成功', [
                'total' => $total,
                'updated' => $updateCount,
                'list' => $list,
                'update_results' => $updateResults
            ]);
            
        } catch (\Throwable $e) {
            Log::error('获取未分类商品失败: ' . $e->getMessage());
            return $this->error('获取未分类商品失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新商品的分类级别
     * 
     * @param int $id 商品ID
     * @param string $category1 分类1
     * @param string $category2 分类2
     * @param string $category3 分类3
     * @return array 更新结果
     */
    public function updateCategoryLevels($id, $category1, $category2, $category3)
    {
        // 初始化返回结果
        $result = [
            'updated' => false,
            'catLv1' => 0,
            'catLv2' => 0,
            'catLv3' => 0
        ];
        
        try {
            // 检查分类是否为空
            if (empty($category1) && empty($category2) && empty($category3)) {
                return $result;
            }
            
            // 构建查询条件
            $conditions = [];
            
            if (!empty($category1)) {
                $conditions[] = ['category1', '=', $category1];
            }
            
            if (!empty($category2)) {
                $conditions[] = ['category2', '=', $category2];
            }
            
            if (!empty($category3)) {
                $conditions[] = ['category3', '=', $category3];
            }
            
            // 查询具有相同category的商品
            $query = Goods::where('status', 1)
                ->where(function($q) use ($conditions) {
                    foreach ($conditions as $condition) {
                        $q->where($condition[0], $condition[1], $condition[2]);
                    }
                })
                ->whereRaw('catLv1 > 0') // 确保只查找已分类的商品
                ->field('catLv1, catLv2, catLv3, COUNT(*) as count')
                ->group('catLv1, catLv2, catLv3')
                ->order('count', 'desc')
                ->limit(1);
                
            $similarGoods = $query->find();
            
            // 如果找到了相似商品
            if ($similarGoods && $similarGoods['count'] > 0) {
                // 获取出现频率最高的catLv值
                $catLv1 = $similarGoods['catLv1'];
                $catLv2 = $similarGoods['catLv2'];
                $catLv3 = $similarGoods['catLv3'];
                
                // 使用直接更新方式，避免事务冲突
                $updateResult = Goods::where('id', $id)->update([
                    'catLv1' => $catLv1,
                    'catLv2' => $catLv2,
                    'catLv3' => $catLv3,
                    'update_time' => time()
                ]);

                if ($updateResult) {
                    // 更新返回结果
                    $result['updated'] = true;
                    $result['catLv1'] = $catLv1;
                    $result['catLv2'] = $catLv2;
                    $result['catLv3'] = $catLv3;
                }
            } else if (!empty($category1) && !empty($category2)) {
                // 如果完全匹配未找到相似商品，尝试只匹配category1和category2
                $conditions = [];
                $conditions[] = ['category1', '=', $category1];
                $conditions[] = ['category2', '=', $category2];
                
                $query = Goods::where('status', 1)
                    ->where(function($q) use ($conditions) {
                        foreach ($conditions as $condition) {
                            $q->where($condition[0], $condition[1], $condition[2]);
                        }
                    })
                    ->whereRaw('catLv1 > 0') // 确保只查找已分类的商品
                    ->field('catLv1, catLv2, catLv3, COUNT(*) as count')
                    ->group('catLv1, catLv2, catLv3')
                    ->order('count', 'desc')
                    ->limit(1);
                    
                $similarGoods = $query->find();
                
                if ($similarGoods && $similarGoods['count'] > 0) {
                    // 获取出现频率最高的catLv值
                    $catLv1 = $similarGoods['catLv1'];
                    $catLv2 = $similarGoods['catLv2'];
                    $catLv3 = $similarGoods['catLv3'];
                    
                    // 使用直接更新方式，避免事务冲突
                    $updateResult = Goods::where('id', $id)->update([
                        'catLv1' => $catLv1,
                        'catLv2' => $catLv2,
                        'catLv3' => $catLv3,
                        'update_time' => time()
                    ]);

                    if ($updateResult) {
                        // 更新返回结果
                        $result['updated'] = true;
                        $result['catLv1'] = $catLv1;
                        $result['catLv2'] = $catLv2;
                        $result['catLv3'] = $catLv3;
                    }
                }
            }
            
            return $result;
        } catch (\Throwable $e) {
            Log::error("更新商品分类失败 (ID: {$id}): " . $e->getMessage());
            return $result;
        }
    }
} 