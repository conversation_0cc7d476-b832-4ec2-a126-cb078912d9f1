<?php

namespace app\api\controller\Sams;

use app\common\model\sam\Categories;
use app\common\model\sam\Goods;
use app\common\model\sam\store\Goods as StoreGoods;
use app\common\model\sam\Store;
use think\facade\Log;
use think\Request;
use think\Response;

/**
 * 山姆商品数据导出类
 */
class SamsGoodsExport extends SamsBase
{
    /**
     * 导出所有商品数据
     * 
     * @param Request $request
     * @return Response
     */
    public function exportAllGoods(Request $request)
    {
        try {
            // 获取参数
            $format = $request->param('format', 'csv', 'trim'); // 导出格式：csv, json, excel
            $storeId = $request->param('store_id', 0, 'intval'); // 门店ID筛选
            $categoryId = $request->param('category_id', 0, 'intval'); // 分类ID筛选
            $status = $request->param('status', 1, 'intval'); // 商品状态筛选
            $hasStock = $request->param('has_stock', -1, 'intval'); // 是否有库存：-1全部，0无库存，1有库存
            $dateFrom = $request->param('date_from', '', 'trim'); // 更新时间起始
            $dateTo = $request->param('date_to', '', 'trim'); // 更新时间结束
            $limit = $request->param('limit', 0, 'intval'); // 导出数量限制，0为不限制
            $specType = $request->param('spec_type', 'all', 'trim'); // 规格筛选：all全部，multi多规格，single单规格

            // 构建查询条件
            $goodsQuery = $this->buildGoodsQuery($storeId, $categoryId, $status, $hasStock, $dateFrom, $dateTo);

            // 获取总数量用于统计
            $totalCount = $goodsQuery->count();

            if ($totalCount == 0) {
                return $this->error('没有找到符合条件的商品数据');
            }

            // 应用数量限制
            if ($limit > 0) {
                $goodsQuery->limit($limit);
            }

            // 获取商品数据
            $goodsData = $this->getGoodsData($goodsQuery, $specType);

            // 根据格式导出
            switch ($format) {
                case 'json':
                    return $this->exportAsJson($goodsData, $totalCount);
                case 'excel':
                    return $this->exportAsExcel($goodsData, $totalCount);
                case 'csv':
                default:
                    return $this->exportAsCsv($goodsData, $totalCount);
            }

        } catch (\Throwable $e) {
            $this->debugLog('error', '导出商品数据失败: ' . $e->getMessage());
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 构建商品查询条件
     *
     * @param int $storeId
     * @param int $categoryId
     * @param int $status
     * @param int $hasStock
     * @param string $dateFrom
     * @param string $dateTo
     * @return \think\db\Query
     */
    protected function buildGoodsQuery($storeId, $categoryId, $status, $hasStock, $dateFrom, $dateTo)
    {
        // 基础查询：关联商品表和门店商品表
        $query = StoreGoods::alias('sg')
            ->join('sam_goods g', 'sg.sam_goods_id = g.spuId')
            ->join('sam_store s', 'sg.sam_store_id = s.storeId')
            ->field([
                'g.spuId',
                'g.name as goods_name',
                'g.imgUrl',
                'g.catLv1',
                'g.catLv2',
                'g.catLv3',
                'g.category1',
                'g.category2',
                'g.category3',
                'g.seriesId',
                'g.hostItemId',
                'g.hostUpc',
                'g.detail',
                'sg.sam_store_id',
                's.name as store_name',
                's.city as store_city',
                's.type as store_type',
                'sg.price',
                'sg.stock',
                'sg.status as store_goods_status',
                'FROM_UNIXTIME(sg.update_time) as last_update_time',
                'FROM_UNIXTIME(g.update_time) as goods_update_time'
            ]);

        // 门店筛选
        if ($storeId > 0) {
            $query->where('sg.sam_store_id', $storeId);
        }

        // 分类筛选
        if ($categoryId > 0) {
            $query->where(function($q) use ($categoryId) {
                $q->whereOr('g.catLv1', $categoryId)
                  ->whereOr('g.catLv2', $categoryId)
                  ->whereOr('g.catLv3', $categoryId);
            });
        }

        // 商品状态筛选
        if ($status >= 0) {
            $query->where('g.status', $status);
        }

        // 库存筛选
        if ($hasStock == 0) {
            $query->where('sg.stock', 0);
        } elseif ($hasStock == 1) {
            $query->where('sg.stock', '>', 0);
        }

        // 时间范围筛选
        if (!empty($dateFrom)) {
            $query->where('sg.update_time', '>=', strtotime($dateFrom));
        }
        if (!empty($dateTo)) {
            $query->where('sg.update_time', '<=', strtotime($dateTo . ' 23:59:59'));
        }

        // 排序
        $query->order('sg.update_time', 'desc');

        return $query;
    }

    /**
     * 获取商品数据并补充分类信息
     *
     * @param \think\db\Query $query
     * @param string $specType 规格筛选：all全部，multi多规格，single单规格
     * @return array
     */
    protected function getGoodsData($query, $specType = 'all')
    {
        $goods = $query->select()->toArray();

        if (empty($goods)) {
            return [];
        }

        // 获取所有分类信息
        $categories = Categories::select()->toArray();
        $categoryMap = [];
        foreach ($categories as $category) {
            $categoryMap[$category['groupingId']] = $category['name'];
        }

        // 规格筛选处理
        if ($specType !== 'all') {
            $filteredGoods = [];
            foreach ($goods as $item) {
                // 解析detail字段获取spuSpecInfo
                $detail = $this->parseDetailField($item['detail']);
                $spuSpecInfo = $detail['spuSpecInfo'] ?? null;

                // 判断是否符合规格筛选条件
                $isMultiSpec = !empty($spuSpecInfo) && is_array($spuSpecInfo);

                if ($specType === 'multi' && $isMultiSpec) {
                    // 导出多规格商品
                    $filteredGoods[] = $item;
                } elseif ($specType === 'single' && !$isMultiSpec) {
                    // 导出单规格商品
                    $filteredGoods[] = $item;
                }
            }
            $goods = $filteredGoods;
        }

        // 补充分类名称和其他信息
        foreach ($goods as &$item) {
            // 分类名称
            $item['catLv1_name'] = $categoryMap[$item['catLv1']] ?? '未知分类';
            $item['catLv2_name'] = $categoryMap[$item['catLv2']] ?? '未知分类';
            $item['catLv3_name'] = $categoryMap[$item['catLv3']] ?? '未知分类';

            // 价格转换（分转元）
            $item['price_yuan'] = round($item['price'] / 100, 2);

            // 门店类型名称
            $item['store_type_name'] = $this->getStoreTypeName($item['store_type']);

            // 库存状态
            $item['stock_status'] = $item['stock'] > 0 ? '有库存' : '无库存';

            // 处理UPC信息
            $item['upc'] = $this->extractUpc($item['hostUpc']);

            // 移除detail字段（避免在导出中显示）
            unset($item['detail']);
        }

        return $goods;
    }

    /**
     * 获取门店类型名称
     *
     * @param int $type
     * @return string
     */
    protected function getStoreTypeName($type)
    {
        $typeMap = [
            2 => '门店',
            4 => '极速达',
            8 => '全球购',
            256 => '全城配'
        ];

        return $typeMap[$type] ?? '未知类型';
    }

    /**
     * 提取UPC信息
     *
     * @param string $hostUpc
     * @return string
     */
    protected function extractUpc($hostUpc)
    {
        if (empty($hostUpc)) {
            return '';
        }

        if (is_string($hostUpc)) {
            $hostUpc = json_decode($hostUpc, true);
        }

        if (is_array($hostUpc) && !empty($hostUpc)) {
            foreach ($hostUpc as $value) {
                if (isset($value["value"])) {
                    return $value["value"];
                }
            }
        }

        return '';
    }

    /**
     * 获取不重复的门店数量
     *
     * @param int $storeId
     * @param int $categoryId
     * @param int $status
     * @param int $hasStock
     * @param string $dateFrom
     * @param string $dateTo
     * @return int
     */
    protected function getDistinctStoreCount($storeId, $categoryId, $status, $hasStock, $dateFrom, $dateTo)
    {
        // 构建基础查询
        $query = StoreGoods::alias('sg')
            ->join('sam_goods g', 'sg.sam_goods_id = g.spuId')
            ->join('sam_store s', 'sg.sam_store_id = s.storeId');

        // 门店筛选
        if ($storeId > 0) {
            $query->where('sg.sam_store_id', $storeId);
        }

        // 分类筛选
        if ($categoryId > 0) {
            $query->where(function($q) use ($categoryId) {
                $q->whereOr('g.catLv1', $categoryId)
                  ->whereOr('g.catLv2', $categoryId)
                  ->whereOr('g.catLv3', $categoryId);
            });
        }

        // 商品状态筛选
        if ($status >= 0) {
            $query->where('g.status', $status);
        }

        // 库存筛选
        if ($hasStock == 0) {
            $query->where('sg.stock', 0);
        } elseif ($hasStock == 1) {
            $query->where('sg.stock', '>', 0);
        }

        // 时间范围筛选
        if (!empty($dateFrom)) {
            $query->where('sg.update_time', '>=', strtotime($dateFrom));
        }
        if (!empty($dateTo)) {
            $query->where('sg.update_time', '<=', strtotime($dateTo . ' 23:59:59'));
        }

        // 使用GROUP BY来获取不重复的门店数量
        return $query->group('sg.sam_store_id')->count();
    }



    /**
     * 获取独立商品数量（sam_goods表）
     *
     * @param int $storeId
     * @param int $categoryId
     * @param int $status
     * @return int
     */
    protected function getIndependentGoodsCount($storeId, $categoryId, $status)
    {
        if ($storeId > 0) {
            // 如果指定了门店，需要关联门店商品表来筛选该门店有售的商品
            $query = Goods::alias('g')
                ->join('sam_store_goods sg', 'g.spuId = sg.sam_goods_id')
                ->where('sg.sam_store_id', $storeId)
                ->where('g.status', $status)
                ->group('g.spuId'); // 去重
        } else {
            // 如果没有指定门店，直接查询商品表
            $query = Goods::where('status', $status);
        }

        // 分类筛选
        if ($categoryId > 0) {
            if ($storeId > 0) {
                // 关联查询时使用表别名
                $query->where(function($q) use ($categoryId) {
                    $q->whereOr('g.catLv1', $categoryId)
                      ->whereOr('g.catLv2', $categoryId)
                      ->whereOr('g.catLv3', $categoryId);
                });
            } else {
                // 单表查询时不使用表别名
                $query->where(function($q) use ($categoryId) {
                    $q->whereOr('catLv1', $categoryId)
                      ->whereOr('catLv2', $categoryId)
                      ->whereOr('catLv3', $categoryId);
                });
            }
        }

        return $query->count();
    }

    /**
     * 导出为CSV格式
     *
     * @param array $goodsData
     * @param int $totalCount
     * @return Response
     */
    protected function exportAsCsv($goodsData, $totalCount)
    {
        // CSV头部
        $csv = "\xEF\xBB\xBF"; // UTF-8 BOM
        $csv .= "商品ID,商品名称,门店ID,门店名称,门店城市,门店类型,价格(元),库存,库存状态,一级分类,二级分类,三级分类,UPC,商品图片,最后更新时间\n";

        foreach ($goodsData as $item) {
            $row = [
                $item['spuId'],
                $item['goods_name'],
                $item['sam_store_id'],
                $item['store_name'],
                $item['store_city'],
                $item['store_type_name'],
                $item['price_yuan'],
                $item['stock'],
                $item['stock_status'],
                $item['catLv1_name'],
                $item['catLv2_name'],
                $item['catLv3_name'],
                $item['upc'],
                $item['imgUrl'],
                $item['last_update_time']
            ];

            // 处理CSV特殊字符
            $row = array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row);

            $csv .= implode(',', $row) . "\n";
        }

        // 设置响应头
        $filename = '山姆商品数据导出_' . date('YmdHis') . '.csv';

        return response($csv, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0'
        ]);
    }

    /**
     * 导出为JSON格式
     *
     * @param array $goodsData
     * @param int $totalCount
     * @return Response
     */
    protected function exportAsJson($goodsData, $totalCount)
    {
        $data = [
            'export_info' => [
                'export_time' => date('Y-m-d H:i:s'),
                'total_count' => $totalCount,
                'export_count' => count($goodsData),
                'format' => 'json'
            ],
            'goods_data' => $goodsData
        ];

        $json = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

        // 设置响应头
        $filename = '山姆商品数据导出_' . date('YmdHis') . '.json';

        return response($json, 200, [
            'Content-Type' => 'application/json; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0'
        ]);
    }

    /**
     * 导出为Excel格式（实际为CSV，但可被Excel打开）
     *
     * @param array $goodsData
     * @param int $totalCount
     * @return Response
     */
    protected function exportAsExcel($goodsData, $totalCount)
    {
        // 使用CSV格式但设置为Excel文件名
        $csv = "\xEF\xBB\xBF"; // UTF-8 BOM
        $csv .= "商品ID\t商品名称\t门店ID\t门店名称\t门店城市\t门店类型\t价格(元)\t库存\t库存状态\t一级分类\t二级分类\t三级分类\tUPC\t商品图片\t最后更新时间\n";

        foreach ($goodsData as $item) {
            $row = [
                $item['spuId'],
                $item['goods_name'],
                $item['sam_store_id'],
                $item['store_name'],
                $item['store_city'],
                $item['store_type_name'],
                $item['price_yuan'],
                $item['stock'],
                $item['stock_status'],
                $item['catLv1_name'],
                $item['catLv2_name'],
                $item['catLv3_name'],
                $item['upc'],
                $item['imgUrl'],
                $item['last_update_time']
            ];

            // 处理特殊字符，使用制表符分隔
            $row = array_map(function($field) {
                return str_replace(["\t", "\n", "\r"], [' ', ' ', ' '], $field);
            }, $row);

            $csv .= implode("\t", $row) . "\n";
        }

        // 设置响应头
        $filename = '山姆商品数据导出_' . date('YmdHis') . '.xls';

        return response($csv, 200, [
            'Content-Type' => 'application/vnd.ms-excel; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0'
        ]);
    }

    /**
     * 获取门店列表（用于筛选）
     *
     * @param Request $request
     * @return Response
     */
    public function getStoreList(Request $request)
    {
        try {
            $stores = Store::where('status', 1)
                ->field(['storeId', 'name', 'city', 'type'])
                ->order('city', 'asc')
                ->order('storeId', 'asc')
                ->select()
                ->toArray();

            // 添加门店类型名称
            foreach ($stores as &$store) {
                $store['type_name'] = $this->getStoreTypeName($store['type']);
            }

            return $this->success('获取成功', $stores);

        } catch (\Throwable $e) {
            $this->debugLog('error', '获取门店列表失败: ' . $e->getMessage());
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取分类列表（用于筛选）
     *
     * @param Request $request
     * @return Response
     */
    public function getCategoryList(Request $request)
    {
        try {
            $level = $request->param('level', 0, 'intval'); // 分类级别：0全部，1一级，2二级，3三级

            $query = Categories::where('status', 1);

            if ($level > 0) {
                $query->where('level', $level);
            }

            $categories = $query->field(['groupingId', 'name', 'level'])
                ->order('level', 'asc')
                ->order('groupingId', 'asc')
                ->select()
                ->toArray();

            return $this->success('获取成功', $categories);

        } catch (\Throwable $e) {
            $this->debugLog('error', '获取分类列表失败: ' . $e->getMessage());
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取导出统计信息
     *
     * @param Request $request
     * @return Response
     */
    public function getExportStatistics(Request $request)
    {
        try {
            // 获取筛选参数
            $storeId = $request->param('store_id', 0, 'intval');
            $categoryId = $request->param('category_id', 0, 'intval');
            $status = $request->param('status', 1, 'intval');
            $hasStock = $request->param('has_stock', -1, 'intval');
            $dateFrom = $request->param('date_from', '', 'trim');
            $dateTo = $request->param('date_to', '', 'trim');

            // 构建查询条件
            $baseQuery = $this->buildGoodsQuery($storeId, $categoryId, $status, $hasStock, $dateFrom, $dateTo);

            // 统计信息
            $statistics = [
                'total_goods_records' => $baseQuery->count(), // 门店商品记录总数
                'independent_goods_count' => $this->getIndependentGoodsCount($storeId, $categoryId, $status), // 独立商品总数（sam_goods表）
                'total_stores' => $this->getDistinctStoreCount($storeId, $categoryId, $status, $hasStock, $dateFrom, $dateTo),
                'has_stock_count' => $this->buildGoodsQuery($storeId, $categoryId, $status, $hasStock, $dateFrom, $dateTo)->where('sg.stock', '>', 0)->count(),
                'no_stock_count' => $this->buildGoodsQuery($storeId, $categoryId, $status, $hasStock, $dateFrom, $dateTo)->where('sg.stock', 0)->count(),
                'last_update' => date('Y-m-d H:i:s', $this->buildGoodsQuery($storeId, $categoryId, $status, $hasStock, $dateFrom, $dateTo)->max('sg.update_time'))
            ];

            return $this->success('获取成功', $statistics);

        } catch (\Throwable $e) {
            $this->debugLog('error', '获取导出统计失败: ' . $e->getMessage());
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 分页导出商品数据（用于大数据量导出）
     *
     * @param Request $request
     * @return Response
     */
    public function exportGoodsByPage(Request $request)
    {
        try {
            // 获取参数
            $page = $request->param('page', 1, 'intval');
            $pageSize = $request->param('page_size', 1000, 'intval'); // 每页数量
            $format = $request->param('format', 'csv', 'trim');
            $storeId = $request->param('store_id', 0, 'intval');
            $categoryId = $request->param('category_id', 0, 'intval');
            $status = $request->param('status', 1, 'intval');
            $hasStock = $request->param('has_stock', -1, 'intval');
            $dateFrom = $request->param('date_from', '', 'trim');
            $dateTo = $request->param('date_to', '', 'trim');
            $specType = $request->param('spec_type', 'all', 'trim'); // 规格筛选：all全部，multi多规格，single单规格

            // 限制每页最大数量
            if ($pageSize > 5000) {
                $pageSize = 5000;
            }

            // 构建查询条件
            $goodsQuery = $this->buildGoodsQuery($storeId, $categoryId, $status, $hasStock, $dateFrom, $dateTo);

            // 分页查询
            $offset = ($page - 1) * $pageSize;
            $goodsQuery->limit($offset, $pageSize);

            // 获取商品数据
            $goodsData = $this->getGoodsData($goodsQuery, $specType);

            if (empty($goodsData)) {
                return $this->error('没有找到符合条件的商品数据');
            }

            // 根据格式导出
            switch ($format) {
                case 'json':
                    return $this->exportAsJson($goodsData, count($goodsData));
                case 'excel':
                    return $this->exportAsExcel($goodsData, count($goodsData));
                case 'csv':
                default:
                    return $this->exportAsCsv($goodsData, count($goodsData));
            }

        } catch (\Throwable $e) {
            $this->debugLog('error', '分页导出商品数据失败: ' . $e->getMessage());
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出指定门店的所有商品
     *
     * @param Request $request
     * @return Response
     */
    public function exportStoreGoods(Request $request)
    {
        try {
            $storeId = $request->param('store_id', 0, 'intval');
            $format = $request->param('format', 'csv', 'trim');
            $specType = $request->param('spec_type', 'all', 'trim'); // 规格筛选：all全部，multi多规格，single单规格

            if ($storeId <= 0) {
                return $this->error('请指定门店ID');
            }

            // 检查门店是否存在
            $store = Store::where('storeId', $storeId)->find();
            if (!$store) {
                return $this->error('门店不存在');
            }

            // 构建查询条件（只查询指定门店）
            $goodsQuery = $this->buildGoodsQuery($storeId, 0, 1, -1, '', '');

            // 获取商品数据
            $goodsData = $this->getGoodsData($goodsQuery, $specType);

            if (empty($goodsData)) {
                return $this->error('该门店没有商品数据');
            }

            // 根据格式导出
            switch ($format) {
                case 'json':
                    return $this->exportAsJson($goodsData, count($goodsData));
                case 'excel':
                    return $this->exportAsExcel($goodsData, count($goodsData));
                case 'csv':
                default:
                    return $this->exportAsCsv($goodsData, count($goodsData));
            }

        } catch (\Throwable $e) {
            $this->debugLog('error', '导出门店商品数据失败: ' . $e->getMessage());
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出独立商品数据（sam_goods表）
     *
     * @param Request $request
     * @return Response
     */
    public function exportIndependentGoods(Request $request)
    {
        try {
            $format = $request->param('format', 'csv', 'trim');
            $storeId = $request->param('store_id', 0, 'intval');
            $categoryId = $request->param('category_id', 0, 'intval');
            $status = $request->param('status', 1, 'intval');
            $limit = $request->param('limit', 0, 'intval');
            $specType = $request->param('spec_type', 'all', 'trim'); // 规格筛选：all全部，multi多规格，single单规格

            // 构建查询条件
            $query = $this->buildIndependentGoodsQuery($storeId, $categoryId, $status);

            // 应用数量限制
            if ($limit > 0) {
                $query->limit($limit);
            }

            // 获取商品数据
            $goodsData = $this->getIndependentGoodsData($query, $storeId, $specType);

            if (empty($goodsData)) {
                return $this->error('没有找到符合条件的独立商品数据');
            }

            // 根据格式导出
            switch ($format) {
                case 'json':
                    return $this->exportIndependentGoodsAsJson($goodsData);
                case 'excel':
                    return $this->exportIndependentGoodsAsExcel($goodsData);
                case 'csv':
                default:
                    return $this->exportIndependentGoodsAsCsv($goodsData);
            }

        } catch (\Throwable $e) {
            $this->debugLog('error', '导出独立商品数据失败: ' . $e->getMessage());
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 构建独立商品查询条件（支持门店筛选）
     *
     * @param int $storeId
     * @param int $categoryId
     * @param int $status
     * @return \think\db\Query
     */
    protected function buildIndependentGoodsQuery($storeId, $categoryId, $status)
    {
        if ($storeId > 0) {
            // 如果指定了门店，需要关联门店商品表来筛选该门店有售的商品
            $query = Goods::alias('g')
                ->join('sam_store_goods sg', 'g.spuId = sg.sam_goods_id')
                ->where('sg.sam_store_id', $storeId)
                ->where('g.status', $status)
                ->group('g.spuId'); // 去重，因为一个商品可能在同一门店有多条记录
        } else {
            // 如果没有指定门店，直接查询商品表
            $query = Goods::where('status', $status);
        }

        // 分类筛选
        if ($categoryId > 0) {
            if ($storeId > 0) {
                // 关联查询时使用表别名
                $query->where(function($q) use ($categoryId) {
                    $q->whereOr('g.catLv1', $categoryId)
                      ->whereOr('g.catLv2', $categoryId)
                      ->whereOr('g.catLv3', $categoryId);
                });
            } else {
                // 单表查询时不使用表别名
                $query->where(function($q) use ($categoryId) {
                    $q->whereOr('catLv1', $categoryId)
                      ->whereOr('catLv2', $categoryId)
                      ->whereOr('catLv3', $categoryId);
                });
            }
        }

        return $query;
    }

    /**
     * 获取独立商品数据并补充分类信息
     *
     * @param \think\db\Query $query
     * @param int $storeId 门店ID，0表示查询所有门店
     * @param string $specType 规格筛选：all全部，multi多规格，single单规格
     * @return array
     */
    protected function getIndependentGoodsData($query, $storeId = 0, $specType = 'all')
    {
        // 检查是否是关联查询（包含门店筛选）
        $sql = $query->buildSql();
        $isJoinQuery = strpos($sql, 'JOIN') !== false;

        if ($isJoinQuery) {
            // 关联查询时需要指定表别名
            $goods = $query->field([
                'g.spuId',
                'g.detail',
                'g.catLv1',
                'g.catLv2',
                'g.catLv3'
            ])->select()->toArray();
        } else {
            // 单表查询
            $goods = $query->field([
                'spuId',
                'detail',
                'catLv1',
                'catLv2',
                'catLv3'
            ])->select()->toArray();
        }

        if (empty($goods)) {
            return [];
        }

        // 获取商品在门店中的最高价格和最高库存
        $spuIds = array_column($goods, 'spuId');
        $storeGoodsData = $this->getMaxPriceAndStock($spuIds, $storeId);

        // 规格筛选处理
        if ($specType !== 'all') {
            $filteredGoods = [];
            foreach ($goods as $item) {
                // 解析detail字段获取spuSpecInfo
                $detail = $this->parseDetailField($item['detail']);
                $spuSpecInfo = $detail['spuSpecInfo'] ?? null;

                // 判断是否符合规格筛选条件
                $isMultiSpec = !empty($spuSpecInfo) && is_array($spuSpecInfo);

                if ($specType === 'multi' && $isMultiSpec) {
                    // 导出多规格商品
                    $filteredGoods[] = $item;
                } elseif ($specType === 'single' && !$isMultiSpec) {
                    // 导出单规格商品
                    $filteredGoods[] = $item;
                }
            }
            $goods = $filteredGoods;
        }

        // 补充数据
        foreach ($goods as &$item) {
            // 解析detail字段
            $detail = $this->parseDetailField($item['detail']);

            // 将detail里面的数据放到最顶层
            if (is_array($detail)) {
                foreach ($detail as $key => $value) {
                    if($key == 'storeId' and $storeId > 0){
                        $item[$key] = $storeId;
                    }else{
                        $item[$key] = $value;
                    }

                }
            }

            // 移除原来的detail字段
            unset($item['detail']);
            // 如果 存在 spuSpecInfo 需要从数据库中获取对应的条形码 添加到spuSpecInfo
            if (isset($item['spuSpecInfo']) && is_array($item['spuSpecInfo'])) {
                foreach ($item['spuSpecInfo'] as &$spec) {
                    $spuId = $spec['spuId'];
                    // 需要使用 $spuId 在商品表中 获取hostUpc 字段内容
                    $hostUpc = Goods::where('spuId', $spuId)->value('hostUpc');
                    $spec['upc'] = $this->extractUpc($hostUpc);
                    $spuSpecInfo = $this->getMaxPriceAndStock([$spuId], $storeId);

                    $spec['price'] = $spuSpecInfo[$spuId]['max_price'] ?? 0;
                    $spec['stock'] = $spuSpecInfo[$spuId]['max_stock'] ?? 0;

                }
            }

            // 添加额外字段到最外层
            $spuId = $item['spuId'];
            $categoryId = ',' . $item['catLv1'] . ',' . $item['catLv2'] . ',' . $item['catLv3'] . ',';

            $item['categoryId'] = $categoryId;
            $item['category_json'] = [(string)$item['catLv1'], (string)$item['catLv2'], (string)$item['catLv3']];
            $item['price'] = $storeGoodsData[$spuId]['max_price'] ?? 0;
            $item['stock'] = $storeGoodsData[$spuId]['max_stock'] ?? 0;

            // 移除分类字段，因为已经有categoryId了
            unset($item['catLv1'], $item['catLv2'], $item['catLv3']);
        }

        return $goods;
    }

    /**
     * 解析detail字段数据
     *
     * @param string $detail
     * @return array
     */
    protected function parseDetailField($detail)
    {
        if (empty($detail)) {
            return [];
        }

        // 如果是JSON字符串，解析它
        if (is_string($detail)) {
            $parsed = json_decode($detail, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $parsed;
            }
        }

        // 如果已经是数组，直接返回
        if (is_array($detail)) {
            return $detail;
        }

        return [];
    }

    /**
     * 获取商品在门店中的最高价格和最高库存
     *
     * @param array $spuIds
     * @param int $storeId 门店ID，0表示查询所有门店
     * @return array
     */
    protected function getMaxPriceAndStock($spuIds, $storeId = 0)
    {
        if (empty($spuIds)) {
            return [];
        }

        $result = [];

        // 构建查询
        $query = StoreGoods::whereIn('sam_goods_id', $spuIds);

        // 先默认查询最高价格和最高库存
        $storeId = 0;
        // 如果指定了门店，只查询该门店的数据
        if ($storeId > 0) {
            $query->where('sam_store_id', $storeId);
            // 指定门店时，直接获取价格和库存（不需要MAX）
            $storeGoods = $query->field([
                'sam_goods_id',
                'price',
                'stock'
            ])->select()->toArray();

            foreach ($storeGoods as $item) {
                $result[$item['sam_goods_id']] = [
                    'max_price' => $item['price'],
                    'max_stock' => $item['stock']
                ];
            }
        } else {
            // 未指定门店时，查询每个商品在所有门店中的最高价格和最高库存
            $storeGoods = $query->field([
                'sam_goods_id',
                'MAX(price) as max_price',
                'MAX(stock) as max_stock'
            ])
            ->group('sam_goods_id')
            ->select()
            ->toArray();

            foreach ($storeGoods as $item) {
                $result[$item['sam_goods_id']] = [
                    'max_price' => $item['max_price'],
                    'max_stock' => $item['max_stock']
                ];
            }
        }

        return $result;
    }

    /**
     * 导出独立商品为CSV格式
     */
    protected function exportIndependentGoodsAsCsv($goodsData)
    {
        $csv = "\xEF\xBB\xBF"; // UTF-8 BOM
        $csv .= "商品完整数据\n";

        foreach ($goodsData as $item) {
            $row = [
                json_encode($item, JSON_UNESCAPED_UNICODE)
            ];

            $row = array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row);

            $csv .= implode(',', $row) . "\n";
        }

        $filename = '山姆独立商品数据导出_' . date('YmdHis') . '.csv';

        return response($csv, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0'
        ]);
    }

    /**
     * 导出独立商品为JSON格式
     */
    protected function exportIndependentGoodsAsJson($goodsData)
    {
        $data = [
            'export_info' => [
                'export_time' => date('Y-m-d H:i:s'),
                'export_count' => count($goodsData),
                'format' => 'json',
                'type' => 'independent_goods'
            ],
            'goods_data' => $goodsData
        ];

        $json = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $filename = '山姆独立商品数据导出_' . date('YmdHis') . '.json';

        return response($json, 200, [
            'Content-Type' => 'application/json; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0'
        ]);
    }

    /**
     * 导出独立商品为Excel格式
     */
    protected function exportIndependentGoodsAsExcel($goodsData)
    {
        $csv = "\xEF\xBB\xBF"; // UTF-8 BOM
        $csv .= "商品完整数据\n";

        foreach ($goodsData as $item) {
            $row = [
                json_encode($item, JSON_UNESCAPED_UNICODE)
            ];

            $row = array_map(function($field) {
                return str_replace(["\t", "\n", "\r"], [' ', ' ', ' '], $field);
            }, $row);

            $csv .= implode("\t", $row) . "\n";
        }

        $filename = '山姆独立商品数据导出_' . date('YmdHis') . '.xls';

        return response($csv, 200, [
            'Content-Type' => 'application/vnd.ms-excel; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0'
        ]);
    }
}
