<?php

namespace app\api\controller\Sams;

use app\common\model\sam\Token;
use think\facade\Log;
use think\Request;
use think\Response;

/**
 * 山姆Token管理类
 */
class SamsTokenManager extends SamsBase
{
    /**
     * 重置非今日更新的token使用次数为0
     * 只处理状态正常的token
     * 
     * @param Request $request
     * @return Response
     */
    public function resetTokenUsageCount(Request $request)
    {
        try {
            // 获取今天的开始时间戳
            $todayStart = strtotime(date('Y-m-d 00:00:00'));
            
            // 查询状态正常且更新时间不是今天的token
            $affectedRows = Token::where('status', 1)
                ->where('update_time', '<', $todayStart)
                ->update(['usage_count' => 0]);
            
            $this->debugLog('info', "重置Token使用次数完成，影响行数: {$affectedRows}");
            
            return $this->success('重置Token使用次数成功', [
                'affected_rows' => $affectedRows,
                'reset_time' => date('Y-m-d H:i:s'),
                'today_start' => date('Y-m-d H:i:s', $todayStart)
            ]);
            
        } catch (\Throwable $e) {
            $this->debugLog('error', '重置Token使用次数失败: ' . $e->getMessage());
            return $this->error('重置失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 添加新的手机号和token
     *
     * @param Request $request
     * @return Response
     */
    public function addToken(Request $request)
    {
        try {
            // 获取参数
            $phone = $request->param('phone', '', 'trim');
            $token = $request->param('token', '', 'trim');
            $status = $request->param('status', 1, 'intval'); // 默认状态为1（正常）

            // 验证参数
            if (empty($phone)) {
                return $this->error('手机号不能为空');
            }

            if (empty($token)) {
                return $this->error('Token不能为空');
            }



            // 检查手机号是否已存在
            $existingPhone = Token::where('phone', $phone)->find();
            if ($existingPhone) {
                return $this->error('该手机号已存在');
            }

            // 检查token是否已存在
            $existingToken = Token::where('token', $token)->find();
            if ($existingToken) {
                return $this->error('该Token已存在');
            }

            // 创建新的token记录
            $tokenModel = new Token();
            $tokenModel->phone = $phone;
            $tokenModel->token = $token;
            $tokenModel->status = $status;
            $tokenModel->usage_count = 0; // 初始使用次数为0

            $result = $tokenModel->save();

            if ($result) {
                Log::info("新增Token成功: 手机号={$phone}, Token=" . substr($token, 0, 20) . '...');

                return $this->success('添加Token成功', [
                    'id' => $tokenModel->id,
                    'phone' => $phone,
                    'token_preview' => substr($token, 0, 20) . '...',
                    'status' => $status,
                    'usage_count' => 0,
                    'create_time' => $tokenModel->create_time
                ]);
            } else {
                return $this->error('添加Token失败');
            }

        } catch (\Throwable $e) {
            Log::error('添加Token失败: ' . $e->getMessage());
            return $this->error('添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量添加Token
     * 格式：手机号----token，每行一个
     *
     * @param Request $request
     * @return Response
     */
    public function batchAddTokens(Request $request)
    {
        try {
            // 获取批量数据
            $batchData = $request->param('batch_data', '', 'trim');
            $status = $request->param('status', 1, 'intval'); // 默认状态为1（正常）

            if (empty($batchData)) {
                return $this->error('批量数据不能为空');
            }

            // 按行分割数据
            $lines = array_filter(array_map('trim', explode("\n", $batchData)));

            if (empty($lines)) {
                return $this->error('没有有效的数据行');
            }

            $successCount = 0;
            $failedCount = 0;
            $results = [];
            $errors = [];

            foreach ($lines as $lineIndex => $line) {
                $lineNumber = $lineIndex + 1;

                // 检查格式：手机号----token
                if (!str_contains($line, '----')) {
                    $errors[] = "第{$lineNumber}行格式错误：缺少分隔符'----'";
                    $failedCount++;
                    continue;
                }

                $parts = explode('----', $line, 2);
                if (count($parts) !== 2) {
                    $errors[] = "第{$lineNumber}行格式错误：分隔符使用不正确";
                    $failedCount++;
                    continue;
                }

                $phone = trim($parts[0]);
                $token = trim($parts[1]);



                if (empty($token)) {
                    $errors[] = "第{$lineNumber}行Token不能为空";
                    $failedCount++;
                    continue;
                }

                // 检查手机号是否已存在
                $existingPhone = Token::where('phone', $phone)->find();
                if ($existingPhone) {
                    $errors[] = "第{$lineNumber}行手机号已存在：{$phone}";
                    $failedCount++;
                    continue;
                }

                // 检查token是否已存在
                $existingToken = Token::where('token', $token)->find();
                if ($existingToken) {
                    $errors[] = "第{$lineNumber}行Token已存在：" . substr($token, 0, 20) . '...';
                    $failedCount++;
                    continue;
                }

                try {
                    // 创建新的token记录
                    $tokenModel = new Token();
                    $tokenModel->phone = $phone;
                    $tokenModel->token = $token;
                    $tokenModel->status = $status;
                    $tokenModel->usage_count = 0;

                    $result = $tokenModel->save();

                    if ($result) {
                        $successCount++;
                        $results[] = [
                            'line' => $lineNumber,
                            'phone' => $phone,
                            'token_preview' => substr($token, 0, 20) . '...',
                            'status' => 'success'
                        ];

                        Log::info("批量新增Token成功: 第{$lineNumber}行, 手机号={$phone}");
                    } else {
                        $errors[] = "第{$lineNumber}行保存失败：{$phone}";
                        $failedCount++;
                    }
                } catch (\Throwable $e) {
                    $errors[] = "第{$lineNumber}行处理异常：{$phone} - " . $e->getMessage();
                    $failedCount++;
                }
            }

            $message = "批量添加完成：成功{$successCount}个，失败{$failedCount}个";

            return $this->success($message, [
                'total_lines' => count($lines),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'results' => $results,
                'errors' => $errors
            ]);

        } catch (\Throwable $e) {
            Log::error('批量添加Token失败: ' . $e->getMessage());
            return $this->error('批量添加失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取Token列表
     * 
     * @param Request $request
     * @return Response
     */
    public function getTokenList(Request $request)
    {
        try {
            $page = $request->param('page', 1, 'intval');
            $pageSize = $request->param('page_size', 20, 'intval');
            $status = $request->param('status', '', 'trim'); // 状态筛选
            
            $query = Token::order('create_time', 'desc');
            
            // 状态筛选
            if ($status !== '') {
                $query->where('status', $status);
            }
            
            $tokens = $query->paginate([
                'list_rows' => $pageSize,
                'page' => $page
            ]);
            
            // 处理返回数据，隐藏完整token
            $tokenList = [];
            foreach ($tokens->items() as $token) {
                $tokenList[] = [
                    'id' => $token->id,
                    'phone' => $token->phone,
                    'token_preview' => substr($token->token, 0, 20) . '...',
                    'status' => $token->status,
                    'status_text' => $token->status == 1 ? '正常' : '禁用',
                    'usage_count' => $token->usage_count,
                    'create_time' => $token->create_time,
                    'update_time' => $token->update_time
                ];
            }
            
            return $this->success('获取成功', [
                'list' => $tokenList,
                'total' => $tokens->total(),
                'page' => $page,
                'page_size' => $pageSize
            ]);
            
        } catch (\Throwable $e) {
            Log::error('获取Token列表失败: ' . $e->getMessage());
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新Token状态
     * 
     * @param Request $request
     * @return Response
     */
    public function updateTokenStatus(Request $request)
    {
        try {
            $id = $request->param('id', 0, 'intval');
            $status = $request->param('status', 1, 'intval');
            
            if ($id <= 0) {
                return $this->error('Token ID不能为空');
            }
            
            $token = Token::find($id);
            if (!$token) {
                return $this->error('Token不存在');
            }
            
            $token->status = $status;
            $result = $token->save();
            
            if ($result) {
                Log::info("更新Token状态成功: ID={$id}, 状态={$status}");
                
                return $this->success('更新状态成功', [
                    'id' => $id,
                    'status' => $status,
                    'status_text' => $status == 1 ? '正常' : '禁用'
                ]);
            } else {
                return $this->error('更新状态失败');
            }
            
        } catch (\Throwable $e) {
            Log::error('更新Token状态失败: ' . $e->getMessage());
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 删除Token
     * 
     * @param Request $request
     * @return Response
     */
    public function deleteToken(Request $request)
    {
        try {
            $id = $request->param('id', 0, 'intval');
            
            if ($id <= 0) {
                return $this->error('Token ID不能为空');
            }
            
            $token = Token::find($id);
            if (!$token) {
                return $this->error('Token不存在');
            }
            
            $result = $token->delete();
            
            if ($result) {
                Log::info("删除Token成功: ID={$id}, 手机号={$token->phone}");
                
                return $this->success('删除Token成功');
            } else {
                return $this->error('删除Token失败');
            }
            
        } catch (\Throwable $e) {
            Log::error('删除Token失败: ' . $e->getMessage());
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取Token统计信息
     * 
     * @param Request $request
     * @return Response
     */
    public function getTokenStatistics(Request $request)
    {
        try {
            $statistics = [
                'total_tokens' => Token::count(),
                'active_tokens' => Token::where('status', 1)->count(),
                'disabled_tokens' => Token::where('status', 0)->count(),
                'high_usage_tokens' => Token::where('usage_count', '>', 1000)->count(),
                'today_updated_tokens' => Token::where('update_time', '>=', strtotime(date('Y-m-d 00:00:00')))->count(),
                'avg_usage_count' => round(Token::where('status', 1)->avg('usage_count'), 2)
            ];
            
            return $this->success('获取统计信息成功', $statistics);
            
        } catch (\Throwable $e) {
            Log::error('获取Token统计信息失败: ' . $e->getMessage());
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }
}
