<?php

namespace app\api\controller;

use think\Request;
class Api
{
    private Request $request;
    private \app\common\model\sam\Token $sam_token;
    private \app\common\model\sam\store\Goods $sam_store_goods;

    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->sam_token = new \app\common\model\sam\Token();
        $this->sam_store_goods = new \app\common\model\sam\store\Goods();
    }


    public function sam_add()
    {
        $phone = $this->request->param('phone');
        $token = $this->request->param('token');
        if (!$phone || !$token) {
            return json(['code'=>400,'msg'=>'参数错误']);
        }
        if ($this->sam_token->where('phone',$phone)->find()) {
            $this->sam_token->where('phone',$phone)->delete();
        }
        try {
            $this->sam_token->insert(['phone'=>$phone,'token'=>$token,'create_time'=>time()]);
        }
        catch (\Exception $e) {
            return json(['code'=>400,'msg'=>'添加失败']);
        }
        return json(['code'=>200,'msg'=>'添加成功']);
    }


    public function get_store_goods(){
        $store_id = $this->request->param('store_id');
        // 改成获取指定字段的 sam_goods_id price stock
        $products = $this->sam_store_goods->where('sam_store_id', $store_id)->column('sam_goods_id,price,stock');
        // 去除
        return json(['code'=>200,'msg'=>'获取成功','data'=>$products]);
    }



}