<?php

namespace app\common\model\sam\store;

use think\Model;

/**
 * Goods
 */
class Goods extends Model
{
    // 表名
    protected $name = 'sam_store_goods';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;


    public function samStore(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\sam\Store::class, 'sam_store_id', 'storeId');
    }

    public function samGoods(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\sam\Goods::class, 'sam_goods_id', 'spuId');
    }
}