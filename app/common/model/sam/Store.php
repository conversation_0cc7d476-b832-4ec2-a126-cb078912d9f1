<?php

namespace app\common\model\sam;

use think\Model;
use app\common\model\sam\store\Goods as StoreGoods;

/**
 * Store
 */
class Store extends Model
{
    // 表名
    protected $name = 'sam_store';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;
    protected $updateTime = false;

    /**
     * 关联门店商品
     */
    public function storeGoods()
    {
        return $this->hasMany(StoreGoods::class, 'sam_store_id', 'storeId');
    }
}