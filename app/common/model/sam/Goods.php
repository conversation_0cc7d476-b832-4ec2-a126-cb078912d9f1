<?php

namespace app\common\model\sam;

use think\Model;

/**
 * Goods
 */
class Goods extends Model
{
    // 表名
    protected $name = 'sam_goods';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'hostUpc' => 'json',
    ];


    public function getHostUpcAttr($value): array
    {
        return !$value ? [] : json_decode($value, true);
    }

    public function catLv1Table(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\sam\Categories::class, 'catLv1', 'groupingId');
    }

    public function catLv2Table(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\sam\Categories::class, 'catLv2', 'groupingId');
    }

    public function catLv3Table(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\sam\Categories::class, 'catLv3', 'groupingId');
    }
}