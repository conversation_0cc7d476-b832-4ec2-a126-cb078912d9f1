### [BuildAdmin 更新日志](https://gitee.com/wonderful-code/buildadmin)

🔥🔥基于 Vue3.x setup + ThinkPHP8 + TypeScript + Vite + Pinia + Element Plus等流行技术栈的后台管理系统，自适应多端、支持CRUD代码生成、自带WEB终端、同时提供Web和Server端、内置全局数据回收站和字段级数据修改保护、自动注册路由、无限子级权限管理等，无需授权即可免费商用，希望能帮助大家实现快速开发。

## v2.1.3-Release
### 新增
- 后台控制器基类增加有序保证属性
- 存在热更新脏文件时，于后台顶栏显示需要重启 `Vite` 热更新服务的警告按钮

### 重构/优化/修复
- 表格拖拽排序由直接替换改为增量重排法
- 优化点选验证码组件的渲染与销毁
- 优化会员中心菜单点击时的处理函数逻辑
- 优化前台顶栏菜单被点击时无需激活的菜单项的处理
- 优化终端弹窗样式
- 优化小屏下的菜单抽屉
- 优化顶栏子级菜单的激活逻辑
- 优化系统配置项 `buildadmin.api_url` 的注释
- 优化 `CRUD` 无意义默认值的清理逻辑
- 优化 `CRUD` 浮点数类型字段的模型获取器生成
- 去除开发环境下跨域代理示例
- 前端的会员中心开关状态默认开启
- 会员前台菜单的路由路径支持 `query`
- 后台删除操作不再必需为 `Delete` 请求、统一输入变量接受方式
- 完善 `Request` 类的真实 `IP` 获取支持（可避免 `Nuxt` 工程服务端渲染时获取不到客户端真实 `IP`）
- 修改 `request` 类全局过滤规则的设置时机
- 修复单元格 `tag` 渲染器值为 `0` 时不渲染的问题
- 修复控制器代码中未写入自定义的权重字段名称的问题
- 修复会员的分组无所有权限时上传文件会提示无权限的问题
- 修复模块安装过程中可能提示网络请求超时的问题
- 其他细节

## v2.1.2-Release
### 新增
- 增加前台会员登录验证码开关配置项
- `BaInput` 和 `FormItem` 组件增加插槽支持
- 可视化CRUD将字段默认值区分为多个类型进行设定
- 可视化CRUD修改字段的生成类型时，询问是否重置为新类型的预设属性
- 终端设置窗口内增加修改 `NPM` 和 `Composer` 源的功能

### 重构/优化/修复
- 升级 `think-orm`
- 提高 `node` 版本要求
- 优化根标签和头像样式
- 优化通用搜索表单重置逻辑
- 优化前台会员登录注册接口
- 优化开发服务环境检测，去除开发服务端口配置功能
- 优化输入组件类型对应的数据表字段设计方案
- 后台菜单的路由路径支持 `query`
- `baTable` 的通用搜索初始化相关逻辑解耦
- 删除 `countup.js` 依赖，使用 `useTransition` 代替
- 设置通用搜索数据时对时间日期的识别优化
- 表格列的 `renderFormatter` 替换为 `formatter`
- 单元格渲染器拆分为独立组件并改用易于扩展的方式加载
- 使用 `v-memo` 指令缓存表格中的按钮组以提高表格性能
- 会员管理控制器的 `select` 方法过滤敏感字段
- 附件选择器关闭通过 `query` 自动触发通用搜索的功能
- 升级 `pinia`、`eslint`、`vue-tsc` 等多个前端依赖
- 使用 `qrcode.vue` 替代 `vue-qr` 以确保不存在已弃用的子依赖
- 安装程序增加对 `pdo_mysql` 的检测
- 修复控制台页面部分图表超出容器的问题
- 修复上传文件时入库文件路径使用了错误的斜杠造成图片可能无法显示的问题
- 修复在多数据库场景中远程下拉组件的 `pk` 属性可能错误的问题
- 修复系统配置中部分输入组件可能报警告的问题
- 其他细节

## v2.1.1-Release
### 新增
- 上传类重构为多驱动模式，同时云存储模块将为系统安装服务端上传云存储的驱动

### 重构/优化/修复
- 限定`think-orm`版本以修复该依赖新版本带来的问题
- 修复上传组件的文件上传状态可能错误的问题
- 修复自定义后台入口后`WEB`终端命令执行失败的问题
- 修复`v-drag`指令对`el-dialog`使用时会意外抖动的问题
- 修复会员的权限不是所有时，修改邮箱提示没有权限的问题
- 修复一处类型定义中的注释书写错误 !163
- 优化`full_url`函数的参数类型
- 当表格公共搜索字段渲染为`tag`且操作为`in`时，支持多选 !162
- 上传组件禁用状态相关优化
- 调高部分全局通知消息的`zIndex`
- 使用更合理的方案处理`OPTIONS`请求

## v2.1.0-Release
### 新增
- 升级所有前端依赖
- 增加`Writeable`工具类型，可将只读属性转为可写
- 增加`vue-tsc`依赖和`typecheck`命令
- 模块上传安装时对模块版本是否兼容当前系统版本进行检查

### 重构/优化/修复
- 修复`FormItem`组件属性失去了响应性的问题
- 修复`switch`组件使用了`activeValue`属性时无法工作的问题
- 修复连续安装模块时终端命令可能执行失败的问题
- 修复拥有所有权限的分组可能无法默认选中权限节点的问题
- 基于`Element plus`新版本优化远程下拉组件（分页样式、逻辑等）
- 基于`Element plus`新版本优化文件上传组件（钩子等）
- 基于`Element plus`新版本优化`FormItem`组件
- 优化`FormItem`的`props.tip`和`props.blockHelp`样式
- 优化`FormItem`组件的类型定义
- 合并`FormItem`的`props`本身和`props.attr`，可查阅[此提交](https://gitee.com/wonderful-code/buildadmin/commit/10527ebb760a10b329130e3194a6bbc929a52737)
- 合并`baInput`组件的`props.attr`和`props.data`，可查阅[此提交](https://gitee.com/wonderful-code/buildadmin/commit/10527ebb760a10b329130e3194a6bbc929a52737)
- 优化表格的`props`继承的类型的定义
- 优化富文本编辑器的默认宽度
- 优化可视化`CRUD`生成的模型的`onBeforeInsert`方法返回值类型定义
- 优化可视化`CRUD`生成的`FormItem`组件的属性代码
- 优化`debounce`的使用，无必要不使用全局`debounce`
- 去除菜单权限规则类的静态变量缓存以更好的兼容常驻内存运行
- 使用`PSR-12`编码风格规范格式化和检查所有`PHP`代码
- `Vite`热更新控制的相关功能整理为函数
- 其他细节

## v2.0.10-Release
### 新增
- 单复选框支持按钮模式
- 公共搜索增加渲染类名方便单独定位某字段
- 图片上传达到限制数时隐藏上传框的配置项
- 增加`AttachmentInsert`事件，开发者或模块可于附件入库后对新的附件做一些额外的操作

### 重构/优化/修复
- 优化文件上传，文件移动操作优先于文件数据入库
- 优化管理员管理和菜单规则管理的控制器代码
- 修复选择数据表、选择数据表字段接口非默认数据库返回空数据的问题
- 修复第一个菜单为 Iframe 时不能自动跳转的 BUG
- 修复由于`Gitee Pages`停止服务造成的文档站无法访问
- 修复可视化`CRUD`远程下拉选择数据表时不能关键词搜索的问题
- 修复右击菜单组件的菜单面板可能超出屏幕的问题

## v2.0.9-Release
### 新增
- 增加动态修改后台顶部`Tab`标题的方法
- 增加关闭掉全部或者指定`Tab`的方法
- 新增`refs`状态商店，全局提供了：引用（指向）一些对象（组件）的句柄
- 常驻内存支持，享受比传统框架快上数十倍的性能提升，目前[Workerman模块](https://modules.buildadmin.com/workerman)可提供框架的常驻内存HTTP服务

### 重构/优化/修复
- 优化内置滚动条样式
- 使用鼠标滚轮快速操作顶栏横向滚动条的支持（单双栏布局模式下）
- 在操作日志中管理登录失败时任然尽可能记录输入的管理员名称
- 内置开发服务(php think run)总是禁用输出压缩
- 自定义后台入口时，由禁止后台应用访问改为添加应用映射
- 优化Token门面类的类型定义、鉴权相关解耦、细节优化
- 上传文件时对文件名中不利于传输的字符进行过滤
- 修复WEB终端开始执行命令时可能刷新页面的问题
- 修复清理缓存操作的日志标题错误的问题

## v2.0.8-Beta
### 新增
- [Workerman模块](https://modules.buildadmin.com/workerman)上线（系统内置的`API`已完成常驻内存运行的兼容）
- 增加一个获取用户鉴权`token`的公共函数
- WEB终端：对交互式命令进行提示并终止执行

### 重构/优化/修复
- 修复收集异常数据时可能出现循环引用导致难以排查的问题
- 公共函数`full_url`默认域名不再携带协议，由浏览器自动识别或手动指定
- 文件上传类兼容常驻内存模式运行
- 权限和权限规则类兼容常驻内存运行
- WEB终端：兼容常驻内存运行
- WEB终端：命令执行完毕后再输出退出码和状态信息
- 跨域中间件：使用更合适的办法设置跨域响应头

## v2.0.7-Release
### 重构/优化/修复
- 优化前台首页在短屏下的样式
- 可视化CRUD：多数据库支持兼容历史生成记录
- 为前台登录页增加滚动条以兼容小屏设备

## v2.0.6-Release
### 新增
- 自定义后台入口支持
- CRUD、数据回收、敏感数据监控的多数据库支持

### 重构/优化/修复
- 修复编辑时`unique`验证问题
- 添加`symfony/http-foundation`依赖
- 修复非超管对于新增的子级菜单规则可能显示异常的问题
- 修复保存系统配置时可能刷新页面的问题
- 选择数据表、选择数据源接口增加快速搜索支持
- 优化`git`对模块文件的忽略规则
- 更换已经失效的`npm`源
- 优化后台规则管理细节
- 安装器优化

## v2.0.5-Release
### 新增
- 更新所有前端依赖`Vite5+ElementPlus2.4`
- 模块更新`composer.json`中的`config`字段实现

### 重构/优化/修复
- 优化前台页脚样式
- 优化路由动态注册
- 优化获取第一个菜单的函数
- 优化通用搜索按钮样式 !142
- 优化系统配置数据模型
- 优化可视化`CRUD`设计器的样式
- 后台会员管理中的会员分组设为必填
- 默认不再内置`easywechat`依赖，添加`guzzlehttp`依赖（受益于模块可以调整`composer.json`的`config`）
- 系统配置中的快捷配置入口使用路由`name`而不再是路由`path`
- 语言包按需加载映射表中的后台入口路径由字面量改为变量
- 管理员登录接口返回的路由路径使用的字面量改为变量
- `eslint`和`prettier`与`ESM`的兼容
- 修复系统配置变量值为`0`时可能无法回显的问题
- 修复可视化`CRUD`富文本字段默认值为`null`时表单打不开的问题
- 修复设置浏览器标题的函数可能失败的问题
- 修复初次打开前端时页面标题不完整的问题
- 修复双栏模式子级菜单跳转异常的问题 close #I7ZECR
- 其他细节

## v2.0.4-Release
### 新增
- 增加静态路由目录，自动加载其中所有文件并注册
- 表格快速搜索关键词可通过类实例访问
- 模块上传安装时对系统版本、模块互斥和依赖关系进行检测
- 模块纯净模式安装（移动模块文件到系统而不是复制）
- 模块可以通过上传安装来完成升级
- 自定义远程下拉初始值操作符号支持

### 重构/优化/修复
- 可视化CRUD:生成的菜单默认开启缓存
- 可视化CRUD:高级配置中显示的字段信息优化
- 可视化CRUD:非新建设计时，总是显示表设计变更预览的按钮
- 可视化CRUD:优化字段临时数据备份机制、优化字段重复检测
- 终端不再使用单独的控制器
- 终端优化命令执行日志缓冲区清理逻辑
- 终端执行`composer`相关命令时，关闭交互询问
- 表单弹窗在小屏设备上的样式优化
- 优化创建`zip`的方法
- 表格数据刷新优化
- 日志数据入库时的编码兼容性优化
- 基础静态路由路径使用的字面量改为变量
- 生成代码的`import`语句整理
- 去掉管理员登录成功时的通知提醒信息
- 优化模块上传安装时的提示信息
- WEB端环境变量加载优化
- 升级`topthink/think-migration`依赖
- 去除已经失效的`travis.yml`文件
- 删除`web`目录内多余的`README.md`文件
- 修复表格自动识别筛选条件功能中，`query`改变不能触发重新筛选的问题
- 修复`nuxt工程`新增依赖时没有备份`package.json`的问题

## v2.0.3-Release
### 新增
- 前台菜单支持无限层级嵌套
- 独立出表格内部组件自动调用的鉴权方法，便于开发者重写
- 前端公共函数`auth`可以通过传递菜单规则的 name 鉴权
- 删除 web 端中多余的默认头像文件
- 会员中心增加可选的 query 指定会员登录成功后自动跳转的URL
- 上传类增加一个`setTopic`方法

### 重构/优化/修复
- 优化控制台菜单规则（增加了查看权限节点）
- 公共函数`get_table_list`默认不再去除表注释中的：后缀`表`字
- 管理员管理中的分组字样改为角色组
- 角色组管理增加权限说明的备注
- 取消前台用户头像必填
- 重置公共搜索表单时，自动刷新表格
- 内置的后台功能中默认ID字段搜索时不再使用模糊查询
- 不再需要清理`css charset`，所以删除多余代码
- 去除多余的 htmlspecialchars 参数（富文本入库可能被多次转义）
- 修复顶栏菜单在非激活菜单右击关闭全部标签会清空标签页的问题
- 修复头像保存时可能丢失的问题，优化头像URL出入库逻辑
- 修复用户默认的头像URL可能被入库的问题
- 修复上传组件图片拖拽排序在添加时无效的问题
- 修复文件名为中文时可能上传失败的问题
- 可视化CRUD:修复生成单选框组件时报错的问题

## v2.0.2-Release
### 新增
- 上传图片组和文件组时支持拖拽排序
- 增加管理员和会员的登录态保持时间配置项
- 新增清理`XSS`代码的公共函数

### 重构/优化/修复
- 从服务端限制`WEB终端`仅限超管执行命令
- 表格公共搜索操作符不再使用不利于传输的符号形式
- 为`a`标签添加`rel="noopener noreferrer"`
- 优化请求输入变量的默认过滤规则
- 可视化CRUD:存在富文本组件时，默认对`XSS`代码进行清理
- 可视化CRUD:远程下拉参数预填弹窗增加滚动条，避免小屏显示异常
- 可视化CRUD:富文本字段默认值改为`empty string`
- 可视化CRUD:修复`php8.1`下从数据表开始可能报错的问题
- 修复远程下拉脱焦后会有个多余的请求的问题
- 修复远程下拉组件`row`事件可能失效的问题
- 修复会员登录态过期后不会触发重新登录的问题
- 修复小屏设备中后台最后一个菜单可能显示不全的问题
- 修复顶栏会员中心菜单的下拉项无法显示的问题

## v2.0.1-Release
### 新增
- 全局提供 mainScrollbarRef 以实现子组件操作滚动条

### 重构/优化/修复
- 前端初始化请求和会员中心初始化请求合并为一个
- 顶栏菜单在手机端的显示和交互优化
- 优化会员中心个人资料页面小屏自适应
- 会员余额和会员积分模型添加悲观锁
- 删除 user 表中可能造成意外错误的唯一索引
- 添加页面按钮时，隐藏链接地址的输入框
- 远程下拉组件内部 select 属性绑定
- 提交表单时操作方法名首字母小写
- 修复安装模块时可能报异常的问题
- 修复 symfony/var-dumper 依赖被锁定为 4.*
- 修复顶栏宽度在侧边栏折叠开启操作后宽度不正确
- 修复模块安装、卸载等操作时管理员日志中标题为未知的问题
- 修复管理员登录页不能响应暗黑模式开关的问题
- 修复后台侧边菜单在小屏设备可能意外被隐藏的问题
- 修复不能同时存在两个地区选择器的问题
- 修复 el-table 原有属性失效的问题
- 可视化CRUD：修复远程下拉多选字段后缀不为 _ids 时会生成重复方法的问题
- 可视化CRUD：修复 enum 默认值为 0 时报错的问题
- 可视化CRUD：修复生成的前端代码中对象 key 以数字开头时报错的问题
- 可视化CRUD：修复编辑关联字段时可能不会更新表字段的问题
- 可视化CRUD：MySQL text 和 blob 类型不能有默认值

## v2.0.0-Release
此版本有一些不兼容更新，建议在更新前参考：[v2.0.0不兼容更新](https://doc.buildadmin.com/guide/other/incompatibleUpdate/v200.html)
### 新增
- 升级到`tp8.0.0`，升级所有后端依赖
- 升级到`vue3.3`，升级所有前端依赖
- 上传文件使用部分文件名作为前缀以便识别
- 多富文本编辑器共存支持
- 模糊搜索关键词可以含有百分号
- 表格，单元格和公共搜索自定义渲染支持`slot`方式
- 表格头组件增加多个插槽
- 使用`Phinx`管理项目数据表，增加数据表管理类
- 增加访问和操作文件系统的类
- 可视化CRUD，修改设计且数据表内有数据时，不再删表重建，而是根据设计调整表结构
- 可视化CRUD，单表多次关联支持、远程下拉字段名自动根据表名生成
- 可视化CRUD，修复生成的远程下拉`pk`属性可能错误的问题
- 可视化CRUD，实时的字段命名规则检查、字段名称重复检查
- 可视化CRUD，增加当前不在开发环境提醒
- 可视化CRUD，选择的表有成功生成的记录则建议从历史记录开始
- 其他细节

### 优化/修复
- 安装器优化
- 公共语言翻译`key`全面大写开头
- 内置`font-awesome`
- `WEB`终端执行日志显示样式优化，且输出支持换行
- 完善前端端类型定义
- 部分公共函数归纳为类
- `array`输入组件可以设置数组项的标题
- 可视化CRUD，修复远程下拉多选字段的公共搜索失效的问题
- 云存储初始化时机优化
- 修改菜单规则默认图标
- 上传组件默认值为`null`时的处理
- 修复后台基类 del 方法的数据权限失效的问题
- 编程式删除系统配置分组时，该分组无配置项再删除
- 同类函数参数命名统一、字段命名规则统一、参数命名规则统一
- 默认折叠所有会员菜单规则
- 优化点选验证码汉字集
- 优化命令执行失败时的提示信息
- 去除不必要的 controllerUrls
- `timeFormat`函数归类到公共文件中
- `menu_rule`表名改为`admin_rule`，因为会员规则表名为`user_rule`
- 修复远程下拉组件在无数据时无提醒的问题
- 修复热更新后鉴权按钮消失的问题
- 其他细节

## v1.1.7-Release
### 新增
- 升级点选验证码
- 增加数据表命名规则的检查
- 增加模块安装时对`Nuxt`工程的版本检测

### 重构/修复
- 优化将字符串属性列表转为数组的函数
- 为模块安装请求设置更长的请求超时时间
- 修改高级配置文字颜色
- 优化输入组件逻辑
- `nuxt`模块改用标签进行筛选
- 系统配置中配置分组不存在时不显示该配置项
- 修复鼠标在时间选择组件上页面无法滚动的问题 !125
- 修复下拉面板滚动到视窗外隐藏时可能抛出错误的问题 !124
- 其他细节

## v1.1.6-Release
### 新增
- 上传组件增加实时上传进度的显示
- 上传组件增加强制上传到本地的`props`
- 上传文件方法增加`AxiosRequestConfig`参数
- 增加`remoteSelects`类型输入框
- 后台会员规则管理增加顶栏会员菜单下拉项类型

### 修复/重构
- 后台关闭`tab`，自动返回到上一个`tab`时不带`query`的问题
- 后台菜单规则管理中的组件路径字段自动转换分隔符号
- 会员规则无组件的不注册到菜单项
- 优化系统配置逻辑
- 优化可视化CRUD拖拽交互
- 因`web-nuxt`提高`API`节流阈值
- 限定`pinia`版本号
- 前台初始化请求防抖
- 优化会员中心跳转到第一个菜单的逻辑
- 前台动态路由注册时可根据`name`从已注册路由分析父级路由
- 不再额外向`element`安装器传递`i18n`选项
- 优化前台动态菜单样式
- 前台`link`类型的顶栏菜单打开失败的问题
- 添加表单默认值赋值改为深拷贝
- 优化鉴权指令
- 优化输入组件用于代码提示的类型定义
- 修复上传组件`onChange`重复触发的问题
- 修复查询条件 [NOT] NULL 报错的问题
- 修复系统配置的远程下拉多选值不能正常选中的问题

## v1.1.5-Release
### 新增
- 全面使用`文字点选验证码`，配合服务端二次验证，为您的重要资源保驾护航
- 增加前台普通路由、顶部导航栏、权限节点的可视化管理
- 远程下拉增加获取被选中项完整对象的事件
- `可视化CRUD`常用字段增加`雪花ID`类型
- `可视化CRUD`生成公共模型代码的支持
- `可视化CRUD`增加快速设定代码相对位置的功能
- `可视化CRUD`根据字段字典自动重新生成字段的数据类型，避免部分情况需要手动拼接的问题
- `可视化CRUD`的字段设计数据导出以便开发者使用
- `baTableApi`当操作不存在时，创建自定义操作
- 增加创建表单项数据的组件
- 前端增加身份证号验证器
- 增加快速搜索前置插槽
- 增加将字符串属性列表转为数组的公共函数
- 增加通过`Git`部署项目至线上时的忽略规则建议
- 增加备用的`font-awesome CDN`和完善注释

### 修复/重构
- 更新所有前端依赖
- 优化类型定义
- 不再加载`lang/pages`中的语言包
- 优化系统配置保存时的代码逻辑
- 从数据表开始时不读取表前缀错误的数据表
- 远程下拉菜单超出视窗时自动隐藏
- 修复会员分组管理回车保存会刷新页面的问题
- 菜单折叠后菜单图标垂直对齐
- 公共搜索查询操作符 FIND_IN_SET 支持传递数组
- 将渲染为 tags 的字段的默认搜索操作符设定为 FIND_IN_SET
- 禁止管理员添加拥有自己全部权限的分组
- 上传函数请求超时时间修改为无限制避免超时
- CRUD下拉框默认的字段数据类型由`tinyint`改为`enum`
- 修复单元格自定义渲染时，改变 data 不重新渲染的问题
- 修复`Tree类`子节点组装方法漏传`pk`的问题
- 修复CRUD远程下拉的公共搜索无数据的问题
- 修复CRUD从数据表加载的字段默认值为null，却被识别为空字符串的问题
- 修复CRUD数字输入组件的默认值无法通过验证的问题
- 修复CRUD中支持多选的表单元素名称错误的问题
- 修复CRUD富文本字段生成失败的问题
- 修复会员管理建立模型验证后密码验证不通过的问题
- 修复php8.1+mysql8.0兼容性问题
- 修复一处 php8.2 不兼容
- 修复公共搜索未传递值时任然拼装查询条件的问题
- 其他细节优化

## v1.1.4-Release
### 新增
- 模块安装增加依赖模块检测
- 新的依赖管理类
- 通过模块市场为`WebNuxt工程`安装模块的实现

## v1.1.3-Release
### 新增
- `WebNuxt`工程发布，可通过模块市场安装，亦可直接访问[代码仓库](https://gitee.com/wonderful-code/build-admin-nuxt)
- 增加可选的管理员和会员单点登录功能
- 增加直接登录会员账号的方法
- 新增双栏布局效果，顶部栏加左侧栏同时存在
- 确保无需登录的接口不会抛出token过期的异常
- 增加表格普通侧边按钮类型
- 增加根据当前路由路径快捷获取语言翻译的函数
- 后台模块管理增加我的模块按钮

### 修复/重构
- 远程下拉增加信息提示框
- 文件上传失败则不在上传列表显示
- 调整composer依赖
- 可视化CRUD生成的语言包代码按需加载实现
- 优化数据行拖拽排序的逻辑
- 优化数据行侧边按钮的类型定义
- 模块封面图片开启懒加载
- 修改管理员日志的data字段类型为longtext
- 修复添加窗口中存在富文本字段时可能无法关闭的问题
- 修复管理员无权限时跳回首页或被注销的问题
- 修复表格行侧边 confirmButton 按钮 disabled 无效的问题
- 修复从历史记录开始时，远程下拉参数无法选择的问题
- 修复菜单规则只添加为菜单时无法打开的问题
- 修复从数据表开始时字段分析可能出错的问题
- 修复行侧边按钮 disabledTip 属性无效的问题
- 修复前台iframe菜单无法打开的问题
- 修复远程下拉监听值为`null、undefined`时报错的问题
- 修复后台因为管理员模型登录时间获取器导致登录判断报错问题

## v1.1.2-Release
- 此版本有一些不兼容更新，建议在更新前参考：[v1.1.2不兼容更新](https://doc.buildadmin.com/guide/other/incompatibleUpdate/v112.html)
- 页面组件与页面语言包全部**按需加载**，大幅减少首屏加载大小
- 更新系统前端的所有可更新依赖到最新稳定版本
- 可视化CRUD增加字段名称检查
- 禁止管理员自己删除自己
- `isAdminApp`方法支持传递`path`进行判断
- `mixins`代码移入到新建的组件内统一管理
- 修复可视化CRUD生成的代码中`-1`没有加引号的问题
- 修复后台单栏布局只有一个菜单时菜单不显示的问题
- 修复模块发布新版本不能减少旧版本模块文件的问题
- 修复模块更新脚本因未加载而不能执行的问题

## v1.1.1-Release
### 新增
- 增加前台会员中心埋点（配合模块为会员中心增加功能）
- 编程式添加会员菜单规则支持

### 优化
- 默认关闭监听SQL
- 服务端返回302时自动删除前端的用户token
- 系统配置保存时只效验和提交当前页的表单数据
- 优化用户信息显示
- 优化`getTableFieldList`接口
- 统一接口响应数据`key`的命名规范
- 默认不再允许上传pdf格式的文件
- `Token::check`方法增加过期不抛出异常时的逻辑

### 修复/重构
- 修复模块下载安装时解压目录名可能错误的问题
- 文件后缀名大写时无法上传的问题
- 修复关联表名带下划线生成的代码出错
- 修复上传组件一处类型检查错误
- 会员中心的用户名默认不再禁止修改
- 会员修改绑定信息时账户验证通过的token在使用后立即删除
- 自定义排序字段，模型onAfterInsert方法生成错误
- 修复生成三级以上的菜单规则时，无法为非超管分配权限的问题
- 修复可视化CRUD删除字段时可能出现报错的问题
- 去除多余的会员菜单规则
- 模块市场中与官网相关的URL修改
- 修复预览图片宽高较大时超出对话框的问题
- 修复公共搜索只有一个输入框时会触发表单的默认行为的问题
- 其他细节

## v1.1.0-Release
### 新增
- **可视化CRUD新增多种快捷组件，并修复已知问题**
- 模块可以在启用和禁用脚本内备份配置数据和运行文件
- 模块支持向main.ts和App.vue添加代码
- 新增会员修改绑定信息（手机号、邮箱）支持
- 文件图片上传增加隐藏附件选择器的选项
- 远程下拉组件增加 label 格式化函数的属性
- 增加颜色选择器（baInput）
- 完善上传组件的onChange等事件

### 优化
- 优化后台登录页面自适应效果
- 优化首页和会员中心菜单样式
- 优化终端警告信息显示效果
- 优化账户名验证错误时的提示消息
- 详情弹窗可以点击弹窗外部进行关闭
- 禁止管理员向自己的角色组添加其他管理员
- 其他细节...

### 修复/重构
- 修复后台编辑弹窗缩放后显示异常的问题
- 修复在第一个tab右击菜单中关闭全部tab时报错的问题 #10
- 修复远程下拉可能出现已聚焦却无选项的问题
- 修复添加管理员和会员时可能出现表单验证信息的问题
- 修复模块管理中会员登录态过期后不自动注销的问题
- 修复系统配置中的数字输入框编辑可能无法保存的问题
- 修复系统配置中的上传组件从附件选择器中选择附件保存无效的问题
- 增加vue-qr依赖
- 增加忽略Desktop.ini

## v1.0.9-Release
- **新增可视化CRUD**
- 去除原命令行CRUD代码生成功能（已打包为模块，按需下载）
- 添加表单颜色选择器和表格内的颜色渲染方式
- 侧边按钮增加 disabled 判定方法和按钮额外属性
- 增加获取数据表字段的辅助函数
- 增加获取一个目录所有文件的辅助函数
- 后台手机端自适应优化
- 公共搜索输入框可一键清空
- 远程下拉默认值优化
- 优化版本类/扩展类
- 优化树状表格
- `DELETE`请求的body改为query以兼容域名CNAME解析
- 在main.ts导入display.css而不是分散导入
- 修复url带参跳转时表格可能报错的问题
- 修复只添加为路由的菜单规则不能刷新的问题
- 修复验权时可能出现错误的问题
- 修复Linux下删除空文件夹可能失败的问题
- 修复自建模块处于未安装状态时显示异常的问题
- 会员切换登录注册时重置表单项 !70
- 会员切换到注册表单时清理用户名
- 管理员分组的上级分组禁止为自身
- 模块管理用户信息弹窗数据更新
- 本地模块更新日志显示异常的问题

## v1.0.8-Release
- **ThinkPHP发布6.1.0版本安全更新**，修正了序列化漏洞问题和优化多语言判断机制。
- 去除`lodash`依赖改用`lodash-es`（后者同时为`Element plus`的依赖，与框架更契合，包体积更小）
- 修复跨域代理示例的规则错误的问题
- 合并打包css文件、增加分包配置示例
- 完善工具函数注释、优化相关代码
- 模块详情展示效果优化

PS: 框架对`TP`的版本限定为`^6.0.0`，针对tp本次安全更新，git包的开发者可以直接`composer update`，若没更新到`v6.1.0`请更换`composer`源，`BuildAdmin`发新版本主要是为了更新完整包和资源包。

## v1.0.7-Release
- 富文本编辑器通过模块市场按需安装（框架不再内置），以方便选择不同的编辑器
- **增加附件资源库**
- 前台用户登录状态检测优化
- 事件监听优化
- 附件管理优化
- 单元格图片预览弹窗可以通过点击遮罩层关闭
- 自定义表格页码相关优化
- 搜索事件Data的类型定义优化
- 修复特殊类型文件上传时可能被限制的问题
- 优化敏感数据修改监听的逻辑
- 修复 typescript-eslint 依赖可能安装失败的问题
- 优化表单密码验证规则

## v1.0.6-Release
- Table组件增加多个插槽位，提供`el-table-column`支持
- 增加WEB端文件上传扩展文件
- 增加文件上传前的类型与大小检查
- 增加文件单位转字节的函数
- 增加系统配置管理类
- 新增以编程的方式删除依赖的功能
- 新增模块安装时对互斥模块的检测
- 增加多个系统预置事件定义
- 增加发送邮件接口
- 增加发送短信接口
- 增加手机验证账户验证方式
- 增加responseType json 以外类型的处理逻辑
- 增加编程式添加系统配置中的快捷配置入口的方法
- 增加清理浏览器缓存的快捷按钮
- 升级element-plus版本到2.2.17
- 优化表单验证
- 优化表格的单元格渲染
- 优化多处类型定义
- 优化后端数据库字段读取函数
- 优化数据管理中数据表和控制器列表的加载
- 优化控制台页面暗黑模式下的文字颜色
- 优化模块安装时对互斥模块的检测
- 优化上传组件
- WEB端语言包文件无限层级读取
- 表格顶部菜单按钮图标在暗黑模式时的样式优化
- 禁用模块时可以选择保留一些由模块添加的依赖项
- 模块状态不为已安装时不定义AppInit事件
- 资源完整路径处理时加入上传文件cdnurl的判断
- Table组件不再使用事件巴士监听相关事件
- 删除文件不存在的附件记录前额外检查是否是本地存储
- 附件管理删除记录时同时删除文件，并提供友好提示信息
- 去除Table组件的action事件
- 去除TableHeader组件的action事件
- 输入组件帮助信息显示效果优化
- 修复对表格第三次排序时(取消排序时)失效的问题
- 修复部分后台功能缓存设置不生效的问题
- 修复多选远程下拉选择一次面板就收缩和无右侧箭头的问题
- 修复菜单规则管理中图标选择器在窗口关闭后残留的问题
- 修复图标选择器选取图标后无法再次显示的问题
- 模块安装器去除等待热更新步骤
- 修复预设表格页码或单页加载数量无效的问题
- 修复主动添加的系统配置不能删除的问题、格式化代码
- 修复模块依赖冲突检测可能异常的问题
- 修复安装云存储模块后，本地上传模块时被上传到云存储的问题
- 修复用户修改头像时顶栏和侧栏的头像图片可能404的问题
- 修复模块依赖冲突时，模块的启用脚本不执行的问题
- 修复模块安装完成后异常的显示了`模块已安装`的错误弹窗
- 管理员管理和会员管理接口中的敏感信息剔除
- 移除多余的IE相关判断
- 其他优化...

## v1.0.5-Release Preview
- 新增**模块市场**，一键安装某个功能、单页或是纯前端技术栈的学习案例项目等等，随时随地为系统添砖加瓦，系统能够自动维护`package.json`和`composer.json`并通过内置终端自动完成模块所需依赖的安装。
- 新增前后台**暗黑模式**支持
- 安装器不再要求数据表前缀必填、安装验证逻辑优化
- 终端原`popen`实现改为`proc_open`
- 重新实现图片文件上传组件
- 单元格渲染为 tags 时支持effect、size等属性
- url的点击事件增加当前行数据的参数
- 为管理员管理功能开启数据限制
- 后台Iframe相关多个细节完善
- 生成代码文件中的缩进改为空格而不是tab
- 访问后端接口时，不再必须通过index.php入口文件
- 放行所有options请求
- 修复顶部菜单columnDisplay和comSearch同时不存在时，仍然会残留一个div边框的问题
- 修复菜单规则管理中无法直接开关规则的问题
- 修复单选远程下拉清理输入框值后无法再读取全部远程数据的问题
- 修复axios封装在showCodeMessage=false时请求无后续处理的问题
- 修复表字段名称为length时CRUD生成语言包报错
- 修复删除菜单规则时未同时删除子级菜单的问题
- 修复角色组的资料可被越权修改的问题
- 修复触发到API请求节流时报错为跨域的问题
- 修复表格顶部下拉菜单复选框和按钮组占位
- 修复已上传文件丢失后，无法再次上传的问题
- 修复有默认值的情况多文件同时上传时文件列表错乱的问题
- 修复隐藏菜单情况刷新页面再展开菜单会导致顶部tab异常的问题
- 修复后台菜单折叠状态刷新后丢失的问题
- 修复管理员昵称过长时首次登录昵称被换行的问题
- 修复登录页面管理员头像位置自适应异常的问题
- 其他细节...

## v1.0.3-Release
- 完善英文语言包
- 公共搜索增加远程下拉组件支持
- 增加数据权限控制支持：不同管理员只可以查看有权数据行 的权限控制功能
- 自动识别表主键并添加到生成的模型属性
- 后台终端按钮只为超级管理员显示
- 关联表指定远程select下拉字段
- 增加表格快速搜索字段是否存在的检测
- 增加以type为后缀的enum等类型字段可被生成为单选框
- 站点系统配置缓存支持
- 增加会员中心开关
- 会员注册时通过API获取可用的验证方式、会员注册验证邮件实现
- 完善会员规则管理
- 表格公共搜索->对开关组件状态的搜索优化
- 公共搜索显示状态可通过baTable实例控制
- 验证码类支持到php8.1
- 去除file_list后缀的字段生成为多文件上传组件(与下拉组件后缀存在冲突)
- 优化角色组权限分配
- 优化默认管理员分组拥有的权限节点
- 数据回收和敏感数据规则中，不再使用带前缀的表全名
- 安装器`npm install`失败自动重试一次
- 安装器增加检测当前端口是否是8000
- 安装器完成页面增加重新安装按钮 (只清理缓存,不会删除install.lock)
- 修复敏感数据规则管理中删除敏感字段时的显示异常问题
- 修复表格时间字段未提供值时显示为当前时间的问题
- 修复管理员个人资料表单中的签名无法被重置的问题
- 修复后端默认应用不存在的问题
- 修复字段类型为char(1)时，生成的单选框无字典数据
- 修复数据表主键不为ID时编辑表单无法保存、表格无法排序等问题
- 修复顶栏标签全屏时，取消全屏的按钮会遮挡表格顶部操作按钮的问题
- 修复前后台路由规则名称重复时可能导致错误跳转问题
- 修复手机号验证正则无法识别部分已知号码的问题
- 修复系统配置中的禁止访问IP和时区配置项无效的问题
- 修复系统配置中富文本编辑器层级过高和无法编辑的问题
- 修复系统配置中时间和城市类型的输入组件无法正常录入值的问题
- 修复数据表没有注释时不生成菜单规则的问题
- 修复表格右侧无buttons，且要初始化排序时会报错的问题
- 修复单元格渲染为tag时值为0等无法显示的问题
- 修复images字段名称后缀不能生成为图片上传组件的问题
- 修复管理员日志权限控制不完善的问题
- 修复管理员可通过后台使自己部分权限丢失的问题
- 修复管理员分组被禁用后还可以被远程select选择的问题
- 修复删除管理员时没有同时删除管理员的分组数据的问题
- 修复远程下拉搜索结果无法选中的问题、同时优化下拉选项面板显示逻辑
- 修复菜单规则和会员分组被禁用后在远程select中依然可以选择的问题
- 修复重复安装系统时.env-example被多次写入数据库资料的问题
- 修复数据安全监听中表不存在时的日志记录异常
- 其他细节优化

## v1.0.2-Release
- **增加前台会员中心**
- 安装器增加NPM源自动设置选项
- CRUD：增加tinyint(1)类型的字段在符合条件下自动生成为单选框
- baInput：单选/复选框/下拉框默认值传递数字支持
- baInput：优化年份选择器
- baInput：文件上传组件增加预览响应
- web端布局(layouts)内的目录结构调整
- 增加跨域代理配置示例，提供给有需要的小伙伴（感谢@ttdms）
- 增加邮件发送类、增加phpMailer依赖、系统邮件配置增加测试邮件发送功能
- 后台右侧菜单增加清理缓存按钮
- 会员余额以分为单位保存到数据库，并在模型层做转换处理
- 附件管理增加上传会员字段
- 优化富文本编辑器滚动条样式、通用弹窗表单增加圆角
- 更新wangeditor依赖版本到5.1.1
- 增加会员资料的状态商店、优化后台登录状态判断逻辑
- 表格开关类型字段的公共搜索使用下拉框渲染
- 重构了站点首页
- 更新font-awesome的资源地址到国内CDN
- 去除build:online命令，使用build代替
- 修复关闭管理员登录验证码后，登录任然报错验证码不存在的问题
- 修复富文本编辑器上传文件时提示未配置上传URL的问题
- 修复表格中的tag和url在无值时任然显示组件的问题
- 修复侧边菜单栏的非激活菜单项的图标颜色不符合直觉的问题
- 修复CRUD生成的代码在添加数据时权重字段无效的问题
- 修复部分日志记录没有标题的问题
- 修复已在后台或会员中心再跳转到模块首页时会卡在loading页面的问题
- 修复系统配置编辑时提示变量名不能为空的问题
- 修复后台表格右侧字段下拉没有高度限定的问题、修复一处样式缺失
- 修复管理员注销时偶尔需要权限的问题
- 修复默认的数据回收规则配置不完整的问题
- 修复表格顶部的批量操作按钮在未选择数据时依然可点击的问题
- 修复表格内tag在公共搜索中被渲染为下拉框的问题
- 修复管理员登录页面编译后可能存在的username未定义报错

## v1.0.1-Release
- 增加终端配置功能
- 终端增加是否运行于安装服务下的检测
- FormItem增加额外的块级输入提示选项
- 优化管理分组权限节点选择时的样式
- 语言包整理
- 额外暴露i18n实例，实现在非setup中使用语言翻译
- 新增站点配置状态store
- 修复bug、完善README

## v1.0.0-beta
**公共测试版本**
- 内置WEB终端
- 一键CRUD
- Pinia
- 可视化配置+动态加载路由
- 细粒度权限控制
- 数据修改保护、数据全局回收
- ...