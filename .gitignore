# 通过 Git 部署项目至线上时建议删除的忽略规则
/vendor
/modules
/public/*.lock
/public/index.html
/public/assets

# 通过 Git 部署项目至线上时可以考虑删除的忽略规则
/public/storage/*
composer.lock
pnpm-lock.yaml
package-lock.json
yarn.lock

# common
/nbproject
/runtime/*
/install
node_modules
dist
dist-ssr
.DS_Store
/.env
Desktop.ini

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
!/web/.vscode

# Other
*.css.map
*.local
!.gitkeep
.svn